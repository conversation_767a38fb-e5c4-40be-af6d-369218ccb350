from core.contracts.retrieval import VectorRecord
from core.stores.in_memory_vector import InMemoryVectorStore
from tests.realistic_data import get_vector_data


def test_vector_store_add_and_search(benchmarker):
    store = InMemoryVectorStore()
    # Use realistic vector data
    vector_data = get_vector_data(3)
    records = [
        VectorRecord(id=item["id"], vector=item["vector"], metadata=item["metadata"])
        for item in vector_data
    ]
    store.add(records)

    # Search with a query vector similar to the first record
    query_vector = [0.8, 0.6, 0.2, 0.9, 0.1, 0.7, 0.4, 0.3]
    res = store.search(query_vector, top_k=2)
    assert len(res) == 2
    assert res[0].id in {item["id"] for item in vector_data}

    metrics = benchmarker(lambda: store.search(query_vector, top_k=3), iterations=200)
    assert metrics["p95_ms"] < 1.5
