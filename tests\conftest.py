import asyncio
import inspect
import json
import os
import statistics
import sys
import time
from pathlib import Path
from typing import Any, Awaitable, Callable, Dict, Iterable

import pytest

# Add the project root to Python path for imports
project_root = Path(__file__).parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

_BENCH_RECORDS: list[dict] = []


@pytest.fixture
def benchmarker(request):
    """Benchmark helper that also records metrics for a session report.

    Usage:
        metrics = benchmarker(lambda: func(...), iterations=50, name="group.testcase")
    """

    def _bench(
        callable_fn: Callable[[], Any], *, iterations: int = 20, name: str | None = None
    ) -> Dict[str, float]:
        times: Iterable[float] = []
        results = []
        t_start = time.perf_counter()
        for _ in range(iterations):
            t0 = time.perf_counter()
            res = callable_fn()
            results.append(res)
            dt = (time.perf_counter() - t0) * 1000.0
            times.append(dt)
        total_ms = (time.perf_counter() - t_start) * 1000.0
        sorted_times = sorted(times)
        p50 = statistics.median(sorted_times)
        p95 = sorted_times[int(0.95 * (len(sorted_times) - 1))]
        p99 = sorted_times[int(0.99 * (len(sorted_times) - 1))] if len(sorted_times) > 1 else sorted_times[-1]
        metrics = {
            "test": name or request.node.nodeid,
            "iterations": float(iterations),
            "total_ms": float(total_ms),
            "avg_ms": float(statistics.mean(sorted_times)),
            "p50_ms": float(p50),
            "p95_ms": float(p95),
            "p99_ms": float(p99),
            "min_ms": float(sorted_times[0]),
            "max_ms": float(sorted_times[-1]),
            "qps": float(iterations / (total_ms / 1000.0)) if total_ms > 0 else float('inf'),
        }
        _BENCH_RECORDS.append(metrics)
        return metrics

    return _bench


@pytest.fixture
def async_bench(request):
    """Benchmark helper for async callables with optional concurrency.

    Usage: await async_bench(lambda: coro(), iterations=N, concurrency=C)
    """

    async def _bench_async(
        async_callable_fn: Callable[[], Awaitable[Any]], *, iterations: int = 20, concurrency: int = 1, name: str | None = None
    ) -> Dict[str, float]:
        if concurrency < 1:
            concurrency = 1
        times: list[float] = []

        async def _one():
            t0 = time.perf_counter()
            _ = await async_callable_fn()
            times.append((time.perf_counter() - t0) * 1000.0)

        t_start = time.perf_counter()
        remaining = iterations
        while remaining > 0:
            batch = min(concurrency, remaining)
            await asyncio.gather(*[_one() for _ in range(batch)])
            remaining -= batch
        total_ms = (time.perf_counter() - t_start) * 1000.0

        sorted_times = sorted(times)
        p50 = statistics.median(sorted_times)
        p95 = sorted_times[int(0.95 * (len(sorted_times) - 1))]
        p99 = sorted_times[int(0.99 * (len(sorted_times) - 1))] if len(sorted_times) > 1 else sorted_times[-1]
        metrics = {
            "test": name or request.node.nodeid,
            "iterations": float(iterations),
            "concurrency": float(concurrency),
            "total_ms": float(total_ms),
            "avg_ms": float(statistics.mean(sorted_times)),
            "p50_ms": float(p50),
            "p95_ms": float(p95),
            "p99_ms": float(p99),
            "min_ms": float(sorted_times[0]),
            "max_ms": float(sorted_times[-1]),
            "qps": float(iterations / (total_ms / 1000.0)) if total_ms > 0 else float('inf'),
        }
        _BENCH_RECORDS.append(metrics)
        return metrics

    return _bench_async


def pytest_sessionfinish(session, exitstatus):
    out_path = os.environ.get("MGAIF_BENCH_OUT", str(Path(".benchmarks.json").resolve()))
    try:
        with open(out_path, "w", encoding="utf-8") as f:
            json.dump({"results": _BENCH_RECORDS}, f, indent=2)
        print(f"[bench] Benchmark report written to {out_path}")
    except Exception as e:
        # Do not fail tests due to reporting errors
        print(f"[bench] Failed to write benchmark report: {e}")


# Use default pytest-asyncio event loop to avoid deprecation warning
