{"name": "mgaif-webapp", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"antd": "5.16.2", "axios": "1.7.2", "yaml": "2.5.0", "react": "18.3.1", "react-dom": "18.3.1", "react-router-dom": "6.26.2"}, "devDependencies": {"@types/node": "20.14.12", "@types/react": "18.3.3", "@types/react-dom": "18.3.0", "@vitejs/plugin-react": "4.3.1", "@testing-library/jest-dom": "6.4.8", "@testing-library/react": "16.0.0", "@testing-library/user-event": "14.5.2", "jsdom": "24.1.0", "typescript": "5.5.4", "vite": "5.4.1", "vitest": "2.0.5", "@vitest/coverage-v8": "2.0.5"}}