import React, { useState } from 'react';
import { Card, Collapse, Typography, Input, Space, Tooltip } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useWorkflowStore } from '../../stores/workflowStore';
import type { NodeTemplate } from '../../types/workflow';

const { Panel } = Collapse;

interface NodePaletteProps {
  onNodeDragStart?: (template: NodeTemplate) => void;
}

export const NodePalette: React.FC<NodePaletteProps> = ({ onNodeDragStart }) => {
  const { nodeTemplates } = useWorkflowStore();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategories, setActiveCategories] = useState<string[]>([
    'input',
    'processing',
    'tools',
    'logic',
    'output',
  ]);

  // Filter templates based on search term
  const filteredTemplates = nodeTemplates.filter((template) =>
    template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    template.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group templates by category
  const templatesByCategory = filteredTemplates.reduce((acc, template) => {
    if (!acc[template.category]) {
      acc[template.category] = [];
    }
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, NodeTemplate[]>);

  const handleDragStart = (event: React.DragEvent, template: NodeTemplate) => {
    event.dataTransfer.setData('application/reactflow', JSON.stringify(template));
    event.dataTransfer.effectAllowed = 'move';
    onNodeDragStart?.(template);
  };

  const categoryLabels = {
    input: 'Input',
    processing: 'Processing',
    tools: 'Tools',
    logic: 'Logic',
    output: 'Output',
  };

  const categoryIcons = {
    input: '📝',
    processing: '⚙️',
    tools: '🔧',
    logic: '🧠',
    output: '📤',
  };

  return (
    <Card
      title="Node Palette"
      size="small"
      style={{ height: '100%', overflow: 'hidden' }}
      bodyStyle={{ padding: '8px', height: 'calc(100% - 57px)', overflow: 'auto' }}
    >
      {/* Search */}
      <Input
        placeholder="Search nodes..."
        prefix={<SearchOutlined />}
        value={searchTerm}
        onChange={(e) => setSearchTerm(e.target.value)}
        style={{ marginBottom: '12px' }}
        size="small"
      />

      {/* Node Categories */}
      <Collapse
        activeKey={activeCategories}
        onChange={(keys) => setActiveCategories(keys as string[])}
        ghost
        size="small"
      >
        {Object.entries(templatesByCategory).map(([category, templates]) => (
          <Panel
            key={category}
            header={
              <Space>
                <span>{categoryIcons[category as keyof typeof categoryIcons]}</span>
                <Typography.Text strong>
                  {categoryLabels[category as keyof typeof categoryLabels]}
                </Typography.Text>
                <Typography.Text type="secondary">({templates.length})</Typography.Text>
              </Space>
            }
          >
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              {templates.map((template) => (
                <Tooltip
                  key={template.id}
                  title={template.description}
                  placement="right"
                >
                  <Card
                    size="small"
                    hoverable
                    draggable
                    onDragStart={(e) => handleDragStart(e, template)}
                    style={{
                      cursor: 'grab',
                      border: '1px solid #d9d9d9',
                      borderRadius: '6px',
                    }}
                    bodyStyle={{ padding: '8px' }}
                  >
                    <Space>
                      <span style={{ fontSize: '16px' }}>{template.icon}</span>
                      <div>
                        <Typography.Text strong style={{ fontSize: '12px' }}>
                          {template.name}
                        </Typography.Text>
                        <Typography.Text
                          type="secondary"
                          style={{
                            fontSize: '10px',
                            display: 'block',
                            lineHeight: '1.2',
                          }}
                        >
                          {template.description.length > 40
                            ? `${template.description.substring(0, 40)}...`
                            : template.description}
                        </Typography.Text>
                      </div>
                    </Space>
                  </Card>
                </Tooltip>
              ))}
            </div>
          </Panel>
        ))}
      </Collapse>

      {/* Empty State */}
      {filteredTemplates.length === 0 && (
        <div
          style={{
            textAlign: 'center',
            padding: '20px',
            color: '#999',
          }}
        >
          <Typography.Text type="secondary">
            No nodes found matching "{searchTerm}"
          </Typography.Text>
        </div>
      )}
    </Card>
  );
};
