import { render, screen } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import App from '../App'

function renderApp() {
  return render(
    <BrowserRouter>
      <App />
    </BrowserRouter>
  )
}

test('renders app shell with title and menu', () => {
  renderApp()
  expect(screen.getByText(/Modular GenAI Framework Console/i)).toBeInTheDocument()
  expect(screen.getByText(/Dashboard/i)).toBeInTheDocument()
  expect(screen.getByText(/Workflows/i)).toBeInTheDocument()
  expect(screen.getByText(/RAG Stores/i)).toBeInTheDocument()
  expect(screen.getByText(/LLMs/i)).toBeInTheDocument()
  expect(screen.getByText(/Settings/i)).toBeInTheDocument()
})
