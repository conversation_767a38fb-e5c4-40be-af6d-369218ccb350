"""Basic tests for evaluation framework components that are implemented.

This module tests the core evaluation components that are currently available.
"""

import pytest
from datetime import datetime

from core.evaluation.base import Metric, EvaluationResult, EvaluationError
from core.evaluation.metrics import BLEUMetric, ROUGEMetric, BERTScoreMetric, AccuracyMetric


class MockMetric(Metric):
    """Mock metric for testing."""
    
    def __init__(self, name="mock_metric", score=0.8):
        super().__init__(name, f"Mock metric: {name}")
        self._score = score
    
    def compute(self, predictions, references=None, **kwargs):
        return EvaluationResult(
            metric_name=self.name,
            score=self._score,
            details={"mock": True, "predictions_count": len(predictions)},
            metadata={"test": True},
            timestamp=datetime.now()
        )


class TestEvaluationResult:
    """Test EvaluationResult data structure."""
    
    def test_evaluation_result_creation(self):
        """Test basic evaluation result creation."""
        result = EvaluationResult(
            metric_name="test_metric",
            score=0.85,
            details={"precision": 0.9, "recall": 0.8},
            metadata={"model": "test-model"},
            timestamp=datetime.now()
        )
        
        assert result.metric_name == "test_metric"
        assert result.score == 0.85
        assert result.details["precision"] == 0.9
        assert result.details["recall"] == 0.8
        assert result.metadata["model"] == "test-model"
        assert isinstance(result.timestamp, datetime)
    
    def test_evaluation_result_to_dict(self):
        """Test evaluation result serialization."""
        timestamp = datetime.now()
        result = EvaluationResult(
            metric_name="test_metric",
            score=0.75,
            details={"key": "value"},
            metadata={"meta": "data"},
            timestamp=timestamp
        )
        
        result_dict = result.to_dict()
        
        assert result_dict["metric_name"] == "test_metric"
        assert result_dict["score"] == 0.75
        assert result_dict["details"] == {"key": "value"}
        assert result_dict["metadata"] == {"meta": "data"}
        assert result_dict["timestamp"] == timestamp.isoformat()


class TestEvaluationError:
    """Test EvaluationError exception."""
    
    def test_evaluation_error_creation(self):
        """Test basic evaluation error creation."""
        error = EvaluationError(
            message="Test error",
            metric_name="test_metric",
            details={"error_code": 123}
        )
        
        assert str(error) == "Test error"
        assert error.metric_name == "test_metric"
        assert error.details["error_code"] == 123
    
    def test_evaluation_error_minimal(self):
        """Test evaluation error with minimal parameters."""
        error = EvaluationError("Simple error")
        
        assert str(error) == "Simple error"
        assert error.metric_name is None
        assert error.details == {}


class TestMetricBase:
    """Test Metric base class functionality."""
    
    def test_mock_metric_creation(self):
        """Test mock metric creation."""
        metric = MockMetric("test_metric", 0.9)
        
        assert metric.name == "test_metric"
        assert "Mock metric: test_metric" in metric.description
    
    def test_mock_metric_computation(self):
        """Test mock metric computation."""
        metric = MockMetric("test_metric", 0.85)
        
        predictions = ["pred1", "pred2", "pred3"]
        references = ["ref1", "ref2", "ref3"]
        
        result = metric.compute(predictions, references)
        
        assert isinstance(result, EvaluationResult)
        assert result.metric_name == "test_metric"
        assert result.score == 0.85
        assert result.details["mock"] is True
        assert result.details["predictions_count"] == 3
        assert result.metadata["test"] is True
        assert isinstance(result.timestamp, datetime)


class TestBLEUMetricBasic:
    """Basic tests for BLEU metric."""
    
    def test_bleu_metric_creation(self):
        """Test BLEU metric creation."""
        bleu = BLEUMetric(max_order=4, smooth=True)
        
        assert bleu.name == "bleu"
        assert bleu.max_order == 4
        assert bleu.smooth is True
        assert "n-gram overlap" in bleu.description.lower()
    
    def test_bleu_perfect_match(self):
        """Test BLEU with perfect match."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert isinstance(result, EvaluationResult)
        assert result.metric_name == "bleu"
        assert result.score == 1.0  # Perfect match
        assert "precision" in result.details
        assert "brevity_penalty" in result.details
    
    def test_bleu_no_match(self):
        """Test BLEU with no match."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = ["completely different text"]
        references = ["the cat sat on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert result.score == 0.0  # No overlap


class TestROUGEMetricBasic:
    """Basic tests for ROUGE metric."""
    
    def test_rouge_metric_creation(self):
        """Test ROUGE metric creation."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        assert rouge.name == "rouge"
        assert rouge.variant == "rouge_1"
        assert "recall" in rouge.description.lower()
    
    def test_rouge_perfect_match(self):
        """Test ROUGE with perfect match."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        result = rouge.compute(predictions, references)
        
        assert result.score == 1.0  # Perfect match
        assert "precision" in result.details
        assert "recall" in result.details
        assert "f1" in result.details


class TestAccuracyMetricBasic:
    """Basic tests for Accuracy metric."""
    
    def test_accuracy_metric_creation(self):
        """Test accuracy metric creation."""
        accuracy = AccuracyMetric(case_sensitive=True)
        
        assert accuracy.name == "accuracy"
        assert accuracy.case_sensitive is True
        assert "exact match" in accuracy.description.lower()
    
    def test_accuracy_perfect_match(self):
        """Test accuracy with perfect matches."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B", "C", "D"]
        references = ["A", "B", "C", "D"]
        
        result = accuracy.compute(predictions, references)
        
        assert result.score == 1.0  # 100% accuracy
        assert result.details["correct"] == 4
        assert result.details["total"] == 4
    
    def test_accuracy_partial_match(self):
        """Test accuracy with partial matches."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B", "C", "D"]
        references = ["A", "B", "X", "D"]
        
        result = accuracy.compute(predictions, references)
        
        assert result.score == 0.75  # 3/4 correct
        assert result.details["correct"] == 3
        assert result.details["total"] == 4


class TestBERTScoreMetricBasic:
    """Basic tests for BERTScore metric."""
    
    def test_bert_score_creation(self):
        """Test BERTScore metric creation."""
        bert_score = BERTScoreMetric()
        
        assert bert_score.name == "bert_score"
        assert "contextual embeddings" in bert_score.description.lower()
    
    def test_bert_score_computation(self):
        """Test BERTScore computation (simplified version)."""
        bert_score = BERTScoreMetric()
        
        predictions = ["the cat is sleeping"]
        references = ["a cat is resting"]
        
        result = bert_score.compute(predictions, references)
        
        # This is a simplified implementation, so we just check basic structure
        assert isinstance(result, EvaluationResult)
        assert result.metric_name == "bert_score"
        assert 0.0 <= result.score <= 1.0
        assert "note" in result.details  # Should mention it's simplified


class TestMetricIntegration:
    """Test integration scenarios for available metrics."""
    
    def test_metric_result_structure(self):
        """Test that all metrics return properly structured results."""
        metrics = [
            BLEUMetric(),
            ROUGEMetric(variant="rouge_1"),
            AccuracyMetric(),
            BERTScoreMetric()
        ]
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        for metric in metrics:
            result = metric.compute(predictions, references)
            
            # Verify result structure
            assert isinstance(result, EvaluationResult)
            assert result.metric_name == metric.name
            assert isinstance(result.score, (int, float))
            assert 0.0 <= result.score <= 1.0
            assert isinstance(result.details, dict)
            assert isinstance(result.timestamp, datetime)
    
    def test_metric_error_handling(self):
        """Test metric error handling."""
        accuracy = AccuracyMetric()
        
        # Test with mismatched lengths
        with pytest.raises(EvaluationError) as exc_info:
            accuracy.compute(["A"], ["A", "B"])
        
        assert "length mismatch" in str(exc_info.value).lower()
        assert exc_info.value.metric_name == "accuracy"
    
    def test_metric_empty_inputs(self):
        """Test metrics with empty inputs."""
        bleu = BLEUMetric()
        
        predictions = []
        references = []
        
        with pytest.raises(EvaluationError):
            bleu.compute(predictions, references)
    
    @pytest.mark.parametrize("metric_class,kwargs", [
        (BLEUMetric, {"max_order": 2}),
        (ROUGEMetric, {"variant": "rouge_1"}),
        (AccuracyMetric, {"case_sensitive": False}),
        (BERTScoreMetric, {})
    ])
    def test_metric_consistency(self, metric_class, kwargs):
        """Test that metrics produce consistent results."""
        metric = metric_class(**kwargs)
        
        predictions = ["the quick brown fox"]
        references = ["the quick brown fox"]
        
        # Run multiple times
        results = []
        for _ in range(3):
            result = metric.compute(predictions, references)
            results.append(result.score)
        
        # Results should be consistent
        assert all(score == results[0] for score in results)


@pytest.mark.asyncio
async def test_basic_async_functionality():
    """Test basic async functionality with available components."""
    # Simple async test to verify async support works
    metric = MockMetric("async_test", 0.95)
    
    predictions = ["async test"]
    references = ["async test"]
    
    # Test that we can call the metric in an async context
    result = metric.compute(predictions, references)
    
    assert result.score == 0.95
    assert result.metric_name == "async_test"
