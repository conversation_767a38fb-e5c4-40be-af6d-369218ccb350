"""Benchmark systems for model evaluation.

This module implements various benchmarks for evaluating LLM and RAG system
performance across different tasks and domains.

The benchmark system provides:
- Standard academic benchmarks (HellaSwag, MMLU, etc.)
- Custom domain-specific benchmarks
- Automated evaluation workflows
- Performance comparison and ranking
- Statistical significance testing

Key Components:
- Benchmark: Base benchmark interface
- HellaSwagBenchmark: Commonsense reasoning benchmark
- MMLUBenchmark: Massive multitask language understanding
- RAGBenchmark: RAG-specific evaluation benchmark

Example:
    >>> from core.evaluation import HellaSwagBenchmark, Evaluator
    >>> benchmark = HellaSwagBenchmark()
    >>> evaluator = Evaluator()
    >>> evaluator.add_benchmark(benchmark)
    >>> 
    >>> results = await evaluator.evaluate_model(model, test_data)
    >>> print(f"HellaSwag Score: {results.benchmark_scores['hellaswag']}")
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple, Union
from dataclasses import dataclass, field
from datetime import datetime
import json
import random

from .base import EvaluationResult, EvaluationError

logger = logging.getLogger(__name__)


@dataclass
class BenchmarkItem:
    """Single item in a benchmark dataset."""
    id: str
    question: str
    choices: List[str]
    correct_answer: Union[str, int]
    metadata: Dict[str, Any] = field(default_factory=dict)
    domain: str = "general"
    difficulty: str = "medium"


@dataclass
class BenchmarkResult:
    """Result from running a benchmark."""
    benchmark_name: str
    score: float
    total_items: int
    correct_items: int
    accuracy: float
    per_domain_scores: Dict[str, float] = field(default_factory=dict)
    per_difficulty_scores: Dict[str, float] = field(default_factory=dict)
    detailed_results: List[Dict[str, Any]] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


class Benchmark(ABC):
    """Base class for all benchmarks."""
    
    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description
        self.items: List[BenchmarkItem] = []
        self.loaded = False
    
    @abstractmethod
    async def load_dataset(self) -> None:
        """Load benchmark dataset."""
        pass
    
    @abstractmethod
    async def evaluate_item(self, item: BenchmarkItem, model_response: str) -> bool:
        """Evaluate a single benchmark item."""
        pass
    
    async def run_benchmark(self, model, sample_size: Optional[int] = None) -> BenchmarkResult:
        """Run the complete benchmark on a model."""
        if not self.loaded:
            await self.load_dataset()
        
        if not self.items:
            raise EvaluationError(f"No items loaded for benchmark {self.name}")
        
        # Sample items if requested
        items_to_evaluate = self.items
        if sample_size and sample_size < len(self.items):
            items_to_evaluate = random.sample(self.items, sample_size)
        
        logger.info(f"Running {self.name} benchmark on {len(items_to_evaluate)} items")
        
        correct_count = 0
        detailed_results = []
        domain_stats = {}
        difficulty_stats = {}
        
        for item in items_to_evaluate:
            try:
                # Get model response
                model_response = await self._get_model_response(model, item)
                
                # Evaluate response
                is_correct = await self.evaluate_item(item, model_response)
                
                if is_correct:
                    correct_count += 1
                
                # Track domain and difficulty stats
                domain = item.domain
                difficulty = item.difficulty
                
                if domain not in domain_stats:
                    domain_stats[domain] = {"correct": 0, "total": 0}
                if difficulty not in difficulty_stats:
                    difficulty_stats[difficulty] = {"correct": 0, "total": 0}
                
                domain_stats[domain]["total"] += 1
                difficulty_stats[difficulty]["total"] += 1
                
                if is_correct:
                    domain_stats[domain]["correct"] += 1
                    difficulty_stats[difficulty]["correct"] += 1
                
                # Store detailed result
                detailed_results.append({
                    "item_id": item.id,
                    "question": item.question,
                    "model_response": model_response,
                    "correct_answer": item.correct_answer,
                    "is_correct": is_correct,
                    "domain": domain,
                    "difficulty": difficulty
                })
                
            except Exception as e:
                logger.error(f"Error evaluating item {item.id}: {e}")
                detailed_results.append({
                    "item_id": item.id,
                    "error": str(e),
                    "is_correct": False
                })
        
        # Calculate scores
        accuracy = correct_count / len(items_to_evaluate) if items_to_evaluate else 0.0
        
        per_domain_scores = {
            domain: stats["correct"] / stats["total"] if stats["total"] > 0 else 0.0
            for domain, stats in domain_stats.items()
        }
        
        per_difficulty_scores = {
            difficulty: stats["correct"] / stats["total"] if stats["total"] > 0 else 0.0
            for difficulty, stats in difficulty_stats.items()
        }
        
        result = BenchmarkResult(
            benchmark_name=self.name,
            score=accuracy * 100,  # Convert to percentage
            total_items=len(items_to_evaluate),
            correct_items=correct_count,
            accuracy=accuracy,
            per_domain_scores=per_domain_scores,
            per_difficulty_scores=per_difficulty_scores,
            detailed_results=detailed_results,
            metadata={
                "description": self.description,
                "sample_size": sample_size,
                "total_available_items": len(self.items)
            }
        )
        
        logger.info(f"{self.name} benchmark completed: {accuracy:.3f} accuracy ({correct_count}/{len(items_to_evaluate)})")
        return result
    
    async def _get_model_response(self, model, item: BenchmarkItem) -> str:
        """Get response from model for a benchmark item."""
        try:
            # Format the question based on benchmark type
            prompt = self._format_prompt(item)
            
            # Get model response (this would depend on the model interface)
            if hasattr(model, 'chat'):
                from core.contracts.llm import ChatMessage, ChatCompletionRequest
                request = ChatCompletionRequest(
                    messages=[ChatMessage(role="user", content=prompt)],
                    temperature=0.0,
                    max_tokens=100
                )
                response = await model.chat(request)
                return response.choices[0].message.content.strip()
            else:
                # Fallback for other model interfaces
                return await model.generate(prompt)
                
        except Exception as e:
            logger.error(f"Failed to get model response: {e}")
            return ""
    
    def _format_prompt(self, item: BenchmarkItem) -> str:
        """Format prompt for the benchmark item."""
        if item.choices:
            choices_text = "\n".join([f"{chr(65+i)}. {choice}" for i, choice in enumerate(item.choices)])
            return f"{item.question}\n\n{choices_text}\n\nAnswer:"
        else:
            return f"{item.question}\n\nAnswer:"


class HellaSwagBenchmark(Benchmark):
    """HellaSwag commonsense reasoning benchmark."""
    
    def __init__(self):
        super().__init__(
            name="HellaSwag",
            description="Commonsense reasoning benchmark for sentence completion"
        )
    
    async def load_dataset(self) -> None:
        """Load HellaSwag dataset."""
        # In a real implementation, this would load from the actual dataset
        # For now, we'll create some sample items
        sample_items = [
            BenchmarkItem(
                id="hellaswag_001",
                question="A woman is outside with a bucket and a dog. The dog is running around trying to avoid getting wet. She",
                choices=[
                    "rinses the bucket off with soap and blow dries the dog.",
                    "uses the hose to keep the dog from getting wet.",
                    "gets the dog wet, then it runs away.",
                    "continues to try to wash the dog as the dog runs around."
                ],
                correct_answer=3,
                domain="commonsense",
                difficulty="medium"
            ),
            BenchmarkItem(
                id="hellaswag_002", 
                question="A man is holding a rubik's cube. He",
                choices=[
                    "solves it in under 30 seconds.",
                    "throws it at the wall.",
                    "starts turning the pieces to solve it.",
                    "puts it down and walks away."
                ],
                correct_answer=2,
                domain="commonsense",
                difficulty="easy"
            ),
            # Add more sample items...
        ]
        
        self.items = sample_items
        self.loaded = True
        logger.info(f"Loaded {len(self.items)} HellaSwag items")
    
    async def evaluate_item(self, item: BenchmarkItem, model_response: str) -> bool:
        """Evaluate HellaSwag item response."""
        # Extract the choice from model response
        model_response = model_response.strip().upper()
        
        # Try to extract letter choice (A, B, C, D)
        if len(model_response) == 1 and model_response in "ABCD":
            choice_index = ord(model_response) - ord('A')
        else:
            # Try to extract number choice (0, 1, 2, 3)
            try:
                choice_index = int(model_response)
            except ValueError:
                # Try to find the choice in the response text
                choice_index = -1
                for i, choice in enumerate(item.choices):
                    if choice.lower() in model_response.lower():
                        choice_index = i
                        break
        
        return choice_index == item.correct_answer


class MMLUBenchmark(Benchmark):
    """Massive Multitask Language Understanding benchmark."""
    
    def __init__(self, subjects: Optional[List[str]] = None):
        super().__init__(
            name="MMLU",
            description="Massive multitask language understanding across 57 subjects"
        )
        self.subjects = subjects or ["mathematics", "history", "science", "literature"]
    
    async def load_dataset(self) -> None:
        """Load MMLU dataset."""
        # Sample MMLU-style items across different subjects
        sample_items = [
            BenchmarkItem(
                id="mmlu_math_001",
                question="What is the derivative of x^2 + 3x + 2?",
                choices=["2x + 3", "x^2 + 3", "2x + 2", "x + 3"],
                correct_answer=0,
                domain="mathematics",
                difficulty="medium"
            ),
            BenchmarkItem(
                id="mmlu_history_001",
                question="In which year did World War II end?",
                choices=["1944", "1945", "1946", "1947"],
                correct_answer=1,
                domain="history",
                difficulty="easy"
            ),
            BenchmarkItem(
                id="mmlu_science_001",
                question="What is the chemical symbol for gold?",
                choices=["Go", "Gd", "Au", "Ag"],
                correct_answer=2,
                domain="science",
                difficulty="easy"
            ),
            # Add more sample items...
        ]
        
        # Filter by requested subjects
        if self.subjects:
            sample_items = [item for item in sample_items if item.domain in self.subjects]
        
        self.items = sample_items
        self.loaded = True
        logger.info(f"Loaded {len(self.items)} MMLU items across {len(self.subjects)} subjects")
    
    async def evaluate_item(self, item: BenchmarkItem, model_response: str) -> bool:
        """Evaluate MMLU item response."""
        # Similar to HellaSwag evaluation
        model_response = model_response.strip().upper()
        
        if len(model_response) == 1 and model_response in "ABCD":
            choice_index = ord(model_response) - ord('A')
            return choice_index == item.correct_answer
        
        try:
            choice_index = int(model_response)
            return choice_index == item.correct_answer
        except ValueError:
            return False


class RAGBenchmark(Benchmark):
    """RAG-specific evaluation benchmark."""
    
    def __init__(self, rag_pipeline=None):
        super().__init__(
            name="RAG-Eval",
            description="Retrieval-Augmented Generation evaluation benchmark"
        )
        self.rag_pipeline = rag_pipeline
    
    async def load_dataset(self) -> None:
        """Load RAG evaluation dataset."""
        # Sample RAG evaluation items
        sample_items = [
            BenchmarkItem(
                id="rag_001",
                question="What are the main applications of artificial intelligence in healthcare?",
                choices=[],  # Open-ended question
                correct_answer="Medical diagnosis, drug discovery, personalized treatment, medical imaging analysis",
                domain="healthcare",
                difficulty="medium",
                metadata={"question_type": "factual", "requires_retrieval": True}
            ),
            BenchmarkItem(
                id="rag_002",
                question="How does machine learning differ from traditional programming?",
                choices=[],
                correct_answer="Machine learning learns patterns from data rather than following explicit instructions",
                domain="technology",
                difficulty="medium",
                metadata={"question_type": "conceptual", "requires_retrieval": True}
            ),
            # Add more sample items...
        ]
        
        self.items = sample_items
        self.loaded = True
        logger.info(f"Loaded {len(self.items)} RAG evaluation items")
    
    async def evaluate_item(self, item: BenchmarkItem, model_response: str) -> bool:
        """Evaluate RAG item response using semantic similarity."""
        try:
            # For RAG evaluation, we use semantic similarity rather than exact matching
            from core.text.embeddings import get_embedder
            embedder = get_embedder("text-embedding-ada-002")
            
            # Get embeddings for both responses
            embeddings = await embedder.embed_batch([
                str(item.correct_answer),
                model_response
            ])
            
            import numpy as np
            correct_emb, response_emb = np.array(embeddings[0]), np.array(embeddings[1])
            
            # Compute cosine similarity
            similarity = np.dot(correct_emb, response_emb) / (
                np.linalg.norm(correct_emb) * np.linalg.norm(response_emb)
            )
            
            # Consider correct if similarity > threshold
            return float(similarity) > 0.7
            
        except Exception as e:
            logger.error(f"RAG evaluation failed: {e}")
            # Fallback to simple keyword matching
            correct_keywords = str(item.correct_answer).lower().split()
            response_lower = model_response.lower()
            
            matches = sum(1 for keyword in correct_keywords if keyword in response_lower)
            return matches / len(correct_keywords) > 0.5 if correct_keywords else False
    
    async def _get_model_response(self, model, item: BenchmarkItem) -> str:
        """Get RAG-enhanced model response."""
        if self.rag_pipeline:
            # Use RAG pipeline to get context-enhanced response
            try:
                rag_result = await self.rag_pipeline.query(item.question)
                context = "\n".join([chunk.content for chunk in rag_result.chunks[:3]])
                
                prompt = f"Context: {context}\n\nQuestion: {item.question}\n\nAnswer:"
                
                if hasattr(model, 'chat'):
                    from core.contracts.llm import ChatMessage, ChatCompletionRequest
                    request = ChatCompletionRequest(
                        messages=[ChatMessage(role="user", content=prompt)],
                        temperature=0.0,
                        max_tokens=200
                    )
                    response = await model.chat(request)
                    return response.choices[0].message.content.strip()
                
            except Exception as e:
                logger.error(f"RAG pipeline failed: {e}")
        
        # Fallback to regular model response
        return await super()._get_model_response(model, item)
