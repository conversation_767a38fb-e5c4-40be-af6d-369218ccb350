"""Input validation and sanitization for M-GAIF security."""

import re
import logging
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class SecurityViolation(Exception):
    """Raised when a security violation is detected."""
    
    def __init__(self, message: str, violation_type: str, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.violation_type = violation_type
        self.details = details or {}


class InputValidator:
    """Validates and sanitizes user inputs for security."""
    
    # Enhanced prompt injection patterns
    INJECTION_PATTERNS = [
        r"ignore\s+(all|any|previous).*instructions",
        r"disregard.*policy",
        r"reveal.*system.*prompt",
        r"sudo\s+",
        r"/etc/passwd",
        r"rm\s+-rf",
        r"drop\s+table",
        r"delete\s+from",
        r"exec\s*\(",
        r"eval\s*\(",
        r"<script",
        r"javascript:",
        r"data:text/html",
        r"vbscript:",
        r"onload\s*=",
        r"onerror\s*=",
        r"onclick\s*=",
        r"prompt\s*\(",
        r"alert\s*\(",
        r"confirm\s*\(",
        r"document\.cookie",
        r"window\.location",
        r"localStorage",
        r"sessionStorage",
    ]
    
    # PII patterns
    PII_PATTERNS = [
        r"\b\d{3}-\d{2}-\d{4}\b",  # SSN
        r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b",  # Credit card
        r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",  # Email
        r"\b\d{3}[\s.-]?\d{3}[\s.-]?\d{4}\b",  # Phone number
        r"\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b",  # IP address
    ]
    
    def __init__(self, strict_mode: bool = False):
        self.strict_mode = strict_mode
        self.injection_regex = re.compile("|".join(self.INJECTION_PATTERNS), re.IGNORECASE)
        self.pii_regex = re.compile("|".join(self.PII_PATTERNS), re.IGNORECASE)
    
    def validate_input(self, text: str, check_pii: bool = True, check_injection: bool = True) -> str:
        """Validate and sanitize input text.
        
        Args:
            text: Input text to validate
            check_pii: Whether to check for PII
            check_injection: Whether to check for prompt injection
            
        Returns:
            Sanitized text
            
        Raises:
            SecurityViolation: If security issues are found and strict mode is enabled
        """
        if not text:
            return text
            
        violations = []
        
        # Check for prompt injection
        if check_injection and self.injection_regex.search(text):
            violations.append({
                "type": "prompt_injection",
                "message": "Potential prompt injection detected"
            })
            logger.warning("Prompt injection attempt detected", extra={"text_preview": text[:100]})
        
        # Check for PII
        if check_pii:
            pii_matches = list(self.pii_regex.finditer(text))
            if pii_matches:
                violations.append({
                    "type": "pii_detected",
                    "message": f"PII detected: {len(pii_matches)} matches",
                    "matches": [match.group() for match in pii_matches[:5]]  # Limit to first 5
                })
                logger.warning("PII detected in input", extra={"match_count": len(pii_matches)})
        
        # Handle violations
        if violations:
            if self.strict_mode:
                raise SecurityViolation(
                    "Input validation failed",
                    "input_validation",
                    {"violations": violations}
                )
            else:
                # Log but continue
                logger.info("Security violations detected but not blocking", extra={"violations": violations})
        
        # Sanitize the text
        sanitized = self._sanitize_text(text, check_pii)
        return sanitized
    
    def _sanitize_text(self, text: str, redact_pii: bool = True) -> str:
        """Sanitize text by removing/redacting sensitive content."""
        sanitized = text
        
        # Redact PII if requested
        if redact_pii:
            # Replace SSN
            sanitized = re.sub(r"\b\d{3}-\d{2}-\d{4}\b", "***-**-****", sanitized)
            # Replace credit card numbers
            sanitized = re.sub(r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b", "**** **** **** ****", sanitized)
            # Replace email addresses
            sanitized = re.sub(r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b", "***@***.***", sanitized)
            # Replace phone numbers
            sanitized = re.sub(r"\b\d{3}[\s.-]?\d{3}[\s.-]?\d{4}\b", "***-***-****", sanitized)
            # Replace IP addresses
            sanitized = re.sub(r"\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b", "***.***.***.***", sanitized)
        
        # Remove potentially dangerous HTML/JS
        sanitized = re.sub(r"<script[^>]*>.*?</script>", "", sanitized, flags=re.IGNORECASE | re.DOTALL)
        sanitized = re.sub(r"javascript:", "", sanitized, flags=re.IGNORECASE)
        sanitized = re.sub(r"vbscript:", "", sanitized, flags=re.IGNORECASE)
        sanitized = re.sub(r"data:text/html", "", sanitized, flags=re.IGNORECASE)
        
        return sanitized
    
    def validate_workflow_spec(self, spec_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Validate workflow specification for security issues."""
        violations = []
        
        # Check for dangerous handlers
        dangerous_handlers = ["exec", "eval", "system", "shell", "subprocess"]
        
        if "nodes" in spec_dict:
            for node in spec_dict["nodes"]:
                if isinstance(node, dict):
                    handler = node.get("handler", "")
                    if any(dangerous in handler.lower() for dangerous in dangerous_handlers):
                        violations.append({
                            "type": "dangerous_handler",
                            "message": f"Potentially dangerous handler: {handler}",
                            "node_id": node.get("id", "unknown")
                        })
        
        if violations and self.strict_mode:
            raise SecurityViolation(
                "Workflow validation failed",
                "workflow_validation",
                {"violations": violations}
            )
        
        return spec_dict


class CapabilityManager:
    """Manages tool and capability access control."""
    
    def __init__(self, allowed_tools: Optional[List[str]] = None):
        self.allowed_tools = set(allowed_tools or [
            "tokenize", "embed", "retrieve", "chat", "workflow_run"
        ])
    
    def check_tool_access(self, tool_name: str, user_token: Optional[str] = None) -> bool:
        """Check if a tool can be accessed."""
        # Basic allowlist check
        if tool_name not in self.allowed_tools:
            logger.warning(f"Access denied to tool: {tool_name}")
            return False
        
        # Token-based access control
        if user_token:
            # In a real implementation, this would validate JWT tokens
            # and check user permissions against the tool
            try:
                # Basic token validation (placeholder)
                if len(user_token) < 10:
                    logger.warning(f"Invalid token format for tool: {tool_name}")
                    return False

                # Check if token is expired or invalid (placeholder)
                # In production, use proper JWT validation
                if user_token.startswith("invalid_"):
                    logger.warning(f"Invalid token for tool: {tool_name}")
                    return False

                logger.debug(f"Token-based access granted for tool: {tool_name}")
                return True
            except Exception as e:
                logger.error(f"Token validation error for tool {tool_name}: {e}")
                return False

        # Allow access without token for basic tools
        return True
    
    def get_allowed_tools(self) -> List[str]:
        """Get list of allowed tools."""
        return list(self.allowed_tools)


# Global instances
default_validator = InputValidator()
default_capability_manager = CapabilityManager()
