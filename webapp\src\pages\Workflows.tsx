import React, { use<PERSON>emo, useState } from 'react'
import { Button, Col, Input, message, Row, Space, Tabs, Typography } from 'antd'
import { api } from '../api/client'
import { parse } from 'yaml'

const templates: Record<string, string> = {
  'RAG Example': `name: rag_example_ui
nodes:
  - id: set_docs
    handler: set
    params:
      docs:
        - {id: d1, text: "the quick brown fox"}
        - {id: d2, text: "jumps over the lazy dog"}
  - id: set_query
    handler: set
    params:
      query: "quick fox"
  - id: retrieve
    handler: retrieve
    params:
      doc_state_key: docs
      query_key: query
      result_key: hits
      result_mode: texts
      top_k: 2
  - id: end
    handler: noop
edges:
  - {from: set_docs, to: set_query}
  - {from: set_query, to: retrieve}
  - {from: retrieve, to: end}
`,
  'RAG + LLM (echo)': `name: rag_llm_echo_ui
nodes:
  - id: set_docs
    handler: set
    params:
      docs:
        - {id: d1, text: "the quick brown fox"}
        - {id: d2, text: "jumps over the lazy dog"}
  - id: set_query
    handler: set
    params:
      query: "quick fox"
      prompt: "Summarize retrieved in 5 words."
  - id: retrieve
    handler: retrieve
    params:
      doc_state_key: docs
      query_key: query
      result_key: hits
      result_mode: texts
      top_k: 2
  - id: gen
    handler: call_llm
    params:
      adapter: echo
      input_key: prompt
      context_key: hits
      output_key: answer
  - id: end
    handler: noop
edges:
  - {from: set_docs, to: set_query}
  - {from: set_query, to: retrieve}
  - {from: retrieve, to: gen}
  - {from: gen, to: end}
`,
}

export default function Workflows() {
  const [yaml, setYaml] = useState(templates['RAG + LLM (echo)'])
  const [state, setState] = useState<any>({})
  const [running, setRunning] = useState(false)

  const tplItems = useMemo(
    () =>
      Object.entries(templates).map(([k, v]) => ({ key: k, label: k, children: (
        <pre style={{ whiteSpace: 'pre-wrap' }}>{v}</pre>
      ) })),
    []
  )

  async function runWorkflow() {
    try {
      setRunning(true)
      const spec = parse(yaml)
      const res = await api.workflowRun(spec, {})
      setState(res.state)
      message.success('Workflow completed')
    } catch (e: any) {
      console.error(e)
      message.error(e.message || 'Failed to run workflow')
    } finally {
      setRunning(false)
    }
  }

  return (
    <Row gutter={16}>
      <Col xs={24} lg={8}>
        <Typography.Title level={4}>Templates</Typography.Title>
        <Tabs items={tplItems} onChange={(k) => setYaml(templates[k])} />
      </Col>
      <Col xs={24} lg={8}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Typography.Title level={4}>Editor (YAML spec)</Typography.Title>
          <Input.TextArea rows={20} value={yaml} onChange={(e) => setYaml(e.target.value)} />
          <Button type="primary" onClick={runWorkflow} loading={running}>Run</Button>
        </Space>
      </Col>
      <Col xs={24} lg={8}>
        <Typography.Title level={4}>Output state</Typography.Title>
        <pre style={{ whiteSpace: 'pre-wrap' }}>{JSON.stringify(state, null, 2)}</pre>
      </Col>
    </Row>
  )
}
