"""Metrics collection for M-GAIF using Prometheus.

This module provides comprehensive metrics collection capabilities
for monitoring M-GAIF components and services.
"""

import os
import time
import logging
from typing import Dict, List, Optional, Union, Any
from contextlib import contextmanager
from functools import wraps

# Prometheus imports
try:
    from prometheus_client import (
        Counter, Histogram, Gauge, Summary, Info,
        CollectorRegistry, CONTENT_TYPE_LATEST, generate_latest,
        start_http_server, push_to_gateway
    )
    PROMETHEUS_AVAILABLE = True
except ImportError:
    PROMETHEUS_AVAILABLE = False
    # Mock classes for when Prometheus is not available
    class MockMetric:
        def inc(self, amount: float = 1, **labels) -> None:
            pass
        
        def observe(self, amount: float, **labels) -> None:
            pass
        
        def set(self, value: float, **labels) -> None:
            pass
        
        def info(self, info_dict: Dict[str, str], **labels) -> None:
            pass
    
    Counter = Histogram = Gauge = Summary = Info = MockMetric
    CollectorRegistry = None
    CONTENT_TYPE_LATEST = "text/plain"
    
    def generate_latest(registry=None):
        return b"# Prometheus metrics not available\n"
    
    def start_http_server(port, addr='', registry=None):
        pass
    
    def push_to_gateway(gateway, job, registry, grouping_key=None, timeout=None):
        pass

logger = logging.getLogger(__name__)


class MetricsCollector:
    """Centralized metrics collector for M-GAIF components."""
    
    def __init__(
        self,
        service_name: str = "mgaif",
        registry: Optional[CollectorRegistry] = None,
        namespace: str = "mgaif"
    ):
        """Initialize metrics collector.
        
        Args:
            service_name: Name of the service
            registry: Prometheus registry (uses default if None)
            namespace: Metrics namespace prefix
        """
        self.service_name = service_name
        self.registry = registry
        self.namespace = namespace
        self._metrics: Dict[str, Any] = {}
        
        # Initialize common metrics
        self._init_common_metrics()
    
    def _init_common_metrics(self):
        """Initialize common metrics used across M-GAIF."""
        # Request metrics
        self._metrics["requests_total"] = Counter(
            f"{self.namespace}_requests_total",
            "Total number of requests",
            ["service", "endpoint", "method", "status"],
            registry=self.registry
        )
        
        self._metrics["request_duration"] = Histogram(
            f"{self.namespace}_request_duration_seconds",
            "Request duration in seconds",
            ["service", "endpoint", "method"],
            registry=self.registry,
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        # Component metrics
        self._metrics["component_status"] = Gauge(
            f"{self.namespace}_component_status",
            "Component status (1=healthy, 0=unhealthy)",
            ["service", "component"],
            registry=self.registry
        )
        
        self._metrics["component_operations"] = Counter(
            f"{self.namespace}_component_operations_total",
            "Total component operations",
            ["service", "component", "operation", "status"],
            registry=self.registry
        )
        
        self._metrics["component_latency"] = Histogram(
            f"{self.namespace}_component_latency_seconds",
            "Component operation latency",
            ["service", "component", "operation"],
            registry=self.registry
        )
        
        # LLM metrics
        self._metrics["llm_tokens"] = Counter(
            f"{self.namespace}_llm_tokens_total",
            "Total LLM tokens processed",
            ["service", "model", "type"],  # type: prompt, completion
            registry=self.registry
        )
        
        self._metrics["llm_requests"] = Counter(
            f"{self.namespace}_llm_requests_total",
            "Total LLM requests",
            ["service", "model", "status"],
            registry=self.registry
        )
        
        self._metrics["llm_cost"] = Counter(
            f"{self.namespace}_llm_cost_dollars",
            "Total LLM cost in dollars",
            ["service", "model"],
            registry=self.registry
        )
        
        # RAG metrics
        self._metrics["rag_queries"] = Counter(
            f"{self.namespace}_rag_queries_total",
            "Total RAG queries",
            ["service", "status"],
            registry=self.registry
        )
        
        self._metrics["rag_retrieval_latency"] = Histogram(
            f"{self.namespace}_rag_retrieval_latency_seconds",
            "RAG retrieval latency",
            ["service", "retriever_type"],
            registry=self.registry
        )
        
        self._metrics["rag_documents_indexed"] = Counter(
            f"{self.namespace}_rag_documents_indexed_total",
            "Total documents indexed",
            ["service", "index_type"],
            registry=self.registry
        )
        
        # Agent metrics
        self._metrics["agent_tasks"] = Counter(
            f"{self.namespace}_agent_tasks_total",
            "Total agent tasks",
            ["service", "agent_type", "status"],
            registry=self.registry
        )
        
        self._metrics["agent_tool_calls"] = Counter(
            f"{self.namespace}_agent_tool_calls_total",
            "Total agent tool calls",
            ["service", "agent_type", "tool", "status"],
            registry=self.registry
        )
        
        # Workflow metrics
        self._metrics["workflow_executions"] = Counter(
            f"{self.namespace}_workflow_executions_total",
            "Total workflow executions",
            ["service", "workflow_type", "status"],
            registry=self.registry
        )
        
        self._metrics["workflow_duration"] = Histogram(
            f"{self.namespace}_workflow_duration_seconds",
            "Workflow execution duration",
            ["service", "workflow_type"],
            registry=self.registry
        )
        
        # System metrics
        self._metrics["system_info"] = Info(
            f"{self.namespace}_system_info",
            "System information",
            registry=self.registry
        )
    
    def increment_counter(
        self,
        metric_name: str,
        amount: float = 1,
        labels: Optional[Dict[str, str]] = None
    ):
        """Increment a counter metric.
        
        Args:
            metric_name: Name of the metric
            amount: Amount to increment by
            labels: Metric labels
        """
        if metric_name in self._metrics:
            labels = labels or {}
            labels["service"] = self.service_name
            self._metrics[metric_name].inc(amount, **labels)
    
    def observe_histogram(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Observe a value in a histogram metric.
        
        Args:
            metric_name: Name of the metric
            value: Value to observe
            labels: Metric labels
        """
        if metric_name in self._metrics:
            labels = labels or {}
            labels["service"] = self.service_name
            self._metrics[metric_name].observe(value, **labels)
    
    def set_gauge(
        self,
        metric_name: str,
        value: float,
        labels: Optional[Dict[str, str]] = None
    ):
        """Set a gauge metric value.
        
        Args:
            metric_name: Name of the metric
            value: Value to set
            labels: Metric labels
        """
        if metric_name in self._metrics:
            labels = labels or {}
            labels["service"] = self.service_name
            self._metrics[metric_name].set(value, **labels)
    
    def set_info(
        self,
        metric_name: str,
        info_dict: Dict[str, str],
        labels: Optional[Dict[str, str]] = None
    ):
        """Set an info metric.
        
        Args:
            metric_name: Name of the metric
            info_dict: Information dictionary
            labels: Metric labels
        """
        if metric_name in self._metrics:
            labels = labels or {}
            labels["service"] = self.service_name
            self._metrics[metric_name].info(info_dict, **labels)
    
    @contextmanager
    def time_operation(
        self,
        metric_name: str = "component_latency",
        labels: Optional[Dict[str, str]] = None
    ):
        """Context manager for timing operations.
        
        Args:
            metric_name: Name of the histogram metric
            labels: Metric labels
        
        Yields:
            Start time of the operation
        """
        start_time = time.time()
        try:
            yield start_time
        finally:
            duration = time.time() - start_time
            self.observe_histogram(metric_name, duration, labels)
    
    def track_request(
        self,
        endpoint: str,
        method: str,
        status_code: int,
        duration: float
    ):
        """Track HTTP request metrics.
        
        Args:
            endpoint: Request endpoint
            method: HTTP method
            status_code: Response status code
            duration: Request duration in seconds
        """
        labels = {
            "endpoint": endpoint,
            "method": method,
            "status": str(status_code)
        }
        
        self.increment_counter("requests_total", labels=labels)
        self.observe_histogram("request_duration", duration, {
            "endpoint": endpoint,
            "method": method
        })
    
    def track_llm_usage(
        self,
        model: str,
        prompt_tokens: int,
        completion_tokens: int,
        cost: float = 0.0,
        status: str = "success"
    ):
        """Track LLM usage metrics.
        
        Args:
            model: LLM model name
            prompt_tokens: Number of prompt tokens
            completion_tokens: Number of completion tokens
            cost: Cost in dollars
            status: Request status
        """
        model_labels = {"model": model}
        status_labels = {"model": model, "status": status}
        
        self.increment_counter("llm_tokens", prompt_tokens, {**model_labels, "type": "prompt"})
        self.increment_counter("llm_tokens", completion_tokens, {**model_labels, "type": "completion"})
        self.increment_counter("llm_requests", labels=status_labels)
        
        if cost > 0:
            self.increment_counter("llm_cost", cost, model_labels)
    
    def track_component_health(self, component: str, healthy: bool):
        """Track component health status.
        
        Args:
            component: Component name
            healthy: Whether component is healthy
        """
        self.set_gauge("component_status", 1.0 if healthy else 0.0, {"component": component})
    
    def get_metrics_text(self) -> str:
        """Get metrics in Prometheus text format.
        
        Returns:
            Metrics in Prometheus text format
        """
        if PROMETHEUS_AVAILABLE:
            return generate_latest(self.registry).decode('utf-8')
        else:
            return "# Prometheus metrics not available\n"


# Global metrics collector instance
_global_collector: Optional[MetricsCollector] = None


def get_metrics_collector(
    service_name: str = "mgaif",
    registry: Optional[CollectorRegistry] = None
) -> MetricsCollector:
    """Get global metrics collector instance.
    
    Args:
        service_name: Name of the service
        registry: Prometheus registry
    
    Returns:
        MetricsCollector instance
    """
    global _global_collector
    
    if _global_collector is None:
        _global_collector = MetricsCollector(service_name, registry)
    
    return _global_collector


def configure_metrics(
    service_name: str = "mgaif",
    http_port: Optional[int] = None,
    push_gateway: Optional[str] = None,
    push_job: str = "mgaif",
    registry: Optional[CollectorRegistry] = None
) -> MetricsCollector:
    """Configure metrics collection.
    
    Args:
        service_name: Name of the service
        http_port: Port for HTTP metrics server
        push_gateway: Push gateway URL
        push_job: Job name for push gateway
        registry: Prometheus registry
    
    Returns:
        Configured MetricsCollector instance
    """
    collector = get_metrics_collector(service_name, registry)
    
    # Start HTTP server if requested
    if http_port and PROMETHEUS_AVAILABLE:
        try:
            start_http_server(http_port, registry=registry)
            logger.info(f"Started Prometheus metrics server on port {http_port}")
        except Exception as e:
            logger.error(f"Failed to start metrics server: {e}")
    
    # Configure push gateway if requested
    if push_gateway and PROMETHEUS_AVAILABLE:
        try:
            # Test push to gateway
            push_to_gateway(push_gateway, push_job, registry or collector.registry)
            logger.info(f"Configured metrics push to {push_gateway}")
        except Exception as e:
            logger.error(f"Failed to configure push gateway: {e}")
    
    return collector


def metrics_middleware(collector: Optional[MetricsCollector] = None):
    """Create metrics middleware for FastAPI.
    
    Args:
        collector: MetricsCollector instance
    
    Returns:
        Middleware function
    """
    if collector is None:
        collector = get_metrics_collector()
    
    async def middleware(request, call_next):
        start_time = time.time()
        
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            collector.track_request(
                endpoint=request.url.path,
                method=request.method,
                status_code=response.status_code,
                duration=duration
            )
            
            return response
        except Exception as e:
            duration = time.time() - start_time
            collector.track_request(
                endpoint=request.url.path,
                method=request.method,
                status_code=500,
                duration=duration
            )
            raise
    
    return middleware


# Auto-configure metrics from environment
def _init_from_env():
    """Initialize metrics from environment variables."""
    service_name = os.getenv("SERVICE_NAME", "mgaif")
    http_port = os.getenv("METRICS_PORT")
    push_gateway = os.getenv("PROMETHEUS_PUSH_GATEWAY")
    push_job = os.getenv("PROMETHEUS_PUSH_JOB", "mgaif")
    
    if http_port:
        try:
            http_port = int(http_port)
        except ValueError:
            http_port = None
    
    if http_port or push_gateway:
        configure_metrics(
            service_name=service_name,
            http_port=http_port,
            push_gateway=push_gateway,
            push_job=push_job
        )


# Auto-initialize if environment variables are set
_init_from_env()
