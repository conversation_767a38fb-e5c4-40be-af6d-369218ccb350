"""Evaluation framework for M-GAIF components.

This module provides comprehensive evaluation capabilities for testing
and benchmarking M-GAIF components including LLMs, agents, workflows,
and complete systems.

The evaluation framework supports:
- Automated metric computation (BLEU, ROUGE, BERTScore, etc.)
- Benchmark harnesses for standard datasets
- Cost tracking and performance profiling
- A/B testing and comparison frameworks
- Custom evaluation pipelines

Key Components:
- Metric: Individual evaluation metrics
- Benchmark: Standardized evaluation suites
- Evaluator: Evaluation orchestration and execution
- CostTracker: Cost monitoring and analysis
- PerformanceProfiler: Performance analysis

Example:
    >>> from core.evaluation import Evaluator, BLEUMetric, HellaSwagBenchmark
    >>> evaluator = Evaluator()
    >>> evaluator.add_metric(BLEUMetric())
    >>> evaluator.add_benchmark(HellaSwagBenchmark())
    >>> 
    >>> results = await evaluator.evaluate_model(model, test_data)
    >>> print(f"BLEU Score: {results.metrics['bleu']}")
"""

from .base import Metric, EvaluationResult, EvaluationError
from .metrics import BLEUMetric, ROUGEMetric, BERTScoreMetric, AccuracyMetric

# Benchmarks
from .benchmarks import Benchmark, BenchmarkResult, BenchmarkItem, HellaSwagBenchmark, MMLUBenchmark, RAGBenchmark

# Evaluation orchestration
from .evaluator import Evaluator, EvaluationConfig, EvaluationReport

# Cost tracking
from .cost_tracker import CostTracker, CostReport, BudgetManager, ModelPricing, UsageRecord

# TODO: Implement performance profiler
# from .profiler import PerformanceProfiler, PerformanceReport

__all__ = [
    # Base classes
    "Metric",
    "EvaluationResult",
    "EvaluationError",

    # Metrics
    "BLEUMetric",
    "ROUGEMetric",
    "BERTScoreMetric",
    "AccuracyMetric",

    # Benchmarks
    "Benchmark",
    "BenchmarkResult",
    "BenchmarkItem",
    "HellaSwagBenchmark",
    "MMLUBenchmark",
    "RAGBenchmark",

    # Evaluation orchestration
    "Evaluator",
    "EvaluationConfig",
    "EvaluationReport",

    # Cost tracking
    "CostTracker",
    "CostReport",
    "BudgetManager",
    "ModelPricing",
    "UsageRecord",

    # TODO: Add performance profiler when implemented
    # "PerformanceProfiler",
    # "PerformanceReport",
]
