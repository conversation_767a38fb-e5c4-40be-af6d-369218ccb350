{"defaults": {"p95_ms_max": 20.0, "p99_ms_max": 40.0}, "tests": {"tests/test_workflow_engine.py::test_workflow_engine_example_yaml": {"p95_ms_max": 10.0, "qps_min": 100.0}, "tests/test_workflow_engine.py::test_workflow_branching": {"p95_ms_max": 10.0, "qps_min": 50.0}, "tests/test_workflow_rag.py::test_workflow_rag_retrieve_and_echo": {"p95_ms_max": 15.0, "qps_min": 30.0}, "tests/test_retriever.py::test_retriever_index_and_search": {"p95_ms_max": 3.0, "qps_min": 200.0}, "tests/test_llm_adapter_echo.py::test_echo_adapter_chat_and_stream": {"p95_ms_max": 5.0}, "tests/test_edge_api.py::test_chat_completions_non_stream": {"p95_ms_max": 100.0}, "tests/test_edge_api.py::test_chat_completions_throughput_concurrency": {"p95_ms_max": 250.0, "qps_min": 80.0}, "tests/test_mcp_api.py::test_mcp_tokenize": {"p95_ms_max": 60.0}, "tests/test_mcp_api.py::test_mcp_embed": {"p95_ms_max": 120.0}, "tests/test_mcp_api.py::test_mcp_retriever_index_search": {"p95_ms_max": 100.0}, "tests/test_mcp_api.py::test_mcp_llm_chat": {"p95_ms_max": 100.0}, "tests/test_mcp_api.py::test_mcp_workflow_run": {"p95_ms_max": 150.0}, "tests/test_mcp_placeholder.py::test_mcp_end_to_end_integration": {"p95_ms_max": 150.0}}}