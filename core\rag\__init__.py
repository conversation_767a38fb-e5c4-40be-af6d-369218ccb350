"""Advanced Retrieval-Augmented Generation (RAG) system for M-GAIF.

This module provides comprehensive RAG capabilities including document processing,
chunking strategies, hybrid retrieval, re-ranking, and grounding validation.

The RAG system supports:
- Multiple document chunking strategies
- Hybrid retrieval (vector + keyword)
- Advanced re-ranking algorithms
- Grounding and hallucination detection
- Multi-modal RAG support
- Query expansion and refinement

Key Components:
- DocumentProcessor: Document ingestion and preprocessing
- ChunkingStrategy: Various text chunking approaches
- HybridRetriever: Combined vector and keyword search
- Reranker: Result re-ranking and scoring
- GroundingValidator: Hallucination detection
- RAGPipeline: End-to-end RAG orchestration

Example:
    >>> from core.rag import RAGPipeline, SemanticChunker, BM25Reranker
    >>> pipeline = RAGPipeline(
    ...     chunker=SemanticChunker(chunk_size=512),
    ...     reranker=BM25Reranker(),
    ...     grounding_threshold=0.8
    ... )
    >>> result = await pipeline.query("What is machine learning?")
"""

from .base import Document, Chunk, RAGResult, RAGError
from .chunking import ChunkingStrategy, FixedSizeChunker, SemanticChunker, RecursiveChunker

# Retrieval systems
from .retrieval import (
    Retriever, RetrievalResult, VectorRetriever, KeywordRetriever, HybridRetriever
)

# Re-ranking systems
from .reranking import (
    Reranker, BM25Reranker, CrossEncoderReranker, LLMReranker, EnsembleReranker
)

# Grounding validation
from .grounding import (
    GroundingValidator, GroundingResult, HallucinationDetector,
    SourceAttributor, FactualConsistencyChecker, ComprehensiveGroundingValidator,
    SourceAttribution
)

# Pipeline orchestration
from .pipeline import RAGPipeline, RAGConfig, RAGMetrics

__all__ = [
    # Base classes
    "Document",
    "Chunk",
    "RAGResult",
    "RAGError",

    # Chunking strategies
    "ChunkingStrategy",
    "FixedSizeChunker",
    "SemanticChunker",
    "RecursiveChunker",

    # Retrieval systems
    "Retriever",
    "RetrievalResult",
    "VectorRetriever",
    "KeywordRetriever",
    "HybridRetriever",

    # Re-ranking
    "Reranker",
    "BM25Reranker",
    "CrossEncoderReranker",
    "LLMReranker",
    "EnsembleReranker",

    # Grounding validation
    "GroundingValidator",
    "GroundingResult",
    "HallucinationDetector",
    "SourceAttributor",
    "FactualConsistencyChecker",
    "ComprehensiveGroundingValidator",
    "SourceAttribution",

    # Pipeline orchestration
    "RAGPipeline",
    "RAGConfig",
    "RAGMetrics",
]