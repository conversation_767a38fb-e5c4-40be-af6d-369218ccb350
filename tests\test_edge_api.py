import asyncio
import json

import pytest
from httpx import AsyncClient, ASGITransport

from core.edge.api import app
from tests.realistic_data import get_chat_messages


@pytest.mark.asyncio
async def test_chat_completions_non_stream(async_bench):
    transport = ASGITransport(app=app)
    async with Async<PERSON>lient(transport=transport, base_url="http://test") as ac:
        # Use realistic chat message
        realistic_messages = get_chat_messages("tech_support_001")
        payload = {
            "messages": [realistic_messages[0]],  # Use first realistic message
            "stream": False,
        }
        metrics = await async_bench(lambda: ac.post("/v1/chat/completions", json=payload), iterations=10)
        assert metrics["p95_ms"] < 100
        resp = await ac.post("/v1/chat/completions", json=payload)
        assert resp.status_code == 200
        data = resp.json()
        # Echo adapter returns the user's message content
        expected_content = realistic_messages[0]["content"]
        assert data["choices"][0]["message"]["content"] == expected_content


@pytest.mark.asyncio
async def test_chat_completions_stream_sse():
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        # Use realistic chat message for streaming test
        realistic_messages = get_chat_messages("learning_ai_002")
        payload = {
            "messages": [realistic_messages[0]],  # Use first realistic message
            "stream": True,
        }
        resp = await ac.post("/v1/chat/completions", json=payload)
        assert resp.status_code == 200
        # SSE: lines prefixed by 'data: '
        lines = [line for line in resp.text.splitlines() if line.startswith("data: ")]
        assert lines[-1].strip() == "data: [DONE]"
        content_accum = []
        for line in lines[:-1]:
            payload_str = line[len("data: "):]
            try:
                obj = json.loads(payload_str)
            except json.JSONDecodeError:
                continue
            for ch in obj.get("choices", []):
                delta = ch.get("delta", {})
                if delta.get("content"):
                    content_accum.append(delta["content"])
        # The EchoAdapter should return the user's message content
        expected_content = realistic_messages[0]["content"]
        assert "".join(content_accum).strip() == expected_content


@pytest.mark.asyncio
async def test_chat_completions_throughput_concurrency(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        # Use realistic chat message for concurrency test
        realistic_messages = get_chat_messages("learning_ai_002")
        payload = {"messages": [realistic_messages[1]], "stream": False}  # Use second message

        async def _one():
            r = await ac.post("/v1/chat/completions", json=payload)
            # Accept both success and rate limit responses (rate limiting is working correctly)
            assert r.status_code in [200, 429]
            return r

        metrics = await async_bench(_one, iterations=100, concurrency=10)
        # Sanity thresholds suitable for local/CI (adjusted for rate limiting)
        assert metrics["qps"] > 50  # Lower threshold due to rate limiting
        assert metrics["p95_ms"] < 500  # Higher threshold due to rate limiting delays
