import sys
import json
import asyncio
from pathlib import Path
import yaml

from core.workflow.schema import WorkflowSpec
from core.workflow.engine import WorkflowEngine


def main() -> None:
    if len(sys.argv) < 2:
        print("Usage: python run.py <workflow_yaml>")
        sys.exit(1)
    config_path = Path(sys.argv[1])
    with config_path.open("r", encoding="utf-8") as f:
        cfg = yaml.safe_load(f) or {}

    spec = WorkflowSpec.model_validate(cfg)
    engine = WorkflowEngine(spec)

    async def run_engine():
        state = await engine.run({})
        return state

    state = asyncio.run(run_engine())
    print(json.dumps(state, indent=2))


if __name__ == "__main__":
    main()
