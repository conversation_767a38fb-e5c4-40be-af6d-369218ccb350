import pytest
from httpx import AsyncClient, ASGITransport

from core.mcp.api import app
from tests.realistic_data import get_document_corpus, get_chat_messages, get_workflow_spec


@pytest.mark.asyncio
async def test_mcp_end_to_end_integration(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        # 1) Tokenize realistic text
        realistic_text = "Natural language processing enables computers to understand human communication effectively."
        r = await ac.post("/mcp/tools/tokenize", json={"text": realistic_text})
        assert r.status_code == 200
        toks = r.json()["tokens"]
        assert isinstance(toks, list) and len(toks) >= 10  # Realistic text has more tokens

        # 2) Embed realistic texts
        realistic_texts = ["Artificial intelligence transforms industries", "Machine learning drives innovation"]
        r = await ac.post("/mcp/tools/embed", json={"texts": realistic_texts})
        assert r.status_code == 200
        vecs = r.json()["vectors"]
        assert len(vecs) == 2 and all(isinstance(v, list) for v in vecs)

        # 3) Retriever index + search with realistic documents
        corpus = get_document_corpus(2)
        items = [{"id": doc["id"], "text": doc["content"]} for doc in corpus]
        r = await ac.post("/mcp/tools/retriever/index", json={"items": items})
        assert r.status_code == 200
        s = await ac.post("/mcp/tools/retriever/search", json={"query": "machine learning applications", "top_k": 1})
        assert s.status_code == 200
        hits = s.json()["hits"]
        assert len(hits) == 1 and hits[0]["id"] in {doc["id"] for doc in corpus}

        # 4) LLM chat with realistic conversation
        chat_messages = get_chat_messages("tech_support_001")
        test_message = {"messages": [chat_messages[0]]}  # Use first realistic message
        r = await ac.post("/mcp/tools/llm/chat", json=test_message)
        assert r.status_code == 200
        # Echo adapter returns the user's message content
        assert r.json()["choices"][0]["content"] == chat_messages[0]["content"]

        # 5) Workflow run with realistic document analysis
        spec = get_workflow_spec("document_analysis_pipeline")
        r = await ac.post("/mcp/tools/workflow/run", json={"spec": spec, "state": {}})
        assert r.status_code == 200
        result_state = r.json()["state"]
        # Check that workflow executed and produced analysis output
        assert "analysis" in result_state
        assert "tokens" in result_state

        # Bench a light sequence call (chat) for consistency in benchmarks
        bench_payload = {"messages": [{"role": "user", "content": "Explain neural networks briefly."}]}
        metrics = await async_bench(lambda: ac.post("/mcp/tools/llm/chat", json=bench_payload), iterations=10)
        assert metrics["p95_ms"] < 120
