name: CI

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: <PERSON><PERSON> pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Type check (optional)
        run: |
          pip install mypy
          mypy core plugins --ignore-missing-imports || true

      - name: Security scan
        run: |
          # Install security scanning tools
          pip install cyclonedx-bom safety bandit

          # Run comprehensive security scan
          python scripts/security_scan.py --output-dir security-reports

          # Also run individual tools for CI visibility
          pip-audit -r requirements.txt --desc || true
          bandit -r core/ -f json -o security-reports/bandit-report.json || true
          safety check --json --output security-reports/safety-report.json || true

      - name: Code quality checks
        run: |
          pip install flake8
          flake8 core plugins --max-line-length=120 --ignore=E203,W503 || true

      - name: Run tests with coverage
        env:
          MGAIF_BENCH_OUT: .benchmarks.json
          MGAIF_BENCH_THRESHOLDS: benchmarks_thresholds.ci.json
        run: |
          pip install pytest-cov
          pytest -q --cov=core --cov=plugins --cov-report=xml --cov-report=term-missing

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ./coverage.xml
          fail_ci_if_error: false

      - name: Upload benchmark artifact
        uses: actions/upload-artifact@v4
        with:
          name: benchmarks
          path: .benchmarks.json
          if-no-files-found: warn

      - name: Upload security reports
        uses: actions/upload-artifact@v4
        with:
          name: security-reports
          path: security-reports/
          if-no-files-found: warn
