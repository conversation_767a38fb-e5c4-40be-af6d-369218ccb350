"""Multi-agent coordination and orchestration."""

from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from .base import Agent, AgentResult, ExecutionContext

logger = logging.getLogger(__name__)


class CoordinationStrategy(Enum):
    """Multi-agent coordination strategies."""
    SEQUENTIAL = "sequential"  # Execute agents one after another
    PARALLEL = "parallel"     # Execute agents simultaneously
    HIERARCHICAL = "hierarchical"  # Master-worker pattern
    COLLABORATIVE = "collaborative"  # Agents collaborate and share state
    COMPETITIVE = "competitive"  # Agents compete for best solution


@dataclass
class MultiAgentResult:
    """Result of multi-agent execution."""
    success: bool
    results: Dict[str, AgentResult]
    coordination_strategy: CoordinationStrategy
    total_execution_time: float
    metadata: Dict[str, Any]
    timestamp: datetime


@dataclass
class AgentTask:
    """Task assignment for an agent."""
    agent_name: str
    goal: str
    context: ExecutionContext
    dependencies: List[str]
    priority: int = 0


class AgentCoordinator:
    """Coordinates multiple agents for complex task execution.
    
    Manages agent orchestration, task distribution, and result
    aggregation for multi-agent systems.
    
    Features:
    - Multiple coordination strategies
    - Task dependency management
    - Agent load balancing
    - Result aggregation
    - Failure handling and recovery
    
    Example:
        >>> coordinator = AgentCoordinator()
        >>> coordinator.register_agent("researcher", research_agent)
        >>> coordinator.register_agent("writer", writing_agent)
        >>> 
        >>> tasks = [
        ...     AgentTask("researcher", "Research AI safety", context),
        ...     AgentTask("writer", "Write summary", context, dependencies=["researcher"])
        ... ]
        >>> result = await coordinator.execute_tasks(tasks, CoordinationStrategy.SEQUENTIAL)
    """
    
    def __init__(self):
        """Initialize agent coordinator."""
        self._agents: Dict[str, Agent] = {}
        self._agent_status: Dict[str, str] = {}
        self._shared_state: Dict[str, Any] = {}
        self._execution_locks: Dict[str, asyncio.Lock] = {}
    
    def register_agent(self, name: str, agent: Agent) -> None:
        """Register an agent for coordination.
        
        Args:
            name: Unique agent name
            agent: Agent instance
        """
        self._agents[name] = agent
        self._agent_status[name] = "idle"
        self._execution_locks[name] = asyncio.Lock()
        logger.info(f"Registered agent: {name}")
    
    def unregister_agent(self, name: str) -> bool:
        """Unregister an agent.
        
        Args:
            name: Agent name to unregister
            
        Returns:
            True if agent was unregistered
        """
        if name in self._agents:
            del self._agents[name]
            del self._agent_status[name]
            del self._execution_locks[name]
            logger.info(f"Unregistered agent: {name}")
            return True
        return False
    
    def get_available_agents(self) -> List[str]:
        """Get list of available agent names."""
        return [name for name, status in self._agent_status.items() 
                if status == "idle"]
    
    async def execute_tasks(self, tasks: List[AgentTask], 
                           strategy: CoordinationStrategy = CoordinationStrategy.SEQUENTIAL) -> MultiAgentResult:
        """Execute multiple tasks using specified coordination strategy.
        
        Args:
            tasks: List of tasks to execute
            strategy: Coordination strategy to use
            
        Returns:
            MultiAgentResult with aggregated results
        """
        start_time = datetime.now()
        
        try:
            if strategy == CoordinationStrategy.SEQUENTIAL:
                results = await self._execute_sequential(tasks)
            elif strategy == CoordinationStrategy.PARALLEL:
                results = await self._execute_parallel(tasks)
            elif strategy == CoordinationStrategy.HIERARCHICAL:
                results = await self._execute_hierarchical(tasks)
            elif strategy == CoordinationStrategy.COLLABORATIVE:
                results = await self._execute_collaborative(tasks)
            elif strategy == CoordinationStrategy.COMPETITIVE:
                results = await self._execute_competitive(tasks)
            else:
                raise ValueError(f"Unknown coordination strategy: {strategy}")
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return MultiAgentResult(
                success=all(result.success for result in results.values()),
                results=results,
                coordination_strategy=strategy,
                total_execution_time=execution_time,
                metadata={
                    "task_count": len(tasks),
                    "agent_count": len(set(task.agent_name for task in tasks))
                },
                timestamp=start_time
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Multi-agent execution failed: {e}")
            
            return MultiAgentResult(
                success=False,
                results={},
                coordination_strategy=strategy,
                total_execution_time=execution_time,
                metadata={"error": str(e)},
                timestamp=start_time
            )
    
    async def _execute_sequential(self, tasks: List[AgentTask]) -> Dict[str, AgentResult]:
        """Execute tasks sequentially with dependency resolution."""
        results = {}
        completed_tasks: Set[str] = set()
        
        # Sort tasks by dependencies and priority
        sorted_tasks = self._sort_tasks_by_dependencies(tasks)
        
        for task in sorted_tasks:
            # Check dependencies
            if not all(dep in completed_tasks for dep in task.dependencies):
                logger.warning(f"Task {task.agent_name} has unmet dependencies")
                continue
            
            # Execute task
            if task.agent_name not in self._agents:
                logger.error(f"Agent {task.agent_name} not found")
                continue
            
            agent = self._agents[task.agent_name]
            
            # Update shared state with previous results
            task.context.metadata["shared_state"] = self._shared_state.copy()
            task.context.metadata["previous_results"] = {
                k: v.result for k, v in results.items()
            }
            
            # Execute agent
            self._agent_status[task.agent_name] = "busy"
            result = await agent.execute(task.goal, task.context)
            self._agent_status[task.agent_name] = "idle"
            
            results[task.agent_name] = result
            completed_tasks.add(task.agent_name)
            
            # Update shared state
            if result.success:
                self._shared_state[f"{task.agent_name}_result"] = result.result
        
        return results
    
    async def _execute_parallel(self, tasks: List[AgentTask]) -> Dict[str, AgentResult]:
        """Execute tasks in parallel (ignoring dependencies)."""
        async def execute_task(task: AgentTask) -> tuple[str, AgentResult]:
            if task.agent_name not in self._agents:
                return task.agent_name, AgentResult(
                    success=False,
                    result=None,
                    reasoning=f"Agent {task.agent_name} not found",
                    steps_taken=[],
                    execution_time=0.0,
                    tokens_used=0,
                    cost_estimate=0.0,
                    metadata={},
                    timestamp=datetime.now()
                )
            
            agent = self._agents[task.agent_name]
            self._agent_status[task.agent_name] = "busy"
            
            try:
                result = await agent.execute(task.goal, task.context)
                return task.agent_name, result
            finally:
                self._agent_status[task.agent_name] = "idle"
        
        # Execute all tasks in parallel
        task_coroutines = [execute_task(task) for task in tasks]
        task_results = await asyncio.gather(*task_coroutines, return_exceptions=True)
        
        results = {}
        for task_result in task_results:
            if isinstance(task_result, Exception):
                logger.error(f"Task execution failed: {task_result}")
            else:
                agent_name, result = task_result
                results[agent_name] = result
        
        return results
    
    async def _execute_hierarchical(self, tasks: List[AgentTask]) -> Dict[str, AgentResult]:
        """Execute tasks in hierarchical master-worker pattern."""
        # Find master task (highest priority or first without dependencies)
        master_task = max(tasks, key=lambda t: t.priority)
        worker_tasks = [t for t in tasks if t != master_task]
        
        results = {}
        
        # Execute worker tasks first
        if worker_tasks:
            worker_results = await self._execute_parallel(worker_tasks)
            results.update(worker_results)
        
        # Execute master task with worker results
        if master_task.agent_name in self._agents:
            master_task.context.metadata["worker_results"] = {
                k: v.result for k, v in results.items()
            }
            
            agent = self._agents[master_task.agent_name]
            self._agent_status[master_task.agent_name] = "busy"
            master_result = await agent.execute(master_task.goal, master_task.context)
            self._agent_status[master_task.agent_name] = "idle"
            
            results[master_task.agent_name] = master_result
        
        return results
    
    async def _execute_collaborative(self, tasks: List[AgentTask]) -> Dict[str, AgentResult]:
        """Execute tasks with collaborative state sharing."""
        results = {}
        
        # Execute tasks with shared state updates
        for task in tasks:
            if task.agent_name not in self._agents:
                continue
            
            # Provide shared state to agent
            task.context.metadata["shared_state"] = self._shared_state.copy()
            task.context.metadata["collaboration_results"] = {
                k: v.result for k, v in results.items()
            }
            
            agent = self._agents[task.agent_name]
            self._agent_status[task.agent_name] = "busy"
            result = await agent.execute(task.goal, task.context)
            self._agent_status[task.agent_name] = "idle"
            
            results[task.agent_name] = result
            
            # Update shared state immediately
            if result.success:
                self._shared_state[f"{task.agent_name}_result"] = result.result
                self._shared_state[f"{task.agent_name}_reasoning"] = result.reasoning
        
        return results
    
    async def _execute_competitive(self, tasks: List[AgentTask]) -> Dict[str, AgentResult]:
        """Execute tasks competitively and select best result."""
        # Execute all tasks in parallel
        parallel_results = await self._execute_parallel(tasks)
        
        # Select best result based on success and confidence
        best_agent = None
        best_score = -1.0
        
        for agent_name, result in parallel_results.items():
            if result.success:
                # Simple scoring: success + (1 - execution_time/max_time)
                max_time = max(r.execution_time for r in parallel_results.values())
                time_score = 1.0 - (result.execution_time / max_time) if max_time > 0 else 1.0
                score = 1.0 + time_score
                
                if score > best_score:
                    best_score = score
                    best_agent = agent_name
        
        # Return all results but mark the winner
        for agent_name, result in parallel_results.items():
            result.metadata["is_winner"] = (agent_name == best_agent)
        
        return parallel_results
    
    def _sort_tasks_by_dependencies(self, tasks: List[AgentTask]) -> List[AgentTask]:
        """Sort tasks by dependencies using topological sort."""
        # Simple dependency resolution - in production, use proper topological sort
        sorted_tasks = []
        remaining_tasks = tasks.copy()
        
        while remaining_tasks:
            # Find tasks with no unmet dependencies
            ready_tasks = [
                task for task in remaining_tasks
                if all(dep in [t.agent_name for t in sorted_tasks] for dep in task.dependencies)
            ]
            
            if not ready_tasks:
                # Circular dependency or missing dependency
                logger.warning("Circular or missing dependencies detected")
                sorted_tasks.extend(remaining_tasks)
                break
            
            # Sort ready tasks by priority
            ready_tasks.sort(key=lambda t: t.priority, reverse=True)
            
            # Add first ready task
            sorted_tasks.append(ready_tasks[0])
            remaining_tasks.remove(ready_tasks[0])
        
        return sorted_tasks
    
    def get_shared_state(self) -> Dict[str, Any]:
        """Get current shared state."""
        return self._shared_state.copy()
    
    def update_shared_state(self, updates: Dict[str, Any]) -> None:
        """Update shared state."""
        self._shared_state.update(updates)
    
    def clear_shared_state(self) -> None:
        """Clear shared state."""
        self._shared_state.clear()
    
    def get_agent_status(self) -> Dict[str, str]:
        """Get status of all agents."""
        return self._agent_status.copy()
