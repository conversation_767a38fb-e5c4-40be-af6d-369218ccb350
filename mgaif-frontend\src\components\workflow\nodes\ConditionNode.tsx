import React from 'react';
import { NodeProps, Handle, Position } from 'reactflow';
import { BaseNode } from './BaseNode';
import type { ConditionNodeData } from '../../../types/workflow';

export const ConditionNode: React.FC<NodeProps> = (props) => {
  const data = props.data as ConditionNodeData;

  return (
    <>
      <BaseNode
        {...props}
        data={data}
        icon="🔀"
        color="#eb2f96"
        showHandles={{ source: false, target: true }}
      />
      
      {/* Multiple output handles for condition branches */}
      <Handle
        type="source"
        position={Position.Right}
        id="true"
        style={{
          background: '#52c41a',
          width: 12,
          height: 12,
          border: '2px solid white',
          top: '30%',
        }}
      />
      <Handle
        type="source"
        position={Position.Right}
        id="false"
        style={{
          background: '#ff4d4f',
          width: 12,
          height: 12,
          border: '2px solid white',
          top: '70%',
        }}
      />
    </>
  );
};
