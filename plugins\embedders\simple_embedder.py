"""Simple hash-based embedder implementation.

This module provides a deterministic embedder that converts text to vector
embeddings using cryptographic hashing. While not semantically meaningful,
it provides consistent, reproducible embeddings suitable for testing and
development scenarios.

Features:
- Deterministic hash-based embeddings
- Configurable vector dimensions
- Unit-normalized output vectors
- Fast computation using SHA-256
- No external dependencies or model files

Performance Characteristics:
- O(n) complexity with text length
- Consistent memory usage
- Fast hash computation
- Deterministic output for same input

Example:
    >>> embedder = SimpleEmbedder(dim=128)
    >>> vectors = embedder.embed(["Hello", "World"])
    >>> print(len(vectors))  # 2
    >>> print(len(vectors[0]))  # 128

Note:
    This embedder does not capture semantic similarity - similar texts
    may produce very different embeddings. Use only for testing or when
    semantic similarity is not required.
"""

from __future__ import annotations

import hashlib
from typing import List

from core.text.embeddings import Embedder


class SimpleEmbedder(Embedder):
    """Hash-based deterministic embedder for testing and development.

    Generates consistent vector embeddings from text using SHA-256 hashing.
    While not semantically meaningful, provides deterministic and reproducible
    embeddings suitable for testing and development scenarios.

    The embedder works by:
    1. Computing SHA-256 hash of input text
    2. Converting hash bytes to float values
    3. Normalizing to unit vector
    4. Padding or truncating to desired dimension

    Args:
        dim: Desired embedding dimension (default: 8)

    Example:
        >>> embedder = SimpleEmbedder(dim=64)
        >>> vectors = embedder.embed(["Hello", "World", "Hello"])
        >>> print(len(vectors))  # 3
        >>> print(len(vectors[0]))  # 64
        >>> print(vectors[0] == vectors[2])  # True (deterministic)

    Note:
        - Same text always produces same embedding
        - Different texts produce very different embeddings
        - No semantic similarity preservation
        - Suitable for testing and development only
    """

    def __init__(self, dim: int = 8) -> None:
        """Initialize embedder with specified dimension.

        Args:
            dim: Vector dimension for output embeddings

        Raises:
            ValueError: If dim is not positive
        """
        if dim <= 0:
            raise ValueError("Embedding dimension must be positive")
        self.dim = dim

    def _embed_one(self, text: str) -> List[float]:
        """Generate embedding for a single text string.

        Args:
            text: Input text to embed

        Returns:
            Unit-normalized float vector of length self.dim
        """
        h = hashlib.sha256(text.encode("utf-8")).digest()
        # take the first dim bytes, map to [0,1), then normalize
        vals = [(b / 255.0) for b in h[: self.dim]]
        # simple L2 normalization
        norm = sum(v * v for v in vals) ** 0.5 or 1.0
        return [v / norm for v in vals]

    def embed(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a batch of texts.

        Converts each input text to a deterministic vector embedding
        using SHA-256 hashing and normalization.

        Args:
            texts: List of text strings to embed

        Returns:
            List of unit-normalized float vectors, one per input text

        Example:
            >>> embedder = SimpleEmbedder(dim=8)
            >>> vectors = embedder.embed(["Hello", "World"])
            >>> print(len(vectors))  # 2
            >>> print(all(len(v) == 8 for v in vectors))  # True

        Note:
            All output vectors are unit-normalized (L2 norm = 1.0).
            Identical input texts produce identical output vectors.
        """
        return [self._embed_one(t) for t in texts]
