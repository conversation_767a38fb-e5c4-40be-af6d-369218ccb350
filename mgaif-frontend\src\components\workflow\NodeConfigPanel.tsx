import React, { useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Select,
  Switch,
  Button,
  Typography,
  Space,
  Divider,
  Alert,
} from 'antd';
import { SaveOutlined, DeleteOutlined } from '@ant-design/icons';
import { useWorkflowStore } from '../../stores/workflowStore';
import type { WorkflowNode, NodeConfigSchema } from '../../types/workflow';

const { TextArea } = Input;
const { Option } = Select;

interface NodeConfigPanelProps {
  node: WorkflowNode | null;
  onClose?: () => void;
}

export const NodeConfigPanel: React.FC<NodeConfigPanelProps> = ({
  node,
  onClose,
}) => {
  const { updateNode, deleteNode, nodeTemplates } = useWorkflowStore();
  const [form] = Form.useForm();

  // Find the template for this node type
  const template = nodeTemplates.find((t) => t.type === node?.type);

  useEffect(() => {
    if (node) {
      // Set form values from node data
      form.setFieldsValue({
        label: node.data.label,
        description: node.data.description,
        ...node.data.config,
      });
    }
  }, [node, form]);

  const handleSave = async () => {
    if (!node) return;

    try {
      const values = await form.validateFields();
      const { label, description, ...config } = values;

      updateNode(node.id, {
        label,
        description,
        config,
      });

      onClose?.();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handleDelete = () => {
    if (!node) return;
    deleteNode(node.id);
    onClose?.();
  };

  const renderFormField = (
    key: string,
    schema: NodeConfigSchema['properties'][string]
  ) => {
    const commonProps = {
      label: schema.label,
      name: key,
      rules: [
        {
          required: schema.required,
          message: `${schema.label} is required`,
        },
      ],
      help: schema.description,
    };

    switch (schema.type) {
      case 'string':
        return (
          <Form.Item {...commonProps}>
            <Input placeholder={`Enter ${schema.label.toLowerCase()}`} />
          </Form.Item>
        );

      case 'textarea':
        return (
          <Form.Item {...commonProps}>
            <TextArea
              rows={4}
              placeholder={`Enter ${schema.label.toLowerCase()}`}
            />
          </Form.Item>
        );

      case 'number':
        return (
          <Form.Item {...commonProps}>
            <InputNumber
              style={{ width: '100%' }}
              min={schema.validation?.min}
              max={schema.validation?.max}
              placeholder={`Enter ${schema.label.toLowerCase()}`}
            />
          </Form.Item>
        );

      case 'boolean':
        return (
          <Form.Item {...commonProps} valuePropName="checked">
            <Switch />
          </Form.Item>
        );

      case 'select':
        return (
          <Form.Item {...commonProps}>
            <Select placeholder={`Select ${schema.label.toLowerCase()}`}>
              {schema.options?.map((option) => (
                <Option key={option.value} value={option.value}>
                  {option.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        );

      default:
        return (
          <Form.Item {...commonProps}>
            <Input placeholder={`Enter ${schema.label.toLowerCase()}`} />
          </Form.Item>
        );
    }
  };

  if (!node || !template) {
    return (
      <Card
        title="Node Configuration"
        size="small"
        style={{ height: '100%' }}
        bodyStyle={{ 
          padding: '16px', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center' 
        }}
      >
        <Typography.Text type="secondary">
          Select a node to configure its properties
        </Typography.Text>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <span style={{ fontSize: '16px' }}>{template.icon}</span>
          <Typography.Text strong>{node.data.label}</Typography.Text>
        </Space>
      }
      size="small"
      style={{ height: '100%', overflow: 'hidden' }}
      bodyStyle={{ 
        padding: '16px', 
        height: 'calc(100% - 57px)', 
        overflow: 'auto' 
      }}
      extra={
        <Button
          type="text"
          danger
          size="small"
          icon={<DeleteOutlined />}
          onClick={handleDelete}
        >
          Delete
        </Button>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        size="small"
      >
        {/* Basic Properties */}
        <Typography.Title level={5}>Basic Properties</Typography.Title>
        
        <Form.Item
          label="Label"
          name="label"
          rules={[{ required: true, message: 'Label is required' }]}
        >
          <Input placeholder="Enter node label" />
        </Form.Item>

        <Form.Item
          label="Description"
          name="description"
        >
          <TextArea
            rows={2}
            placeholder="Enter node description (optional)"
          />
        </Form.Item>

        <Divider />

        {/* Node-specific Configuration */}
        <Typography.Title level={5}>Configuration</Typography.Title>

        {Object.entries(template.configSchema.properties).map(([key, schema]) =>
          renderFormField(key, schema)
        )}

        {/* Validation Errors */}
        {node.data.errors && node.data.errors.length > 0 && (
          <>
            <Divider />
            <Alert
              message="Configuration Errors"
              description={
                <ul style={{ margin: 0, paddingLeft: '20px' }}>
                  {node.data.errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              }
              type="error"
              showIcon
              style={{ marginBottom: '16px' }}
            />
          </>
        )}

        {/* Actions */}
        <div style={{ 
          position: 'sticky', 
          bottom: 0, 
          background: 'white', 
          paddingTop: '16px',
          borderTop: '1px solid #f0f0f0',
          marginTop: '16px'
        }}>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button onClick={onClose}>
              Cancel
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
            >
              Save Changes
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
};
