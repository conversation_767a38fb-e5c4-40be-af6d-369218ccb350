import axios, { AxiosResponse } from 'axios';
import type { ApiResponse, ApiError, User, Project } from '../types';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response.data,
  (error) => {
    const apiError: ApiError = {
      message: error.response?.data?.message || 'An error occurred',
      code: error.response?.status?.toString() || 'UNKNOWN',
      details: error.response?.data,
    };
    
    if (error.response?.status === 401) {
      // Handle unauthorized - redirect to login
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    
    return Promise.reject(apiError);
  }
);

// API endpoints
export const api = {
  // Authentication
  auth: {
    login: (email: string, password: string) =>
      apiClient.post<ApiResponse<{ user: User; token: string }>>('/api/auth/login', {
        email,
        password,
      }),
    register: (name: string, email: string, password: string) =>
      apiClient.post<ApiResponse<{ user: User; token: string }>>('/api/auth/register', {
        name,
        email,
        password,
      }),
    logout: () => apiClient.post('/api/auth/logout'),
    me: () => apiClient.get<ApiResponse<User>>('/api/auth/me'),
  },

  // Projects
  projects: {
    getAll: () => apiClient.get<ApiResponse<Project[]>>('/api/projects'),
    getById: (id: string) => apiClient.get<ApiResponse<Project>>(`/api/projects/${id}`),
    create: (data: Partial<Project>) =>
      apiClient.post<ApiResponse<Project>>('/api/projects', data),
    update: (id: string, data: Partial<Project>) =>
      apiClient.put<ApiResponse<Project>>(`/api/projects/${id}`, data),
    delete: (id: string) => apiClient.delete(`/api/projects/${id}`),
  },

  // Health check
  health: () => apiClient.get('/api/health'),

  // MCP API Integration
  mcp: {
    // Tokenization
    tokenize: (text: string) =>
      apiClient.post('/mcp/tools/tokenize', { text }),

    // Embedding
    embed: (texts: string[]) =>
      apiClient.post('/mcp/tools/embed', { texts }),

    // Retrieval
    retrieverIndex: (items: Array<{ id: string; text: string }>) =>
      apiClient.post('/mcp/tools/retriever/index', { items }),

    retrieverSearch: (query: string, top_k: number = 5) =>
      apiClient.post('/mcp/tools/retriever/search', { query, top_k }),

    // LLM Chat
    llmChat: (messages: Array<{ role: string; content: string }>) =>
      apiClient.post('/mcp/tools/llm/chat', { messages }),

    // Workflow Execution
    workflowRun: (spec: any, state: any = {}) =>
      apiClient.post('/mcp/tools/workflow/run', { spec, state }),
  },

  // Edge API Integration (OpenAI-compatible)
  edge: {
    // Chat Completions
    chatCompletions: (messages: Array<{ role: string; content: string }>, options: any = {}) =>
      apiClient.post('/v1/chat/completions', {
        messages,
        stream: false,
        ...options
      }),

    // Streaming Chat Completions
    chatCompletionsStream: (messages: Array<{ role: string; content: string }>, options: any = {}) =>
      apiClient.post('/v1/chat/completions', {
        messages,
        stream: true,
        ...options
      }, {
        responseType: 'stream'
      }),
  },

  // RAG Data Store Management
  rag: {
    // Document Management
    uploadDocument: (file: File, metadata: any = {}) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('metadata', JSON.stringify(metadata));
      return apiClient.post('/api/rag/documents', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });
    },

    getDocuments: () => apiClient.get('/api/rag/documents'),

    deleteDocument: (id: string) => apiClient.delete(`/api/rag/documents/${id}`),

    // Chunking Configuration
    configureChunking: (strategy: any) =>
      apiClient.post('/api/rag/chunking/configure', strategy),

    // Vector Store Management
    createVectorStore: (config: any) =>
      apiClient.post('/api/rag/vector-stores', config),

    getVectorStores: () => apiClient.get('/api/rag/vector-stores'),

    // Search and Testing
    searchDocuments: (query: string, options: any = {}) =>
      apiClient.post('/api/rag/search', { query, ...options }),
  },
};

export default api;
