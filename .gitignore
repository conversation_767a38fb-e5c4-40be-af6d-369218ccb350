# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.venv/
venv/
ENV/

# PyInstaller
*.manifest
*.spec

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# PyCharm
.idea/

# VSCode
.vscode/

# mypy
.mypy_cache/
.dmypy.json

# pyright
.pyright

# Logs
logs/
*.log

# Local environments
.env
.env.*

# Docker
**/.dockerignore
**/Dockerfile

# Node
node_modules/
