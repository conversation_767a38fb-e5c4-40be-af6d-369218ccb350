{"results": [{"test": "tests/test_edge_api.py::test_chat_completions_non_stream", "iterations": 10.0, "concurrency": 1.0, "total_ms": 30.970699968747795, "avg_ms": 3.0618099961429834, "p50_ms": 1.6034500440582633, "p95_ms": 7.8581999987363815, "p99_ms": 7.8581999987363815, "min_ms": 1.2698000064119697, "max_ms": 9.755500010214746, "qps": 322.8858246694745}, {"test": "tests/test_edge_api.py::test_chat_completions_throughput_concurrency", "iterations": 100.0, "concurrency": 10.0, "total_ms": 187.17749998904765, "avg_ms": 17.581482999958098, "p50_ms": 11.097349983174354, "p95_ms": 85.7553998939693, "p99_ms": 85.98060009535402, "min_ms": 2.8678999515250325, "max_ms": 86.19709999766201, "qps": 534.2522472297755}, {"test": "tests/test_mcp_api.py::test_mcp_tokenize", "iterations": 20.0, "concurrency": 1.0, "total_ms": 22.22409995738417, "avg_ms": 1.0878499888349324, "p50_ms": 0.9801499545574188, "p95_ms": 1.6295999521389604, "p99_ms": 1.6295999521389604, "min_ms": 0.8795999456197023, "max_ms": 1.9139000214636326, "qps": 899.9239581513316}, {"test": "tests/test_mcp_api.py::test_mcp_embed", "iterations": 10.0, "concurrency": 1.0, "total_ms": 10.806999984197319, "avg_ms": 1.058139989618212, "p50_ms": 1.0504500241950154, "p95_ms": 1.2271000305190682, "p99_ms": 1.2271000305190682, "min_ms": 0.8927000453695655, "max_ms": 1.2790999608114362, "qps": 925.3261788306315}, {"test": "tests/test_mcp_api.py::test_mcp_retriever_index_search", "iterations": 20.0, "concurrency": 1.0, "total_ms": 23.087000008672476, "avg_ms": 1.1332199967000633, "p50_ms": 1.0801500175148249, "p95_ms": 1.5709999715909362, "p99_ms": 1.5709999715909362, "min_ms": 0.9154999861493707, "max_ms": 1.6750000650063157, "qps": 866.2883870787515}, {"test": "tests/test_mcp_api.py::test_mcp_llm_chat", "iterations": 30.0, "concurrency": 1.0, "total_ms": 32.113300054334104, "avg_ms": 1.0491333475025992, "p50_ms": 1.032850006595254, "p95_ms": 1.2530999956652522, "p99_ms": 1.2698000064119697, "min_ms": 0.8682000916451216, "max_ms": 1.2925999471917748, "qps": 934.1923735412273}, {"test": "tests/test_mcp_api.py::test_mcp_workflow_run", "iterations": 10.0, "concurrency": 1.0, "total_ms": 10.593999992124736, "avg_ms": 1.038600003812462, "p50_ms": 1.020600029733032, "p95_ms": 1.1323000071570277, "p99_ms": 1.1323000071570277, "min_ms": 0.9005999891087413, "max_ms": 1.3747999910265207, "qps": 943.9305274149237}]}