#!/usr/bin/env python3
"""Performance benchmarking script for M-GAIF."""

import asyncio
import json
import time
import statistics
import httpx
import logging
from typing import Dict, List, Any
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, asdict

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class BenchmarkResult:
    """Benchmark result data structure."""
    test_name: str
    total_requests: int
    successful_requests: int
    failed_requests: int
    avg_response_time: float
    min_response_time: float
    max_response_time: float
    p95_response_time: float
    p99_response_time: float
    requests_per_second: float
    total_duration: float
    error_rate: float


class PerformanceBenchmark:
    """Performance benchmarking suite for M-GAIF APIs."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results: List[BenchmarkResult] = []
    
    async def benchmark_chat_completions(
        self, 
        num_requests: int = 100, 
        concurrent_requests: int = 10,
        stream: bool = False
    ) -> BenchmarkResult:
        """Benchmark chat completions endpoint."""
        
        test_name = f"chat_completions_{'streaming' if stream else 'non_streaming'}"
        logger.info(f"Starting {test_name} benchmark: {num_requests} requests, {concurrent_requests} concurrent")
        
        request_data = {
            "messages": [{"role": "user", "content": "Hello, world!"}],
            "stream": stream
        }
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def make_request():
                nonlocal successful_requests, failed_requests
                
                async with semaphore:
                    request_start = time.time()
                    try:
                        response = await client.post(
                            f"{self.base_url}/v1/chat/completions",
                            json=request_data
                        )
                        request_end = time.time()
                        
                        if response.status_code == 200:
                            successful_requests += 1
                            response_times.append(request_end - request_start)
                        else:
                            failed_requests += 1
                            logger.warning(f"Request failed with status {response.status_code}")
                    
                    except Exception as e:
                        failed_requests += 1
                        logger.error(f"Request failed with exception: {e}")
            
            # Execute all requests
            tasks = [make_request() for _ in range(num_requests)]
            await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Calculate statistics
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            p99_response_time = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p95_response_time = p99_response_time = 0
        
        requests_per_second = num_requests / total_duration if total_duration > 0 else 0
        error_rate = (failed_requests / num_requests) * 100 if num_requests > 0 else 0
        
        result = BenchmarkResult(
            test_name=test_name,
            total_requests=num_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            total_duration=total_duration,
            error_rate=error_rate
        )
        
        self.results.append(result)
        logger.info(f"Completed {test_name}: {requests_per_second:.2f} RPS, {avg_response_time:.3f}s avg response time")
        return result
    
    async def benchmark_mcp_tools(self, num_requests: int = 50, concurrent_requests: int = 5) -> BenchmarkResult:
        """Benchmark MCP tools endpoints."""
        
        test_name = "mcp_tools"
        logger.info(f"Starting {test_name} benchmark: {num_requests} requests, {concurrent_requests} concurrent")
        
        # Test different MCP endpoints
        endpoints = [
            ("/mcp/tools/tokenize", {"text": "Hello, world! This is a test message."}),
            ("/mcp/tools/embed", {"text": "Hello, world! This is a test message."}),
            ("/mcp/tools/llm/chat", {"messages": [{"role": "user", "content": "Hello!"}]}),
        ]
        
        response_times = []
        successful_requests = 0
        failed_requests = 0
        
        start_time = time.time()
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            semaphore = asyncio.Semaphore(concurrent_requests)
            
            async def make_request(endpoint, data):
                nonlocal successful_requests, failed_requests
                
                async with semaphore:
                    request_start = time.time()
                    try:
                        response = await client.post(f"{self.base_url.replace('8000', '8001')}{endpoint}", json=data)
                        request_end = time.time()
                        
                        if response.status_code == 200:
                            successful_requests += 1
                            response_times.append(request_end - request_start)
                        else:
                            failed_requests += 1
                            logger.warning(f"Request to {endpoint} failed with status {response.status_code}")
                    
                    except Exception as e:
                        failed_requests += 1
                        logger.error(f"Request to {endpoint} failed with exception: {e}")
            
            # Execute requests across different endpoints
            tasks = []
            for i in range(num_requests):
                endpoint, data = endpoints[i % len(endpoints)]
                tasks.append(make_request(endpoint, data))
            
            await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Calculate statistics
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max_response_time
            p99_response_time = statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else max_response_time
        else:
            avg_response_time = min_response_time = max_response_time = 0
            p95_response_time = p99_response_time = 0
        
        requests_per_second = num_requests / total_duration if total_duration > 0 else 0
        error_rate = (failed_requests / num_requests) * 100 if num_requests > 0 else 0
        
        result = BenchmarkResult(
            test_name=test_name,
            total_requests=num_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            total_duration=total_duration,
            error_rate=error_rate
        )
        
        self.results.append(result)
        logger.info(f"Completed {test_name}: {requests_per_second:.2f} RPS, {avg_response_time:.3f}s avg response time")
        return result
    
    def benchmark_memory_usage(self) -> Dict[str, Any]:
        """Benchmark memory usage of core components."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        # Test memory usage with different operations
        from plugins.tokenizers.simple_tokenizer import SimpleTokenizer
        from plugins.embedders.simple_embedder import SimpleEmbedder
        from plugins.retrievers.in_memory_retriever import InMemoryRetriever
        
        initial_memory = memory_info.rss / 1024 / 1024  # MB
        
        # Test tokenizer memory usage
        tokenizer = SimpleTokenizer()
        large_text = "This is a test sentence. " * 1000
        tokens = tokenizer.tokenize(large_text)
        
        tokenizer_memory = process.memory_info().rss / 1024 / 1024 - initial_memory
        
        # Test embedder memory usage
        embedder = SimpleEmbedder()
        embeddings = embedder.embed(large_text)
        
        embedder_memory = process.memory_info().rss / 1024 / 1024 - initial_memory - tokenizer_memory
        
        # Test retriever memory usage
        retriever = InMemoryRetriever()
        for i in range(100):
            retriever.add_document(f"doc_{i}", f"Document {i} content: " + large_text)
        
        retriever_memory = process.memory_info().rss / 1024 / 1024 - initial_memory - tokenizer_memory - embedder_memory
        
        return {
            "initial_memory_mb": initial_memory,
            "tokenizer_memory_mb": tokenizer_memory,
            "embedder_memory_mb": embedder_memory,
            "retriever_memory_mb": retriever_memory,
            "total_memory_mb": process.memory_info().rss / 1024 / 1024
        }
    
    async def run_all_benchmarks(self) -> Dict[str, Any]:
        """Run all performance benchmarks."""
        logger.info("Starting comprehensive performance benchmarks")
        
        # Chat completions benchmarks
        await self.benchmark_chat_completions(num_requests=100, concurrent_requests=10, stream=False)
        await self.benchmark_chat_completions(num_requests=50, concurrent_requests=5, stream=True)
        
        # MCP tools benchmark (only if MCP server is running)
        try:
            await self.benchmark_mcp_tools(num_requests=30, concurrent_requests=3)
        except Exception as e:
            logger.warning(f"MCP benchmark failed (server may not be running): {e}")
        
        # Memory usage benchmark
        memory_stats = self.benchmark_memory_usage()
        
        # Compile results
        benchmark_summary = {
            "timestamp": time.time(),
            "results": [asdict(result) for result in self.results],
            "memory_stats": memory_stats,
            "slo_compliance": self._check_slo_compliance()
        }
        
        return benchmark_summary
    
    def _check_slo_compliance(self) -> Dict[str, bool]:
        """Check if results meet SLO requirements."""
        slo_compliance = {}
        
        for result in self.results:
            test_name = result.test_name
            
            # SLO requirements (example thresholds)
            slo_compliance[f"{test_name}_response_time"] = result.avg_response_time < 1.0  # < 1s avg
            slo_compliance[f"{test_name}_p95_response_time"] = result.p95_response_time < 2.0  # < 2s p95
            slo_compliance[f"{test_name}_error_rate"] = result.error_rate < 1.0  # < 1% error rate
            slo_compliance[f"{test_name}_throughput"] = result.requests_per_second > 10.0  # > 10 RPS
        
        return slo_compliance
    
    def save_results(self, filename: str = "benchmark_results.json"):
        """Save benchmark results to file."""
        summary = asyncio.run(self.run_all_benchmarks()) if not self.results else {
            "timestamp": time.time(),
            "results": [asdict(result) for result in self.results],
            "slo_compliance": self._check_slo_compliance()
        }
        
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)
        
        logger.info(f"Benchmark results saved to {filename}")


async def main():
    """Main benchmark execution."""
    benchmark = PerformanceBenchmark()
    
    try:
        results = await benchmark.run_all_benchmarks()
        
        # Print summary
        print("\n" + "="*60)
        print("PERFORMANCE BENCHMARK RESULTS")
        print("="*60)
        
        for result_data in results["results"]:
            result = BenchmarkResult(**result_data)
            print(f"\n{result.test_name.upper()}:")
            print(f"  Total Requests: {result.total_requests}")
            print(f"  Successful: {result.successful_requests}")
            print(f"  Failed: {result.failed_requests}")
            print(f"  Error Rate: {result.error_rate:.2f}%")
            print(f"  Avg Response Time: {result.avg_response_time:.3f}s")
            print(f"  P95 Response Time: {result.p95_response_time:.3f}s")
            print(f"  P99 Response Time: {result.p99_response_time:.3f}s")
            print(f"  Requests/Second: {result.requests_per_second:.2f}")
        
        print(f"\nMEMORY USAGE:")
        memory = results["memory_stats"]
        for key, value in memory.items():
            print(f"  {key}: {value:.2f}")
        
        print(f"\nSLO COMPLIANCE:")
        slo = results["slo_compliance"]
        for key, passed in slo.items():
            status = "✓ PASS" if passed else "✗ FAIL"
            print(f"  {key}: {status}")
        
        # Save results
        benchmark.save_results("benchmark_results.json")
        
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(asyncio.run(main()))
