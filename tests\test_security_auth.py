"""Tests for authentication and authorization system."""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, patch

from core.security.auth import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, AuthManager, User, UserRole, Permission,
    AuthToken, AuthenticationError, AuthorizationError
)
from core.security.audit import AuditLogger


class TestJWTAuthProvider:
    """Test JWT authentication provider."""
    
    @pytest.fixture
    def auth_provider(self):
        """Create JWT auth provider for testing."""
        return JWTAuthProvider(
            secret_key="test_secret_key",
            token_expiry_hours=1
        )
    
    @pytest.mark.asyncio
    async def test_create_user(self, auth_provider):
        """Test user creation."""
        user = await auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        assert user.username == "testuser"
        assert user.email == "<EMAIL>"
        assert UserRole.DEVELOPER in user.roles
        assert Permission.AGENT_EXECUTE in user.permissions
        assert user.is_active
    
    @pytest.mark.asyncio
    async def test_authenticate_valid_user(self, auth_provider):
        """Test authentication with valid credentials."""
        # Create user
        await auth_provider.create_user(
            username="testuser",
            email="<EMAIL>", 
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        # Authenticate
        user = await auth_provider.authenticate({
            "username": "testuser",
            "password": "secure_password"
        })
        
        assert user is not None
        assert user.username == "testuser"
        assert user.last_login is not None
    
    @pytest.mark.asyncio
    async def test_authenticate_invalid_password(self, auth_provider):
        """Test authentication with invalid password."""
        # Create user
        await auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password", 
            roles=[UserRole.DEVELOPER]
        )
        
        # Try to authenticate with wrong password
        user = await auth_provider.authenticate({
            "username": "testuser",
            "password": "wrong_password"
        })
        
        assert user is None
    
    @pytest.mark.asyncio
    async def test_authenticate_nonexistent_user(self, auth_provider):
        """Test authentication with non-existent user."""
        user = await auth_provider.authenticate({
            "username": "nonexistent",
            "password": "any_password"
        })
        
        assert user is None
    
    @pytest.mark.asyncio
    async def test_create_token(self, auth_provider):
        """Test JWT token creation."""
        # Create user
        user = await auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        # Create token
        token = await auth_provider.create_token(user, scopes=["read", "write"])
        
        assert isinstance(token, AuthToken)
        assert token.user_id == user.id
        assert token.scopes == ["read", "write"]
        assert not token.is_expired()
        assert token.token_type == "bearer"
    
    @pytest.mark.asyncio
    async def test_validate_token(self, auth_provider):
        """Test JWT token validation."""
        # Create user and token
        user = await auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        token = await auth_provider.create_token(user)
        
        # Validate token
        validated_token = await auth_provider.validate_token(token.token)
        
        assert validated_token is not None
        assert validated_token.user_id == user.id
        assert not validated_token.is_expired()
    
    @pytest.mark.asyncio
    async def test_validate_invalid_token(self, auth_provider):
        """Test validation of invalid token."""
        invalid_token = "invalid.jwt.token"
        
        validated_token = await auth_provider.validate_token(invalid_token)
        assert validated_token is None
    
    def test_password_hashing(self, auth_provider):
        """Test password hashing and verification."""
        password = "test_password"
        
        # Hash password
        hashed = auth_provider._hash_password(password)
        assert hashed != password
        assert len(hashed) > 64  # Salt + hash
        
        # Verify password
        assert auth_provider._verify_password(password, hashed)
        assert not auth_provider._verify_password("wrong_password", hashed)
    
    def test_role_permissions(self, auth_provider):
        """Test role-based permission assignment."""
        # Admin should have all permissions
        admin_perms = auth_provider.role_permissions[UserRole.ADMIN]
        assert Permission.SYSTEM_CONFIG in admin_perms
        assert Permission.PLUGIN_MANAGE in admin_perms
        
        # Viewer should have limited permissions
        viewer_perms = auth_provider.role_permissions[UserRole.VIEWER]
        assert Permission.PLUGIN_VIEW in viewer_perms
        assert Permission.SYSTEM_CONFIG not in viewer_perms


class TestAuthManager:
    """Test authentication manager."""
    
    @pytest.fixture
    def auth_manager(self):
        """Create auth manager for testing."""
        auth_provider = JWTAuthProvider("test_secret")
        return AuthManager(auth_provider)
    
    @pytest.mark.asyncio
    async def test_authenticate_user(self, auth_manager):
        """Test user authentication through manager."""
        # Create user
        await auth_manager.auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        # Authenticate
        token = await auth_manager.authenticate_user({
            "username": "testuser",
            "password": "secure_password"
        })
        
        assert token is not None
        assert token.token in auth_manager.active_sessions
    
    @pytest.mark.asyncio
    async def test_validate_token(self, auth_manager):
        """Test token validation through manager."""
        # Create user and authenticate
        await auth_manager.auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        token = await auth_manager.authenticate_user({
            "username": "testuser",
            "password": "secure_password"
        })
        
        # Validate token
        user = await auth_manager.validate_token(token.token)
        assert user is not None
        assert user.username == "testuser"
    
    @pytest.mark.asyncio
    async def test_authorize_action(self, auth_manager):
        """Test action authorization."""
        # Create user with developer role
        user = await auth_manager.auth_provider.create_user(
            username="developer",
            email="<EMAIL>",
            password="password",
            roles=[UserRole.DEVELOPER]
        )
        
        # Test authorized action
        authorized = await auth_manager.authorize_action(
            user, Permission.AGENT_EXECUTE
        )
        assert authorized
        
        # Test unauthorized action
        unauthorized = await auth_manager.authorize_action(
            user, Permission.SYSTEM_CONFIG
        )
        assert not unauthorized
    
    @pytest.mark.asyncio
    async def test_logout_user(self, auth_manager):
        """Test user logout."""
        # Create user and authenticate
        await auth_manager.auth_provider.create_user(
            username="testuser",
            email="<EMAIL>",
            password="secure_password",
            roles=[UserRole.DEVELOPER]
        )
        
        token = await auth_manager.authenticate_user({
            "username": "testuser",
            "password": "secure_password"
        })
        
        # Logout
        success = await auth_manager.logout_user(token.token)
        assert success
        assert token.token not in auth_manager.active_sessions
        
        # Try to logout again
        success = await auth_manager.logout_user(token.token)
        assert not success
    
    def test_get_active_sessions(self, auth_manager):
        """Test getting active sessions."""
        sessions = auth_manager.get_active_sessions()
        assert isinstance(sessions, dict)


class TestUser:
    """Test User model."""
    
    def test_user_creation(self):
        """Test user model creation."""
        user = User(
            id="user123",
            username="testuser",
            email="<EMAIL>",
            roles=[UserRole.DEVELOPER],
            permissions={Permission.AGENT_EXECUTE, Permission.WORKFLOW_EXECUTE},
            created_at=datetime.utcnow()
        )
        
        assert user.id == "user123"
        assert user.username == "testuser"
        assert user.is_active
        assert user.has_role(UserRole.DEVELOPER)
        assert user.has_permission(Permission.AGENT_EXECUTE)
        assert not user.has_permission(Permission.SYSTEM_CONFIG)
    
    def test_user_permissions(self):
        """Test user permission checking."""
        user = User(
            id="user123",
            username="testuser",
            email="<EMAIL>",
            roles=[UserRole.ADMIN],
            permissions={Permission.SYSTEM_CONFIG, Permission.PLUGIN_MANAGE},
            created_at=datetime.utcnow()
        )
        
        assert user.has_permission(Permission.SYSTEM_CONFIG)
        assert user.has_permission(Permission.PLUGIN_MANAGE)
        assert not user.has_permission(Permission.AGENT_EXECUTE)


class TestAuthToken:
    """Test AuthToken model."""
    
    def test_token_creation(self):
        """Test token creation."""
        expires_at = datetime.utcnow() + timedelta(hours=1)
        token = AuthToken(
            token="test.jwt.token",
            user_id="user123",
            expires_at=expires_at,
            scopes=["read", "write"]
        )
        
        assert token.token == "test.jwt.token"
        assert token.user_id == "user123"
        assert not token.is_expired()
        assert token.is_valid_for_scope("read")
        assert token.is_valid_for_scope("write")
        assert not token.is_valid_for_scope("admin")
    
    def test_token_expiration(self):
        """Test token expiration."""
        # Expired token
        expires_at = datetime.utcnow() - timedelta(hours=1)
        token = AuthToken(
            token="expired.jwt.token",
            user_id="user123",
            expires_at=expires_at,
            scopes=["read"]
        )
        
        assert token.is_expired()
        assert not token.is_valid_for_scope("read")  # Expired tokens are invalid
    
    def test_token_scopes(self):
        """Test token scope validation."""
        token = AuthToken(
            token="scoped.jwt.token",
            user_id="user123",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            scopes=["read"]
        )
        
        assert token.is_valid_for_scope("read")
        assert not token.is_valid_for_scope("write")
        
        # Token with no scopes should allow any scope
        unrestricted_token = AuthToken(
            token="unrestricted.jwt.token",
            user_id="user123",
            expires_at=datetime.utcnow() + timedelta(hours=1),
            scopes=[]
        )
        
        assert unrestricted_token.is_valid_for_scope("any_scope")


@pytest.mark.asyncio
async def test_auth_system_integration():
    """Test complete authentication system integration."""
    # Create auth system
    auth_provider = JWTAuthProvider("integration_test_secret")
    auth_manager = AuthManager(auth_provider)
    
    # Create users with different roles
    admin = await auth_provider.create_user(
        username="admin",
        email="<EMAIL>",
        password="admin_password",
        roles=[UserRole.ADMIN]
    )
    
    developer = await auth_provider.create_user(
        username="developer", 
        email="<EMAIL>",
        password="dev_password",
        roles=[UserRole.DEVELOPER]
    )
    
    viewer = await auth_provider.create_user(
        username="viewer",
        email="<EMAIL>",
        password="viewer_password",
        roles=[UserRole.VIEWER]
    )
    
    # Test authentication
    admin_token = await auth_manager.authenticate_user({
        "username": "admin",
        "password": "admin_password"
    })
    assert admin_token is not None
    
    # Test authorization
    admin_user = await auth_manager.validate_token(admin_token.token)
    assert await auth_manager.authorize_action(admin_user, Permission.SYSTEM_CONFIG)
    assert await auth_manager.authorize_action(admin_user, Permission.PLUGIN_MANAGE)
    
    developer_token = await auth_manager.authenticate_user({
        "username": "developer",
        "password": "dev_password"
    })
    developer_user = await auth_manager.validate_token(developer_token.token)
    assert await auth_manager.authorize_action(developer_user, Permission.AGENT_EXECUTE)
    assert not await auth_manager.authorize_action(developer_user, Permission.SYSTEM_CONFIG)
    
    viewer_token = await auth_manager.authenticate_user({
        "username": "viewer",
        "password": "viewer_password"
    })
    viewer_user = await auth_manager.validate_token(viewer_token.token)
    assert await auth_manager.authorize_action(viewer_user, Permission.PLUGIN_VIEW)
    assert not await auth_manager.authorize_action(viewer_user, Permission.AGENT_EXECUTE)
    
    # Test session management
    sessions = auth_manager.get_active_sessions()
    assert len(sessions) == 3
    
    # Test logout
    await auth_manager.logout_user(admin_token.token)
    sessions = auth_manager.get_active_sessions()
    assert len(sessions) == 2
