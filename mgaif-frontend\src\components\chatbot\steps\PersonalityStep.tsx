import React from 'react';
import { Form, Select, Input, Card, Typography, Space, Tag, Divider } from 'antd';
import type { ChatbotPersonality } from '../../../types/chatbot';

const { TextArea } = Input;
const { Option } = Select;

interface PersonalityStepProps {
  initialValues?: Partial<ChatbotPersonality>;
  onValuesChange?: (values: Partial<ChatbotPersonality>) => void;
  form: any;
}

const toneOptions = [
  { value: 'professional', label: 'Professional', description: 'Formal and business-like' },
  { value: 'friendly', label: 'Friendly', description: 'Warm and approachable' },
  { value: 'casual', label: 'Casual', description: 'Relaxed and informal' },
  { value: 'formal', label: 'Formal', description: 'Very structured and proper' },
  { value: 'enthusiastic', label: 'Enthusiastic', description: 'Energetic and excited' },
  { value: 'empathetic', label: 'Empathetic', description: 'Understanding and caring' },
];

const styleOptions = [
  { value: 'concise', label: 'Concise', description: 'Brief and to the point' },
  { value: 'detailed', label: 'Detailed', description: 'Comprehensive explanations' },
  { value: 'conversational', label: 'Conversational', description: 'Natural dialogue style' },
  { value: 'technical', label: 'Technical', description: 'Precise and technical' },
];

const languageOptions = [
  { value: 'en', label: 'English' },
  { value: 'es', label: 'Spanish' },
  { value: 'fr', label: 'French' },
  { value: 'de', label: 'German' },
  { value: 'it', label: 'Italian' },
  { value: 'pt', label: 'Portuguese' },
  { value: 'zh', label: 'Chinese' },
  { value: 'ja', label: 'Japanese' },
];

export const PersonalityStep: React.FC<PersonalityStepProps> = ({
  initialValues,
  onValuesChange,
  form,
}) => {
  const selectedTone = Form.useWatch('tone', form);
  const selectedStyle = Form.useWatch('style', form);

  return (
    <Card title="Personality & Tone" style={{ maxWidth: 600, margin: '0 auto' }}>
      <Typography.Paragraph type="secondary" style={{ marginBottom: '24px' }}>
        Define how your chatbot communicates with users. This affects the tone, style, and personality of all responses.
      </Typography.Paragraph>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={onValuesChange}
        size="large"
      >
        {/* Tone Selection */}
        <Form.Item
          label="Tone of Voice"
          name="tone"
          rules={[{ required: true, message: 'Please select a tone' }]}
        >
          <Select placeholder="Select the tone for your chatbot">
            {toneOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <div>
                  <div style={{ fontWeight: 'bold' }}>{option.label}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {option.description}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Style Selection */}
        <Form.Item
          label="Communication Style"
          name="style"
          rules={[{ required: true, message: 'Please select a style' }]}
        >
          <Select placeholder="Select the communication style">
            {styleOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                <div>
                  <div style={{ fontWeight: 'bold' }}>{option.label}</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {option.description}
                  </div>
                </div>
              </Option>
            ))}
          </Select>
        </Form.Item>

        {/* Language Selection */}
        <Form.Item
          label="Primary Language"
          name="language"
          rules={[{ required: true, message: 'Please select a language' }]}
        >
          <Select placeholder="Select the primary language">
            {languageOptions.map((option) => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>

        <Divider />

        {/* Custom Messages */}
        <Typography.Title level={5}>Custom Messages</Typography.Title>

        <Form.Item
          label="Greeting Message"
          name="greeting"
          rules={[
            { required: true, message: 'Please enter a greeting message' },
            { max: 200, message: 'Greeting must be less than 200 characters' },
          ]}
        >
          <TextArea
            rows={2}
            placeholder="Hello! How can I help you today?"
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          label="Fallback Message"
          name="fallbackMessage"
          rules={[
            { required: true, message: 'Please enter a fallback message' },
            { max: 200, message: 'Fallback message must be less than 200 characters' },
          ]}
        >
          <TextArea
            rows={2}
            placeholder="I'm sorry, I don't understand. Could you please rephrase your question?"
            showCount
            maxLength={200}
          />
        </Form.Item>

        <Form.Item
          label="Custom Instructions (Optional)"
          name="customInstructions"
        >
          <TextArea
            rows={3}
            placeholder="Add any specific instructions for how your chatbot should behave..."
            showCount
            maxLength={500}
          />
        </Form.Item>

        {/* Preview */}
        {(selectedTone || selectedStyle) && (
          <div style={{ marginTop: '32px' }}>
            <Typography.Title level={5}>Personality Preview</Typography.Title>
            <Card size="small" style={{ backgroundColor: '#f8f9fa' }}>
              <Space wrap>
                {selectedTone && (
                  <Tag color="blue">Tone: {toneOptions.find(t => t.value === selectedTone)?.label}</Tag>
                )}
                {selectedStyle && (
                  <Tag color="green">Style: {styleOptions.find(s => s.value === selectedStyle)?.label}</Tag>
                )}
              </Space>
              <Typography.Paragraph style={{ marginTop: '12px', marginBottom: 0, fontSize: '12px' }}>
                Your chatbot will communicate in a{' '}
                <strong>{selectedTone}</strong> tone with a{' '}
                <strong>{selectedStyle}</strong> style.
              </Typography.Paragraph>
            </Card>
          </div>
        )}

        {/* Example Responses */}
        <div style={{ marginTop: '24px' }}>
          <Typography.Title level={5}>Example Responses</Typography.Title>
          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
            Here's how your chatbot might respond with the selected personality:
          </Typography.Text>
          
          <div style={{ marginTop: '12px' }}>
            {[
              {
                question: "What are your business hours?",
                professional: "Our business hours are Monday through Friday, 9 AM to 5 PM EST.",
                friendly: "We're open Monday through Friday from 9 AM to 5 PM EST. Feel free to reach out anytime!",
                casual: "Hey! We're open Mon-Fri, 9 to 5 EST. Hit us up during those hours!",
              },
              {
                question: "I need help with my account",
                professional: "I'd be happy to assist you with your account. Could you please specify what type of help you need?",
                friendly: "I'm here to help with your account! What specific issue are you experiencing?",
                casual: "Sure thing! What's going on with your account? I'll help you sort it out.",
              },
            ].map((example, index) => (
              <Card key={index} size="small" style={{ marginBottom: '12px' }}>
                <Typography.Text strong style={{ fontSize: '12px' }}>
                  User: {example.question}
                </Typography.Text>
                <br />
                <Typography.Text style={{ fontSize: '12px', color: '#1890ff' }}>
                  Bot: {selectedTone ? example[selectedTone as keyof typeof example] || example.professional : example.professional}
                </Typography.Text>
              </Card>
            ))}
          </div>
        </div>
      </Form>
    </Card>
  );
};
