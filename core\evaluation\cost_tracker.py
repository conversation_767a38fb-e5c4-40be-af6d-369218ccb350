"""Cost tracking for model evaluation and usage.

This module provides comprehensive cost tracking for LLM usage, including
token counting, pricing calculations, and budget management.

The cost tracking system provides:
- Token usage monitoring across different models
- Dynamic pricing based on model and operation type
- Budget alerts and limits
- Cost optimization recommendations
- Detailed cost breakdowns and reporting

Key Components:
- CostTracker: Main cost tracking interface
- CostReport: Detailed cost analysis and reporting
- BudgetManager: Budget monitoring and alerts

Example:
    >>> from core.evaluation import CostTracker
    >>> tracker = CostTracker()
    >>> 
    >>> # Track model usage
    >>> await tracker.track_usage("gpt-4", input_tokens=100, output_tokens=50)
    >>> 
    >>> # Get cost report
    >>> report = tracker.generate_report()
    >>> print(f"Total cost: ${report.total_cost:.4f}")
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json

logger = logging.getLogger(__name__)


class ModelTier(Enum):
    """Model pricing tiers."""
    FREE = "free"
    BASIC = "basic"
    PREMIUM = "premium"
    ENTERPRISE = "enterprise"


@dataclass
class ModelPricing:
    """Pricing information for a model."""
    model_name: str
    input_cost_per_1k_tokens: float
    output_cost_per_1k_tokens: float
    tier: ModelTier = ModelTier.BASIC
    context_window: int = 4096
    supports_streaming: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class UsageRecord:
    """Record of model usage."""
    model_name: str
    operation_type: str  # chat, completion, embedding, etc.
    input_tokens: int
    output_tokens: int
    input_cost: float
    output_cost: float
    total_cost: float
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class CostReport:
    """Comprehensive cost analysis report."""
    
    # Time period
    start_date: datetime
    end_date: datetime
    
    # Total costs
    total_cost: float = 0.0
    total_input_tokens: int = 0
    total_output_tokens: int = 0
    total_requests: int = 0
    
    # Per-model breakdown
    model_costs: Dict[str, float] = field(default_factory=dict)
    model_tokens: Dict[str, Dict[str, int]] = field(default_factory=dict)
    model_requests: Dict[str, int] = field(default_factory=dict)
    
    # Per-operation breakdown
    operation_costs: Dict[str, float] = field(default_factory=dict)
    operation_tokens: Dict[str, Dict[str, int]] = field(default_factory=dict)
    
    # Daily breakdown
    daily_costs: Dict[str, float] = field(default_factory=dict)
    
    # Cost efficiency metrics
    avg_cost_per_request: float = 0.0
    avg_cost_per_token: float = 0.0
    most_expensive_model: str = ""
    most_used_model: str = ""
    
    # Recommendations
    recommendations: List[str] = field(default_factory=list)
    
    def get_summary(self) -> Dict[str, Any]:
        """Get cost report summary."""
        return {
            "period": f"{self.start_date.date()} to {self.end_date.date()}",
            "total_cost": self.total_cost,
            "total_tokens": self.total_input_tokens + self.total_output_tokens,
            "total_requests": self.total_requests,
            "avg_cost_per_request": self.avg_cost_per_request,
            "most_expensive_model": self.most_expensive_model,
            "most_used_model": self.most_used_model,
            "top_models": dict(sorted(self.model_costs.items(), key=lambda x: x[1], reverse=True)[:5])
        }


class BudgetManager:
    """Budget monitoring and alerting system."""
    
    def __init__(self, daily_budget: float = 100.0, monthly_budget: float = 2000.0):
        self.daily_budget = daily_budget
        self.monthly_budget = monthly_budget
        self.alerts_enabled = True
        self.alert_thresholds = [0.5, 0.8, 0.9, 1.0]  # 50%, 80%, 90%, 100%
        self.triggered_alerts: Dict[str, List[float]] = {}
    
    def check_budget_status(self, current_cost: float, period: str = "daily") -> Dict[str, Any]:
        """Check current budget status."""
        budget = self.daily_budget if period == "daily" else self.monthly_budget
        usage_ratio = current_cost / budget if budget > 0 else 0.0
        
        status = {
            "period": period,
            "budget": budget,
            "current_cost": current_cost,
            "remaining_budget": max(0, budget - current_cost),
            "usage_ratio": usage_ratio,
            "status": "ok"
        }
        
        if usage_ratio >= 1.0:
            status["status"] = "over_budget"
        elif usage_ratio >= 0.9:
            status["status"] = "critical"
        elif usage_ratio >= 0.8:
            status["status"] = "warning"
        elif usage_ratio >= 0.5:
            status["status"] = "caution"
        
        return status
    
    def should_alert(self, usage_ratio: float, period: str) -> bool:
        """Check if an alert should be triggered."""
        if not self.alerts_enabled:
            return False
        
        period_alerts = self.triggered_alerts.get(period, [])
        
        for threshold in self.alert_thresholds:
            if usage_ratio >= threshold and threshold not in period_alerts:
                if period not in self.triggered_alerts:
                    self.triggered_alerts[period] = []
                self.triggered_alerts[period].append(threshold)
                return True
        
        return False
    
    def reset_alerts(self, period: str) -> None:
        """Reset alerts for a period (e.g., at start of new day/month)."""
        if period in self.triggered_alerts:
            del self.triggered_alerts[period]


class CostTracker:
    """Main cost tracking system."""
    
    def __init__(self, budget_manager: Optional[BudgetManager] = None):
        self.budget_manager = budget_manager or BudgetManager()
        self.usage_records: List[UsageRecord] = []
        self.model_pricing: Dict[str, ModelPricing] = {}
        self.total_cost = 0.0
        
        # Initialize default pricing
        self._initialize_default_pricing()
        
        logger.info("Cost tracker initialized")
    
    def _initialize_default_pricing(self) -> None:
        """Initialize default model pricing."""
        default_models = [
            ModelPricing("gpt-4", 0.03, 0.06, ModelTier.PREMIUM, 8192),
            ModelPricing("gpt-4-32k", 0.06, 0.12, ModelTier.PREMIUM, 32768),
            ModelPricing("gpt-3.5-turbo", 0.0015, 0.002, ModelTier.BASIC, 4096),
            ModelPricing("gpt-3.5-turbo-16k", 0.003, 0.004, ModelTier.BASIC, 16384),
            ModelPricing("claude-3-opus", 0.015, 0.075, ModelTier.PREMIUM, 200000),
            ModelPricing("claude-3-sonnet", 0.003, 0.015, ModelTier.BASIC, 200000),
            ModelPricing("claude-3-haiku", 0.00025, 0.00125, ModelTier.BASIC, 200000),
            ModelPricing("text-embedding-ada-002", 0.0001, 0.0, ModelTier.BASIC, 8191),
            ModelPricing("text-embedding-3-small", 0.00002, 0.0, ModelTier.BASIC, 8191),
            ModelPricing("text-embedding-3-large", 0.00013, 0.0, ModelTier.BASIC, 8191),
        ]
        
        for pricing in default_models:
            self.model_pricing[pricing.model_name] = pricing
    
    def add_model_pricing(self, pricing: ModelPricing) -> None:
        """Add or update pricing for a model."""
        self.model_pricing[pricing.model_name] = pricing
        logger.info(f"Added pricing for {pricing.model_name}")
    
    def get_model_pricing(self, model_name: str) -> Optional[ModelPricing]:
        """Get pricing information for a model."""
        return self.model_pricing.get(model_name)
    
    async def track_usage(self, model_name: str, input_tokens: int, output_tokens: int = 0,
                         operation_type: str = "chat", metadata: Optional[Dict[str, Any]] = None) -> UsageRecord:
        """Track model usage and calculate costs."""
        pricing = self.model_pricing.get(model_name)
        
        if not pricing:
            logger.warning(f"No pricing found for model {model_name}, using default rates")
            # Use default pricing
            input_cost = input_tokens * 0.001 / 1000  # $0.001 per 1K tokens
            output_cost = output_tokens * 0.002 / 1000  # $0.002 per 1K tokens
        else:
            input_cost = input_tokens * pricing.input_cost_per_1k_tokens / 1000
            output_cost = output_tokens * pricing.output_cost_per_1k_tokens / 1000
        
        total_cost = input_cost + output_cost
        
        # Create usage record
        record = UsageRecord(
            model_name=model_name,
            operation_type=operation_type,
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            input_cost=input_cost,
            output_cost=output_cost,
            total_cost=total_cost,
            metadata=metadata or {}
        )
        
        # Store record
        self.usage_records.append(record)
        self.total_cost += total_cost
        
        # Check budget alerts
        await self._check_budget_alerts()
        
        logger.debug(f"Tracked usage: {model_name} - ${total_cost:.6f} ({input_tokens}+{output_tokens} tokens)")
        return record
    
    async def _check_budget_alerts(self) -> None:
        """Check and trigger budget alerts if necessary."""
        today = datetime.now().date()
        current_month = today.replace(day=1)
        
        # Calculate daily cost
        daily_cost = sum(
            record.total_cost for record in self.usage_records
            if record.timestamp.date() == today
        )
        
        # Calculate monthly cost
        monthly_cost = sum(
            record.total_cost for record in self.usage_records
            if record.timestamp.date() >= current_month
        )
        
        # Check daily budget
        daily_status = self.budget_manager.check_budget_status(daily_cost, "daily")
        if self.budget_manager.should_alert(daily_status["usage_ratio"], f"daily_{today}"):
            logger.warning(f"Daily budget alert: {daily_status['usage_ratio']:.1%} of budget used (${daily_cost:.4f}/${daily_status['budget']})")
        
        # Check monthly budget
        monthly_status = self.budget_manager.check_budget_status(monthly_cost, "monthly")
        if self.budget_manager.should_alert(monthly_status["usage_ratio"], f"monthly_{current_month}"):
            logger.warning(f"Monthly budget alert: {monthly_status['usage_ratio']:.1%} of budget used (${monthly_cost:.4f}/${monthly_status['budget']})")
    
    def generate_report(self, start_date: Optional[datetime] = None, 
                       end_date: Optional[datetime] = None) -> CostReport:
        """Generate comprehensive cost report."""
        if not start_date:
            start_date = datetime.now() - timedelta(days=30)
        if not end_date:
            end_date = datetime.now()
        
        # Filter records by date range
        filtered_records = [
            record for record in self.usage_records
            if start_date <= record.timestamp <= end_date
        ]
        
        if not filtered_records:
            return CostReport(start_date=start_date, end_date=end_date)
        
        report = CostReport(start_date=start_date, end_date=end_date)
        
        # Calculate totals
        report.total_cost = sum(record.total_cost for record in filtered_records)
        report.total_input_tokens = sum(record.input_tokens for record in filtered_records)
        report.total_output_tokens = sum(record.output_tokens for record in filtered_records)
        report.total_requests = len(filtered_records)
        
        # Per-model breakdown
        for record in filtered_records:
            model = record.model_name
            
            # Costs
            if model not in report.model_costs:
                report.model_costs[model] = 0.0
            report.model_costs[model] += record.total_cost
            
            # Tokens
            if model not in report.model_tokens:
                report.model_tokens[model] = {"input": 0, "output": 0}
            report.model_tokens[model]["input"] += record.input_tokens
            report.model_tokens[model]["output"] += record.output_tokens
            
            # Requests
            if model not in report.model_requests:
                report.model_requests[model] = 0
            report.model_requests[model] += 1
        
        # Per-operation breakdown
        for record in filtered_records:
            operation = record.operation_type
            
            if operation not in report.operation_costs:
                report.operation_costs[operation] = 0.0
                report.operation_tokens[operation] = {"input": 0, "output": 0}
            
            report.operation_costs[operation] += record.total_cost
            report.operation_tokens[operation]["input"] += record.input_tokens
            report.operation_tokens[operation]["output"] += record.output_tokens
        
        # Daily breakdown
        for record in filtered_records:
            day = record.timestamp.date().isoformat()
            if day not in report.daily_costs:
                report.daily_costs[day] = 0.0
            report.daily_costs[day] += record.total_cost
        
        # Calculate efficiency metrics
        if report.total_requests > 0:
            report.avg_cost_per_request = report.total_cost / report.total_requests
        
        total_tokens = report.total_input_tokens + report.total_output_tokens
        if total_tokens > 0:
            report.avg_cost_per_token = report.total_cost / total_tokens
        
        # Find most expensive and most used models
        if report.model_costs:
            report.most_expensive_model = max(report.model_costs, key=report.model_costs.get)
        if report.model_requests:
            report.most_used_model = max(report.model_requests, key=report.model_requests.get)
        
        # Generate recommendations
        report.recommendations = self._generate_recommendations(report)
        
        return report
    
    def _generate_recommendations(self, report: CostReport) -> List[str]:
        """Generate cost optimization recommendations."""
        recommendations = []
        
        # High-cost model recommendations
        if report.model_costs:
            expensive_models = {k: v for k, v in report.model_costs.items() if v > report.total_cost * 0.3}
            for model, cost in expensive_models.items():
                if "gpt-4" in model.lower():
                    recommendations.append(f"Consider using GPT-3.5-turbo instead of {model} for simpler tasks (potential 90% cost reduction)")
                elif "claude-3-opus" in model.lower():
                    recommendations.append(f"Consider using Claude-3-Sonnet instead of {model} for most tasks (potential 80% cost reduction)")
        
        # Token efficiency recommendations
        if report.avg_cost_per_token > 0.0001:  # High cost per token
            recommendations.append("Consider optimizing prompts to reduce token usage")
            recommendations.append("Implement response caching for repeated queries")
        
        # Usage pattern recommendations
        if len(report.model_costs) > 5:
            recommendations.append("Consider consolidating to fewer models to simplify cost management")
        
        # Budget recommendations
        daily_avg = report.total_cost / max(1, (report.end_date - report.start_date).days)
        if daily_avg > 50:  # High daily spend
            recommendations.append("Consider implementing request rate limiting to control costs")
            recommendations.append("Set up automated budget alerts and limits")
        
        return recommendations
    
    def get_current_costs(self) -> Dict[str, float]:
        """Get current cost breakdown."""
        today = datetime.now().date()
        current_month = today.replace(day=1)
        
        daily_cost = sum(
            record.total_cost for record in self.usage_records
            if record.timestamp.date() == today
        )
        
        monthly_cost = sum(
            record.total_cost for record in self.usage_records
            if record.timestamp.date() >= current_month
        )
        
        return {
            "daily_cost": daily_cost,
            "monthly_cost": monthly_cost,
            "total_cost": self.total_cost
        }
    
    def export_usage_data(self, filepath: str, format: str = "json") -> None:
        """Export usage data to file."""
        data = [
            {
                "model_name": record.model_name,
                "operation_type": record.operation_type,
                "input_tokens": record.input_tokens,
                "output_tokens": record.output_tokens,
                "total_cost": record.total_cost,
                "timestamp": record.timestamp.isoformat(),
                "metadata": record.metadata
            }
            for record in self.usage_records
        ]
        
        if format == "json":
            with open(filepath, 'w') as f:
                json.dump(data, f, indent=2)
        elif format == "csv":
            import csv
            with open(filepath, 'w', newline='') as f:
                if data:
                    writer = csv.DictWriter(f, fieldnames=data[0].keys())
                    writer.writeheader()
                    writer.writerows(data)
        else:
            raise ValueError(f"Unsupported export format: {format}")
        
        logger.info(f"Exported {len(data)} usage records to {filepath}")
    
    def clear_usage_data(self, before_date: Optional[datetime] = None) -> int:
        """Clear usage data before a certain date."""
        if not before_date:
            before_date = datetime.now() - timedelta(days=90)  # Keep last 90 days
        
        initial_count = len(self.usage_records)
        self.usage_records = [
            record for record in self.usage_records
            if record.timestamp >= before_date
        ]
        
        # Recalculate total cost
        self.total_cost = sum(record.total_cost for record in self.usage_records)
        
        cleared_count = initial_count - len(self.usage_records)
        logger.info(f"Cleared {cleared_count} usage records before {before_date.date()}")
        return cleared_count
