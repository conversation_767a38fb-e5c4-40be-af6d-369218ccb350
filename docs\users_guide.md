# User Guide

This guide helps you run the Modular GenAI Framework backend and the Web Console, and perform common tasks without writing code.

## Prerequisites

- Python 3.11
- Node.js 18+
- Optional: Ollama (for local LLMs)

## Start the Backend

1) Install dependencies
```powershell
pip install -r requirements.txt
```

2) Start the API (Edge+MCP are both served by FastAPI apps)
- Edge API (OpenAI-compatible):
```powershell
uvicorn core.edge.api:app --reload --port 8000
```
- MCP API (tools & workflows):
```powershell
uvicorn core.mcp.api:app --reload --port 8000
```

3) Optional configuration
- Choose LLM adapter for Edge:
```powershell
$env:MGAIF_EDGE_ADAPTER = "echo"  # or "ollama"
```
- Choose LLM adapter for MCP:
```powershell
$env:MGAIF_MCP_ADAPTER = "echo"  # or "ollama"
```
- Enable conservative prompt-injection blocking:
```powershell
$env:MGAIF_SECURITY_STRICT = "true"
```
- Enable tracing (OpenTelemetry):
```powershell
$env:OTEL_EXPORTER_OTLP_ENDPOINT = "http://localhost:4318"
```

- Metrics are available at:
```
GET http://127.0.0.1:8000/metrics
```

## Start the Web Console

In a new terminal:
```powershell
# from webapp/
npm install
npm run dev
```
Open http://127.0.0.1:5173 in your browser.

If the app cannot auto-detect the API, open the Settings page and set API Base to:
```
http://127.0.0.1:8000
```

## Common Tasks

- LLM Tester
  - Choose backend: Edge (streaming) or MCP (non-OpenAI tool)
  - Enter a prompt and send; for Ollama, ensure Ollama is running and model name is correct (e.g., "llama3")

- Run a Workflow
  - Go to Workflows page
  - Load a template or paste your YAML (see examples in `configs/workflows/`)
  - Click Run; inspect the resulting JSON state

- Manage a RAG Store
  - Open RAG Stores page
  - Index documents (one per line)
  - Search with a query; adjust `top_k` if needed

## Using Ollama (optional)

Install Ollama and pull a model:
```bash
ollama pull llama3
```
Set environment variables as needed:
```powershell
$env:OLLAMA_MODEL = "llama3"
# $env:OLLAMA_BASE_URL = "http://127.0.0.1:11434"  # default
```

## Docker Compose (Edge + Observability)

Run a local stack with Edge API, OpenTelemetry Collector, and Prometheus:
```powershell
docker compose -f deploy/docker-compose.yml up --build
```
- Edge API: http://localhost:8000
- Metrics: http://localhost:8000/metrics
- Prometheus: http://localhost:9090

## Troubleshooting

- Backend connection failed
  - Verify the backend is running on port 8000
  - Check API Base URL in the webapp Settings
- Streaming not working
  - Use Edge mode in LLM Tester
  - Check CORS and network errors in browser dev tools
- MCP errors
  - Validate JSON payloads and workflow YAML
  
## Where to go next

- Examples: `configs/workflows/`
- Developer docs: `docs/developer_guide.md`
- API reference: `docs/api.md`
