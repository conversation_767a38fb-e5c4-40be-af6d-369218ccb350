import React, { useState, useCallback } from 'react';
import {
  Layout,
  Button,
  Space,
  Typography,
  message,
  Modal,
  Input,
  Form
} from 'antd';
import {
  PlayCircleOutlined,
  SaveOutlined,
  PlusOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useWorkflowStore } from '../stores/workflowStore';
import { WorkflowCanvasProvider } from '../components/workflow/WorkflowCanvas';
import { NodePalette } from '../components/workflow/NodePalette';
import { NodeConfigPanel } from '../components/workflow/NodeConfigPanel';
import type { WorkflowNode, NodeTemplate } from '../types/workflow';

const { Sider, Content } = Layout;

export const Workflows: React.FC = () => {
  const {
    workflow,
    selectedNode,
    isExecuting,
    executionResult,
    createWorkflow,
    saveWorkflow,
    executeWorkflow,
    addNode,
    validateWorkflow,
  } = useWorkflowStore();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showConfigPanel, setShowConfigPanel] = useState(false);
  const [form] = Form.useForm();

  const handleCreateWorkflow = async () => {
    try {
      const values = await form.validateFields();
      createWorkflow(values.name, values.description || '');
      setShowCreateModal(false);
      form.resetFields();
      message.success('Workflow created successfully!');
    } catch (error) {
      console.error('Failed to create workflow:', error);
    }
  };

  const handleSaveWorkflow = async () => {
    try {
      await saveWorkflow();
      message.success('Workflow saved successfully!');
    } catch (error) {
      message.error('Failed to save workflow');
    }
  };

  const handleExecuteWorkflow = async () => {
    if (!workflow) return;

    const validation = validateWorkflow();
    if (!validation.isValid) {
      message.error(`Cannot execute workflow: ${validation.errors.join(', ')}`);
      return;
    }

    try {
      await executeWorkflow({ input: 'Test input' });
      message.success('Workflow executed successfully!');
    } catch (error) {
      message.error('Failed to execute workflow');
    }
  };

  const handleCanvasDrop = useCallback(
    (event: React.DragEvent) => {
      event.preventDefault();

      const reactFlowBounds = event.currentTarget.getBoundingClientRect();
      const templateData = event.dataTransfer.getData('application/reactflow');

      if (!templateData) return;

      try {
        const template: NodeTemplate = JSON.parse(templateData);
        const position = {
          x: event.clientX - reactFlowBounds.left - 100,
          y: event.clientY - reactFlowBounds.top - 50,
        };

        addNode(template, position);
      } catch (error) {
        console.error('Failed to add node:', error);
      }
    },
    [addNode]
  );

  const handleCanvasDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = 'move';
  }, []);

  const handleNodeClick = (node: WorkflowNode) => {
    setShowConfigPanel(true);
  };

  if (!workflow) {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '60vh'
      }}>
        <Typography.Title level={2}>Workflow Builder</Typography.Title>
        <Typography.Paragraph style={{ textAlign: 'center', maxWidth: '500px' }}>
          Create AI workflows using our visual drag-and-drop interface.
          Connect different AI components to build powerful automation.
        </Typography.Paragraph>

        <Button
          type="primary"
          size="large"
          icon={<PlusOutlined />}
          onClick={() => setShowCreateModal(true)}
        >
          Create New Workflow
        </Button>

        <Modal
          title="Create New Workflow"
          open={showCreateModal}
          onOk={handleCreateWorkflow}
          onCancel={() => setShowCreateModal(false)}
          okText="Create"
        >
          <Form form={form} layout="vertical">
            <Form.Item
              label="Workflow Name"
              name="name"
              rules={[{ required: true, message: 'Please enter workflow name' }]}
            >
              <Input placeholder="Enter workflow name" />
            </Form.Item>
            <Form.Item
              label="Description"
              name="description"
            >
              <Input.TextArea
                rows={3}
                placeholder="Enter workflow description (optional)"
              />
            </Form.Item>
          </Form>
        </Modal>
      </div>
    );
  }

  return (
    <div style={{ height: 'calc(100vh - 112px)' }}>
      {/* Header */}
      <div style={{
        padding: '16px 0',
        borderBottom: '1px solid #f0f0f0',
        marginBottom: '16px'
      }}>
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div>
            <Typography.Title level={3} style={{ margin: 0 }}>
              {workflow.name}
            </Typography.Title>
            <Typography.Text type="secondary">
              {workflow.description}
            </Typography.Text>
          </div>

          <Space>
            <Button
              icon={<SettingOutlined />}
              onClick={() => message.info('Workflow settings coming soon')}
            >
              Settings
            </Button>
            <Button
              icon={<SaveOutlined />}
              onClick={handleSaveWorkflow}
            >
              Save
            </Button>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              loading={isExecuting}
              onClick={handleExecuteWorkflow}
            >
              {isExecuting ? 'Executing...' : 'Execute'}
            </Button>
          </Space>
        </div>
      </div>

      {/* Main Layout */}
      <Layout style={{ height: 'calc(100% - 80px)', background: 'white' }}>
        {/* Node Palette */}
        <Sider
          width={280}
          style={{
            background: 'white',
            borderRight: '1px solid #f0f0f0'
          }}
        >
          <NodePalette />
        </Sider>

        {/* Canvas */}
        <Content
          style={{ position: 'relative' }}
          onDrop={handleCanvasDrop}
          onDragOver={handleCanvasDragOver}
        >
          <WorkflowCanvasProvider
            onNodeClick={handleNodeClick}
            onCanvasClick={() => setShowConfigPanel(false)}
          />
        </Content>

        {/* Configuration Panel */}
        {showConfigPanel && (
          <Sider
            width={350}
            style={{
              background: 'white',
              borderLeft: '1px solid #f0f0f0'
            }}
          >
            <NodeConfigPanel
              node={selectedNode}
              onClose={() => setShowConfigPanel(false)}
            />
          </Sider>
        )}
      </Layout>

      {/* Execution Result Modal */}
      {executionResult && (
        <Modal
          title="Execution Result"
          open={!!executionResult}
          onCancel={() => useWorkflowStore.getState().clearExecutionResult()}
          footer={[
            <Button
              key="close"
              onClick={() => useWorkflowStore.getState().clearExecutionResult()}
            >
              Close
            </Button>
          ]}
          width={600}
        >
          <div>
            <Typography.Text strong>Status: </Typography.Text>
            <Typography.Text
              type={executionResult.status === 'completed' ? 'success' : 'danger'}
            >
              {executionResult.status.toUpperCase()}
            </Typography.Text>
          </div>

          {executionResult.duration && (
            <div style={{ marginTop: '8px' }}>
              <Typography.Text strong>Duration: </Typography.Text>
              <Typography.Text>{executionResult.duration}ms</Typography.Text>
            </div>
          )}

          {executionResult.output && (
            <div style={{ marginTop: '16px' }}>
              <Typography.Text strong>Output:</Typography.Text>
              <pre style={{
                background: '#f5f5f5',
                padding: '12px',
                borderRadius: '4px',
                marginTop: '8px',
                overflow: 'auto'
              }}>
                {JSON.stringify(executionResult.output, null, 2)}
              </pre>
            </div>
          )}

          {executionResult.error && (
            <div style={{ marginTop: '16px' }}>
              <Typography.Text strong type="danger">Error:</Typography.Text>
              <div style={{
                background: '#fff2f0',
                padding: '12px',
                borderRadius: '4px',
                marginTop: '8px',
                color: '#ff4d4f'
              }}>
                {executionResult.error}
              </div>
            </div>
          )}
        </Modal>
      )}
    </div>
  );
};
