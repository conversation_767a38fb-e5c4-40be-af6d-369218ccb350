# M-GAIF Web Console API Reference

## Overview

The M-GAIF Web Console communicates with the backend through a TypeScript API client that provides type-safe access to both MCP and Edge APIs.

## API Client

### Import and Usage

```typescript
import { api, ChatMessage } from '../api/client'

// The api instance is pre-configured and ready to use
const result = await api.workflowRun(spec, state)
```

### Configuration

```typescript
// Set custom API base URL
api.setBase('http://127.0.0.1:8000')

// Clear API base (use proxy)
api.setBase(undefined)

// Get current base URL
const currentBase = api.base
```

## MCP API Methods

### Workflow Execution

Execute a workflow specification with optional initial state.

```typescript
async workflowRun(spec: any, state?: any): Promise<{ state: any }>
```

**Parameters:**
- `spec`: Workflow specification object (parsed from YAML)
- `state`: Optional initial state object

**Returns:**
- Promise resolving to execution result with final state

**Example:**
```typescript
const spec = {
  name: "example",
  nodes: [
    { id: "start", handler: "set", params: { message: "Hello" } },
    { id: "end", handler: "noop" }
  ],
  edges: [{ from: "start", to: "end" }]
}

const result = await api.workflowRun(spec, {})
console.log(result.state) // { message: "Hello" }
```

### Document Indexing

Index documents in the RAG vector store.

```typescript
async retrieverIndex(items: Array<{ id: string; text: string }>): Promise<{ ok: boolean; count: number }>
```

**Parameters:**
- `items`: Array of documents with unique IDs and text content

**Returns:**
- Promise resolving to indexing result

**Example:**
```typescript
const documents = [
  { id: "doc1", text: "The quick brown fox" },
  { id: "doc2", text: "jumps over the lazy dog" }
]

const result = await api.retrieverIndex(documents)
console.log(`Indexed ${result.count} documents`) // Indexed 2 documents
```

### Document Search

Search indexed documents using semantic similarity.

```typescript
async retrieverSearch(query: string, top_k = 3): Promise<{ hits: Array<{ id: string; score: number }> }>
```

**Parameters:**
- `query`: Search query string
- `top_k`: Maximum number of results to return (default: 3)

**Returns:**
- Promise resolving to search results with similarity scores

**Example:**
```typescript
const results = await api.retrieverSearch("quick fox", 5)
results.hits.forEach(hit => {
  console.log(`${hit.id}: ${hit.score.toFixed(3)}`)
})
```

### MCP Chat

Send messages to the LLM via MCP tool interface.

```typescript
async mcpChat(messages: ChatMessage[]): Promise<any>
```

**Parameters:**
- `messages`: Array of chat messages

**Returns:**
- Promise resolving to LLM response

**Example:**
```typescript
const messages: ChatMessage[] = [
  { role: "user", content: "Hello, how are you?" }
]

const response = await api.mcpChat(messages)
const reply = response.choices[0].content
```

## Edge API Methods

### Non-Streaming Chat

Send messages to LLM via OpenAI-compatible Edge API.

```typescript
async edgeChat(messages: ChatMessage[], model?: string): Promise<any>
```

**Parameters:**
- `messages`: Array of chat messages
- `model`: Optional model name (e.g., "llama3")

**Returns:**
- Promise resolving to OpenAI-compatible response

**Example:**
```typescript
const messages: ChatMessage[] = [
  { role: "system", content: "You are a helpful assistant." },
  { role: "user", content: "Explain quantum computing" }
]

const response = await api.edgeChat(messages, "llama3")
const reply = response.choices[0].message.content
```

### Streaming Chat

Send messages with real-time streaming response.

```typescript
async edgeChatStream(
  messages: ChatMessage[], 
  onDelta: (text: string) => void, 
  model?: string
): Promise<void>
```

**Parameters:**
- `messages`: Array of chat messages
- `onDelta`: Callback function for each response chunk
- `model`: Optional model name

**Returns:**
- Promise that resolves when streaming completes

**Example:**
```typescript
let fullResponse = ""

await api.edgeChatStream(
  [{ role: "user", content: "Tell me a story" }],
  (delta) => {
    fullResponse += delta
    console.log(delta) // Print each chunk as it arrives
  },
  "llama3"
)

console.log("Complete response:", fullResponse)
```

## Type Definitions

### ChatMessage

```typescript
type ChatMessage = {
  role: 'system' | 'user' | 'assistant'
  content: string
}
```

### Workflow Specification

```typescript
interface WorkflowSpec {
  name: string
  nodes: Array<{
    id: string
    handler: string
    params: Record<string, any>
  }>
  edges: Array<{
    from: string
    to: string
    condition?: string
  }>
}
```

### Document Item

```typescript
interface DocumentItem {
  id: string
  text: string
}
```

### Search Hit

```typescript
interface SearchHit {
  id: string
  score: number
}
```

## Error Handling

All API methods can throw errors. Use try-catch blocks for proper error handling:

```typescript
try {
  const result = await api.workflowRun(spec)
  console.log("Success:", result)
} catch (error) {
  console.error("API Error:", error.message)
  // Handle error appropriately
}
```

### Common Error Types

1. **Network Errors**: Connection failed, timeout
2. **HTTP Errors**: 404 Not Found, 500 Server Error
3. **Validation Errors**: Invalid workflow spec, malformed data
4. **Backend Errors**: Workflow execution failed, model not available

## Configuration Management

### Environment Variables

```typescript
// Set at build time
VITE_API_BASE=http://127.0.0.1:8000
```

### Runtime Configuration

```typescript
// Get current API base
function getApiBase(): string | undefined {
  return localStorage.getItem('mgaif.apiBase') || 
         import.meta.env.VITE_API_BASE
}

// Set API base programmatically
api.setBase('https://my-backend.com')
```

### Local Storage

The API client automatically persists settings in browser localStorage:

- `mgaif.apiBase`: Custom API base URL

## Advanced Usage

### Custom Headers

For authentication or custom headers, extend the ApiClient:

```typescript
class CustomApiClient extends ApiClient {
  constructor(authToken?: string) {
    super()
    if (authToken) {
      this.http.defaults.headers.common['Authorization'] = `Bearer ${authToken}`
    }
  }
}
```

### Request Interceptors

Add request/response interceptors for logging or transformation:

```typescript
// Add request interceptor
api.http.interceptors.request.use(config => {
  console.log('API Request:', config.method?.toUpperCase(), config.url)
  return config
})

// Add response interceptor
api.http.interceptors.response.use(
  response => response,
  error => {
    console.error('API Error:', error.response?.status, error.message)
    return Promise.reject(error)
  }
)
```

### Timeout Configuration

```typescript
// Set custom timeout
api.http.defaults.timeout = 30000 // 30 seconds
```

## Testing

### Mock API Client

For testing, create a mock implementation:

```typescript
class MockApiClient {
  async workflowRun(spec: any, state?: any) {
    return { state: { ...state, result: "mocked" } }
  }
  
  async retrieverSearch(query: string, top_k = 3) {
    return { hits: [{ id: "mock1", score: 0.95 }] }
  }
  
  // ... other methods
}
```

### Integration Testing

```typescript
// Test with real backend
describe('API Integration', () => {
  beforeAll(() => {
    api.setBase('http://localhost:8000')
  })
  
  test('workflow execution', async () => {
    const spec = { /* workflow spec */ }
    const result = await api.workflowRun(spec)
    expect(result.state).toBeDefined()
  })
})
```

## Performance Considerations

### Caching

Implement caching for frequently accessed data:

```typescript
const cache = new Map()

async function cachedRetrieverSearch(query: string, top_k = 3) {
  const key = `${query}:${top_k}`
  if (cache.has(key)) {
    return cache.get(key)
  }
  
  const result = await api.retrieverSearch(query, top_k)
  cache.set(key, result)
  return result
}
```

### Debouncing

For search-as-you-type functionality:

```typescript
import { debounce } from 'lodash'

const debouncedSearch = debounce(async (query: string) => {
  const results = await api.retrieverSearch(query)
  setSearchResults(results.hits)
}, 300)
```

### Streaming Optimization

For large streaming responses, consider buffering:

```typescript
let buffer = ""
const flushInterval = 100 // ms

await api.edgeChatStream(messages, (delta) => {
  buffer += delta
}, model)

// Flush buffer periodically
setInterval(() => {
  if (buffer) {
    setDisplayText(prev => prev + buffer)
    buffer = ""
  }
}, flushInterval)
```

---

This API reference provides comprehensive documentation for integrating with the M-GAIF backend APIs through the TypeScript client.
