"""Realistic test data for M-GAIF unit tests.

This module provides realistic data to replace mock/placeholder data in tests.
All data is designed to be representative of real-world usage scenarios.
"""

from typing import List, Dict, Any, Tuple

# Realistic text samples for tokenization and embedding tests
REALISTIC_TEXTS = {
    "short": [
        "Machine learning transforms data into insights.",
        "Artificial intelligence enhances human capabilities.",
        "Natural language processing enables computer understanding.",
        "Deep learning models recognize complex patterns.",
        "Neural networks simulate brain-like processing.",
    ],
    "medium": [
        "The rapid advancement of artificial intelligence has revolutionized numerous industries, from healthcare and finance to transportation and entertainment, creating unprecedented opportunities for innovation and efficiency.",
        "Modern machine learning algorithms can process vast amounts of data to identify patterns and make predictions that were previously impossible for traditional computing methods to achieve.",
        "Natural language processing technologies enable computers to understand, interpret, and generate human language in ways that feel increasingly natural and contextually appropriate.",
        "Computer vision systems now surpass human performance in many image recognition tasks, enabling applications like autonomous vehicles, medical diagnosis, and quality control in manufacturing.",
        "Reinforcement learning algorithms learn optimal strategies through trial and error, much like humans do, making them particularly effective for game playing, robotics, and resource optimization.",
    ],
    "long": [
        "The field of artificial intelligence has undergone remarkable transformation over the past decade, driven by advances in deep learning, increased computational power, and the availability of large-scale datasets. These developments have enabled breakthrough applications in computer vision, natural language processing, speech recognition, and autonomous systems. Machine learning models now demonstrate superhuman performance in specific domains while researchers continue to work toward more general artificial intelligence systems that can adapt and learn across multiple domains with human-like flexibility and reasoning capabilities.",
        "Modern natural language processing systems leverage transformer architectures and attention mechanisms to understand context and meaning in text with unprecedented accuracy. These systems can perform complex tasks such as language translation, text summarization, question answering, and creative writing. The integration of large language models with retrieval systems has created powerful tools for information processing and knowledge synthesis, enabling applications that can understand and respond to human queries with remarkable sophistication and contextual awareness.",
        "Computer vision technology has evolved from simple pattern recognition to sophisticated scene understanding and object manipulation. Contemporary systems can identify objects, understand spatial relationships, track movement across time, and even generate realistic images and videos. These capabilities have found applications in autonomous vehicles, medical imaging, surveillance systems, augmented reality, and creative industries, fundamentally changing how we interact with visual information and automated systems.",
    ]
}

# Realistic document corpus for retrieval tests
DOCUMENT_CORPUS = [
    {
        "id": "ai_overview_001",
        "title": "Introduction to Artificial Intelligence",
        "content": "Artificial intelligence (AI) is a branch of computer science that aims to create intelligent machines capable of performing tasks that typically require human intelligence. These tasks include learning, reasoning, problem-solving, perception, and language understanding. AI systems can be categorized into narrow AI, which is designed for specific tasks, and general AI, which would possess human-like cognitive abilities across multiple domains."
    },
    {
        "id": "ml_fundamentals_002", 
        "title": "Machine Learning Fundamentals",
        "content": "Machine learning is a subset of artificial intelligence that enables computers to learn and improve from experience without being explicitly programmed. It involves algorithms that can identify patterns in data and make predictions or decisions based on those patterns. The three main types of machine learning are supervised learning, unsupervised learning, and reinforcement learning, each suited for different types of problems and data structures."
    },
    {
        "id": "nlp_applications_003",
        "title": "Natural Language Processing Applications", 
        "content": "Natural language processing (NLP) is a field that combines computational linguistics with machine learning to help computers understand, interpret, and generate human language. NLP applications include machine translation, sentiment analysis, chatbots, text summarization, and information extraction. Modern NLP systems use deep learning techniques and large language models to achieve human-like performance in many language tasks."
    },
    {
        "id": "computer_vision_004",
        "title": "Computer Vision and Image Recognition",
        "content": "Computer vision is an interdisciplinary field that deals with how computers can gain high-level understanding from digital images or videos. It seeks to automate tasks that the human visual system can do, such as object recognition, image classification, and scene understanding. Applications include facial recognition, medical image analysis, autonomous vehicles, and augmented reality systems."
    },
    {
        "id": "deep_learning_005",
        "title": "Deep Learning Neural Networks",
        "content": "Deep learning is a subset of machine learning that uses artificial neural networks with multiple layers to model and understand complex patterns in data. These networks are inspired by the structure and function of the human brain and can automatically learn hierarchical representations of data. Deep learning has achieved breakthrough results in image recognition, natural language processing, speech recognition, and game playing."
    },
    {
        "id": "robotics_ai_006",
        "title": "Robotics and Artificial Intelligence",
        "content": "The integration of artificial intelligence with robotics has created intelligent machines capable of perceiving their environment, making decisions, and performing physical tasks. AI-powered robots can adapt to changing conditions, learn from experience, and collaborate with humans. Applications range from industrial automation and healthcare assistance to space exploration and domestic service robots."
    },
    {
        "id": "ethics_ai_007",
        "title": "AI Ethics and Responsible Development",
        "content": "As artificial intelligence becomes more prevalent in society, ethical considerations become increasingly important. Key concerns include algorithmic bias, privacy protection, job displacement, transparency in decision-making, and the potential for misuse. Responsible AI development involves creating systems that are fair, accountable, transparent, and aligned with human values and societal benefits."
    },
    {
        "id": "future_ai_008",
        "title": "The Future of Artificial Intelligence",
        "content": "The future of artificial intelligence holds immense potential for transforming society, economy, and human life. Emerging trends include the development of artificial general intelligence, quantum computing integration, brain-computer interfaces, and AI-human collaboration. However, realizing this potential requires addressing technical challenges, ethical considerations, and ensuring that AI benefits all of humanity."
    }
]

# Realistic chat conversation examples
CHAT_CONVERSATIONS = [
    {
        "conversation_id": "tech_support_001",
        "messages": [
            {"role": "user", "content": "I'm having trouble connecting my laptop to the WiFi network. Can you help me troubleshoot this issue?"},
            {"role": "assistant", "content": "I'd be happy to help you with your WiFi connection issue. Let's start with some basic troubleshooting steps. First, can you tell me what operating system you're using and what error message, if any, you're seeing when you try to connect?"},
            {"role": "user", "content": "I'm using Windows 11, and when I try to connect, it says 'Can't connect to this network' but other devices in my house connect fine."},
            {"role": "assistant", "content": "That suggests the issue is specific to your laptop rather than the network itself. Let's try a few things: 1) First, try forgetting and reconnecting to the network. 2) Update your WiFi drivers. 3) Reset your network settings. Would you like me to walk you through these steps one by one?"}
        ]
    },
    {
        "conversation_id": "learning_ai_002", 
        "messages": [
            {"role": "user", "content": "I'm new to machine learning and want to understand the difference between supervised and unsupervised learning. Can you explain this in simple terms?"},
            {"role": "assistant", "content": "Great question! Think of it this way: Supervised learning is like learning with a teacher who provides the correct answers. You show the algorithm examples with both input data and the correct output, so it learns to make predictions. For example, showing it thousands of photos labeled 'cat' or 'dog' to teach it to recognize cats and dogs. Unsupervised learning is like learning without a teacher - you give the algorithm data without labels and let it find patterns on its own. For instance, giving it customer data and letting it discover different customer groups or segments naturally."},
            {"role": "user", "content": "That makes sense! What about reinforcement learning? How does that fit in?"},
            {"role": "assistant", "content": "Reinforcement learning is like learning through trial and error with rewards and penalties - similar to how you might train a pet! The algorithm takes actions in an environment and receives feedback in the form of rewards or punishments. Over time, it learns which actions lead to better outcomes. A classic example is training an AI to play chess: it makes moves (actions), wins or loses games (rewards/penalties), and gradually learns better strategies. This is how many game-playing AIs and autonomous systems learn to optimize their behavior."}
        ]
    }
]

# Realistic workflow specifications
WORKFLOW_SPECS = [
    {
        "name": "document_analysis_pipeline",
        "description": "Analyze documents for key insights and sentiment",
        "nodes": [
            {"id": "input", "handler": "set", "params": {"document_text": "The quarterly financial report shows strong growth in revenue and customer acquisition, with a 15% increase compared to last quarter."}},
            {"id": "tokenize", "handler": "tokenize", "params": {"input_key": "document_text", "output_key": "tokens"}},
            {"id": "analyze", "handler": "echo", "params": {"input_key": "tokens", "output_key": "analysis"}},
            {"id": "output", "handler": "noop"}
        ],
        "edges": [
            {"from": "input", "to": "tokenize"},
            {"from": "tokenize", "to": "analyze"}, 
            {"from": "analyze", "to": "output"}
        ]
    },
    {
        "name": "content_recommendation_system",
        "description": "Recommend relevant content based on user query",
        "nodes": [
            {"id": "query_input", "handler": "set", "params": {"user_query": "I want to learn about machine learning applications in healthcare"}},
            {"id": "embed_query", "handler": "embed", "params": {"input_key": "user_query", "output_key": "query_embedding"}},
            {"id": "search_docs", "handler": "retrieve", "params": {"query_key": "user_query", "output_key": "relevant_docs", "top_k": 3}},
            {"id": "generate_response", "handler": "call_llm", "params": {"input_key": "relevant_docs", "output_key": "recommendation"}},
            {"id": "final_output", "handler": "noop"}
        ],
        "edges": [
            {"from": "query_input", "to": "embed_query"},
            {"from": "embed_query", "to": "search_docs"},
            {"from": "search_docs", "to": "generate_response"},
            {"from": "generate_response", "to": "final_output"}
        ]
    }
]

# Realistic API test payloads
API_TEST_PAYLOADS = {
    "tokenize": [
        {"text": "Natural language processing enables computers to understand human communication effectively."},
        {"text": "Machine learning algorithms can identify complex patterns in large datasets and make accurate predictions."},
        {"text": "The integration of artificial intelligence in healthcare has improved diagnostic accuracy and patient outcomes."}
    ],
    "embed": [
        {"texts": ["Artificial intelligence transforms industries", "Machine learning drives innovation", "Deep learning enables breakthroughs"]},
        {"texts": ["Computer vision recognizes objects", "Natural language processing understands text", "Robotics combines AI with physical systems"]},
        {"texts": ["Data science extracts insights", "Neural networks model complex relationships", "Algorithms optimize decision making"]}
    ],
    "chat": [
        {"messages": [{"role": "user", "content": "Explain the concept of neural networks in simple terms."}]},
        {"messages": [{"role": "user", "content": "What are the main applications of computer vision technology?"}]},
        {"messages": [{"role": "user", "content": "How does reinforcement learning differ from supervised learning?"}]}
    ],
    "retrieval": [
        {"query": "machine learning applications", "top_k": 3},
        {"query": "artificial intelligence ethics", "top_k": 2}, 
        {"query": "deep learning neural networks", "top_k": 4}
    ]
}

# Realistic vector data for testing
VECTOR_TEST_DATA = [
    {"id": "vec_001", "vector": [0.8, 0.6, 0.2, 0.9, 0.1, 0.7, 0.4, 0.3], "metadata": {"category": "technology", "topic": "AI"}},
    {"id": "vec_002", "vector": [0.2, 0.9, 0.7, 0.1, 0.8, 0.3, 0.6, 0.4], "metadata": {"category": "science", "topic": "ML"}},
    {"id": "vec_003", "vector": [0.6, 0.3, 0.8, 0.4, 0.2, 0.9, 0.1, 0.7], "metadata": {"category": "research", "topic": "NLP"}},
    {"id": "vec_004", "vector": [0.4, 0.7, 0.1, 0.8, 0.6, 0.2, 0.9, 0.3], "metadata": {"category": "application", "topic": "CV"}},
    {"id": "vec_005", "vector": [0.9, 0.1, 0.4, 0.6, 0.3, 0.8, 0.7, 0.2], "metadata": {"category": "industry", "topic": "robotics"}}
]

# Performance test scenarios with realistic data
PERFORMANCE_SCENARIOS = {
    "light_load": {"iterations": 10, "concurrency": 1},
    "medium_load": {"iterations": 50, "concurrency": 5},
    "heavy_load": {"iterations": 100, "concurrency": 10}
}

def get_realistic_text(category: str = "medium", index: int = 0) -> str:
    """Get realistic text for testing."""
    texts = REALISTIC_TEXTS.get(category, REALISTIC_TEXTS["medium"])
    return texts[index % len(texts)]

def get_document_corpus(count: int = None) -> List[Dict[str, str]]:
    """Get realistic document corpus for testing."""
    if count is None:
        return DOCUMENT_CORPUS
    return DOCUMENT_CORPUS[:count]

def get_chat_messages(conversation_id: str = None) -> List[Dict[str, str]]:
    """Get realistic chat messages for testing."""
    if conversation_id:
        for conv in CHAT_CONVERSATIONS:
            if conv["conversation_id"] == conversation_id:
                return conv["messages"]
    return CHAT_CONVERSATIONS[0]["messages"]

def get_workflow_spec(name: str = None) -> Dict[str, Any]:
    """Get realistic workflow specification for testing."""
    if name:
        for spec in WORKFLOW_SPECS:
            if spec["name"] == name:
                return spec
    return WORKFLOW_SPECS[0]

def get_api_payload(endpoint: str, index: int = 0) -> Dict[str, Any]:
    """Get realistic API payload for testing."""
    payloads = API_TEST_PAYLOADS.get(endpoint, [])
    if not payloads:
        return {}
    return payloads[index % len(payloads)]

def get_vector_data(count: int = None) -> List[Dict[str, Any]]:
    """Get realistic vector data for testing."""
    if count is None:
        return VECTOR_TEST_DATA
    return VECTOR_TEST_DATA[:count]
