import React, { useState } from 'react'
import { Button, Col, Form, Input, List, message, Row, Space, Typography } from 'antd'
import { api } from '../api/client'

export default function RAGStores() {
  const [docs, setDocs] = useState<Array<{id: string; text: string}>>([])
  const [hits, setHits] = useState<Array<{id: string; score: number}>>([])
  const [loading, setLoading] = useState(false)

  async function onIndex(values: any) {
    try {
      setLoading(true)
      const items = values.bulk?.trim()
        ? values.bulk.split('\n').map((line: string, i: number) => ({ id: `d${i+1}`, text: line }))
        : docs
      const res = await api.retrieverIndex(items)
      setDocs(items)
      message.success(`Indexed ${res.count} docs`)
    } catch (e: any) {
      message.error(e.message || 'Index failed')
    } finally {
      setLoading(false)
    }
  }

  async function onSearch(values: any) {
    try {
      setLoading(true)
      const res = await api.retrieverSearch(values.query, values.top_k ?? 3)
      setHits(res.hits)
    } catch (e: any) {
      message.error(e.message || 'Search failed')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Row gutter={16}>
      <Col xs={24} lg={12}>
        <Typography.Title level={4}>Index Documents</Typography.Title>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Form layout="vertical" onFinish={onIndex}>
            <Form.Item label="Bulk lines (one doc per line)" name="bulk">
              <Input.TextArea rows={6} placeholder="Enter documents..." />
            </Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>Index</Button>
          </Form>
          <Typography.Title level={5}>Indexed ({docs.length})</Typography.Title>
          <List
            size="small"
            bordered
            dataSource={docs}
            renderItem={(d) => <List.Item><b>{d.id}</b>: {d.text}</List.Item>}
          />
        </Space>
      </Col>
      <Col xs={24} lg={12}>
        <Typography.Title level={4}>Search</Typography.Title>
        <Form layout="inline" onFinish={onSearch}>
          <Form.Item name="query" rules={[{ required: true }]}>
            <Input placeholder="query" style={{ width: 260 }} />
          </Form.Item>
          <Form.Item name="top_k" initialValue={3}>
            <Input placeholder="top_k" type="number" style={{ width: 100 }} />
          </Form.Item>
          <Button htmlType="submit" type="primary" loading={loading}>Search</Button>
        </Form>
        <Typography.Title level={5} style={{ marginTop: 16 }}>Hits</Typography.Title>
        <List
          size="small"
          bordered
          dataSource={hits}
          renderItem={(h) => <List.Item><b>{h.id}</b> — score: {h.score.toFixed(3)}</List.Item>}
        />
      </Col>
    </Row>
  )
}
