import { create } from 'zustand';
import type { Project, ProjectFilters } from '../types';
import { api } from '../services/api';

interface ProjectState {
  projects: Project[];
  selectedProject: Project | null;
  filters: ProjectFilters;
  isLoading: boolean;
  error: string | null;
}

interface ProjectActions {
  // Project CRUD
  fetchProjects: () => Promise<void>;
  createProject: (data: Partial<Project>) => Promise<Project>;
  updateProject: (id: string, data: Partial<Project>) => Promise<void>;
  deleteProject: (id: string) => Promise<void>;
  
  // Selection
  selectProject: (project: Project | null) => void;
  
  // Filters
  setFilters: (filters: Partial<ProjectFilters>) => void;
  clearFilters: () => void;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
}

type ProjectStore = ProjectState & ProjectActions;

const initialFilters: ProjectFilters = {
  type: [],
  status: [],
  sortBy: 'updatedAt',
  sortOrder: 'desc',
  search: '',
};

export const useProjectStore = create<ProjectStore>((set, get) => ({
  // Initial state
  projects: [],
  selectedProject: null,
  filters: initialFilters,
  isLoading: false,
  error: null,

  // Actions
  fetchProjects: async () => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.projects.getAll();
      set({
        projects: response.data,
        isLoading: false,
      });
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to fetch projects',
      });
    }
  },

  createProject: async (data: Partial<Project>) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.projects.create(data);
      const newProject = response.data;
      
      set((state) => ({
        projects: [newProject, ...state.projects],
        isLoading: false,
      }));
      
      return newProject;
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to create project',
      });
      throw error;
    }
  },

  updateProject: async (id: string, data: Partial<Project>) => {
    set({ isLoading: true, error: null });
    try {
      const response = await api.projects.update(id, data);
      const updatedProject = response.data;
      
      set((state) => ({
        projects: state.projects.map((p) =>
          p.id === id ? updatedProject : p
        ),
        selectedProject:
          state.selectedProject?.id === id ? updatedProject : state.selectedProject,
        isLoading: false,
      }));
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to update project',
      });
      throw error;
    }
  },

  deleteProject: async (id: string) => {
    set({ isLoading: true, error: null });
    try {
      await api.projects.delete(id);
      
      set((state) => ({
        projects: state.projects.filter((p) => p.id !== id),
        selectedProject:
          state.selectedProject?.id === id ? null : state.selectedProject,
        isLoading: false,
      }));
    } catch (error: any) {
      set({
        isLoading: false,
        error: error.message || 'Failed to delete project',
      });
      throw error;
    }
  },

  selectProject: (project: Project | null) => {
    set({ selectedProject: project });
  },

  setFilters: (filters: Partial<ProjectFilters>) => {
    set((state) => ({
      filters: { ...state.filters, ...filters },
    }));
  },

  clearFilters: () => {
    set({ filters: initialFilters });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setError: (error: string | null) => {
    set({ error });
  },

  clearError: () => {
    set({ error: null });
  },
}));
