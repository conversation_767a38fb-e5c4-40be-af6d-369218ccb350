import os
import asyncio

import httpx
import pytest

from core.adapters.ollama_adapter import OllamaAdapter
from core.contracts.llm import ChatCompletionRequest, ChatMessage


async def _ollama_alive(base_url: str) -> bool:
    """Check if Ollama server is running and responsive."""
    try:
        async with httpx.AsyncClient(timeout=3.0) as ac:
            # Check if the server is running by hitting the tags endpoint
            r = await ac.get(f"{base_url}/api/tags")
            if r.status_code != 200:
                return False

            # Also verify that we can make a basic chat request
            # This ensures the chat endpoint is available
            test_payload = {
                "model": "llama3",  # Default model
                "messages": [{"role": "user", "content": "test"}],
                "stream": False,
            }
            r = await ac.post(f"{base_url}/api/chat", json=test_payload)
            # If we get 404, the endpoint doesn't exist
            # If we get other errors (like model not found), that's still "alive"
            return r.status_code != 404
    except Exception:
        return False


@pytest.fixture
def ollama_adapter():
    """Create an OllamaAdapter instance, skipping if <PERSON>lla<PERSON> is not available."""
    base_url = os.getenv("OLLAMA_BASE_URL", "http://127.0.0.1:11434")
    model = os.getenv("OLLAMA_MODEL", "llama3")

    # Check if Ollama is alive synchronously using asyncio.run
    try:
        is_alive = asyncio.run(_ollama_alive(base_url))
        if not is_alive:
            pytest.skip("Ollama server not running; set OLLAMA_BASE_URL and start ollama serve")
    except Exception:
        pytest.skip("Failed to check Ollama availability")

    return OllamaAdapter(base_url=base_url, default_model=model)


@pytest.mark.asyncio
async def test_ollama_chat_basic(ollama_adapter, async_bench):
    req = ChatCompletionRequest(
        messages=[
            ChatMessage(role="system", content="You are a helpful assistant."),
            ChatMessage(role="user", content="Say 'hi' in one short sentence."),
        ],
        model=os.getenv("OLLAMA_MODEL", "llama3"),
        temperature=0.0,
        max_tokens=64,
        stream=False,
    )
    # warmup
    resp = await ollama_adapter.chat(req)
    assert resp.choices and isinstance(resp.choices[0].message.content, str)
    assert len(resp.choices[0].message.content) > 0
    # benchmark a few calls (keep light)
    async def _call():
        return await ollama_adapter.chat(req)

    metrics = await async_bench(_call, iterations=3)
    # Do not assert strict latency here; model/hardware dependent
    assert metrics["iterations"] == 3


@pytest.mark.asyncio
async def test_ollama_chat_stream(ollama_adapter):
    req = ChatCompletionRequest(
        messages=[ChatMessage(role="user", content="List two colors.")],
        model=os.getenv("OLLAMA_MODEL", "llama3"),
        temperature=0.0,
        max_tokens=32,
        stream=True,
    )
    chunks = []
    async for ch in ollama_adapter.chat_stream(req):
        chunks.append(ch)
    # There should be at least the initial role chunk and some content chunks
    assert len(chunks) >= 2
    content = "".join((c.choices[0].delta.content or "") for c in chunks)
    assert any(word in content.lower() for word in ["red", "blue", "green"]) or len(content) > 0
