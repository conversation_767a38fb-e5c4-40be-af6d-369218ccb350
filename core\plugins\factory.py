"""Plugin factory system for dynamic plugin creation."""

from __future__ import annotations

import importlib
import inspect
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, Type

from .base import Plugin, PluginError, PluginLoadError

logger = logging.getLogger(__name__)


class PluginFactory(ABC):
    """Abstract factory for creating plugin instances.
    
    Provides a standardized interface for creating plugins with
    configuration validation and dependency injection.
    
    Example:
        >>> class TokenizerFactory(PluginFactory):
        ...     def create_plugin(self, config: Dict[str, Any]) -> Plugin:
        ...         tokenizer_type = config.get("type", "simple")
        ...         if tokenizer_type == "simple":
        ...             return SimpleTokenizer()
        ...         elif tokenizer_type == "huggingface":
        ...             return HuggingFaceTokenizer(config["model"])
    """
    
    @abstractmethod
    async def create_plugin(self, config: Dict[str, Any]) -> Plugin:
        """Create a plugin instance with the given configuration.
        
        Args:
            config: Configuration dictionary for plugin creation
            
        Returns:
            Configured plugin instance
            
        Raises:
            PluginError: If plugin creation fails
        """
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate plugin configuration.
        
        Override to provide custom configuration validation.
        
        Args:
            config: Configuration to validate
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        return True
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Get JSON schema for plugin configuration.
        
        Override to provide configuration schema for validation
        and documentation.
        
        Returns:
            JSON schema dictionary
        """
        return {}


class ReflectionPluginFactory(PluginFactory):
    """Factory that creates plugins using reflection.
    
    Dynamically loads plugin classes from module paths and creates
    instances with configuration-based initialization.
    
    Example:
        >>> factory = ReflectionPluginFactory()
        >>> config = {
        ...     "class": "plugins.tokenizers.simple_tokenizer.SimpleTokenizer",
        ...     "args": {},
        ...     "kwargs": {"vocab_size": 10000}
        ... }
        >>> plugin = await factory.create_plugin(config)
    """
    
    async def create_plugin(self, config: Dict[str, Any]) -> Plugin:
        """Create plugin using reflection.
        
        Args:
            config: Configuration with 'class' key specifying the plugin class
                   and optional 'args' and 'kwargs' for initialization
                   
        Returns:
            Plugin instance
            
        Raises:
            PluginLoadError: If plugin class cannot be loaded or instantiated
        """
        if not self.validate_config(config):
            raise PluginLoadError("Invalid configuration for reflection factory")
        
        class_path = config["class"]
        args = config.get("args", [])
        kwargs = config.get("kwargs", {})
        
        try:
            # Parse module and class name
            module_name, class_name = class_path.rsplit(".", 1)
            
            # Import module and get class
            module = importlib.import_module(module_name)
            plugin_class = getattr(module, class_name)
            
            # Validate it's a Plugin subclass
            if not (inspect.isclass(plugin_class) and issubclass(plugin_class, Plugin)):
                raise PluginLoadError(f"{class_path} is not a Plugin subclass")
            
            # Create instance
            plugin = plugin_class(*args, **kwargs)
            
            # Initialize if configuration provided
            init_config = config.get("config", {})
            if init_config:
                await plugin.initialize(init_config)
            
            return plugin
            
        except ImportError as e:
            raise PluginLoadError(f"Failed to import {class_path}: {e}")
        except AttributeError as e:
            raise PluginLoadError(f"Class {class_name} not found in {module_name}: {e}")
        except Exception as e:
            raise PluginLoadError(f"Failed to create plugin {class_path}: {e}")
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate reflection factory configuration.
        
        Args:
            config: Configuration to validate
            
        Returns:
            True if valid
            
        Raises:
            ValueError: If configuration is invalid
        """
        if "class" not in config:
            raise ValueError("Reflection factory requires 'class' in configuration")
        
        class_path = config["class"]
        if not isinstance(class_path, str) or "." not in class_path:
            raise ValueError("'class' must be a valid module.ClassName string")
        
        return True
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Get configuration schema for reflection factory."""
        return {
            "type": "object",
            "properties": {
                "class": {
                    "type": "string",
                    "description": "Full class path (module.ClassName)"
                },
                "args": {
                    "type": "array",
                    "description": "Positional arguments for constructor"
                },
                "kwargs": {
                    "type": "object",
                    "description": "Keyword arguments for constructor"
                },
                "config": {
                    "type": "object",
                    "description": "Configuration for plugin initialization"
                }
            },
            "required": ["class"]
        }


class ConfigurablePluginFactory(PluginFactory):
    """Factory that creates plugins from a registry of types.
    
    Maintains a mapping of plugin type names to plugin classes,
    allowing creation by type name rather than full class path.
    
    Example:
        >>> factory = ConfigurablePluginFactory()
        >>> factory.register_type("simple", SimpleTokenizer)
        >>> factory.register_type("huggingface", HuggingFaceTokenizer)
        >>> 
        >>> config = {"type": "simple", "vocab_size": 10000}
        >>> plugin = await factory.create_plugin(config)
    """
    
    def __init__(self):
        """Initialize factory with empty type registry."""
        self._types: Dict[str, Type[Plugin]] = {}
    
    def register_type(self, type_name: str, plugin_class: Type[Plugin]) -> None:
        """Register a plugin type.
        
        Args:
            type_name: Name to use for this plugin type
            plugin_class: Plugin class to instantiate for this type
            
        Raises:
            ValueError: If plugin_class is not a Plugin subclass
        """
        if not (inspect.isclass(plugin_class) and issubclass(plugin_class, Plugin)):
            raise ValueError(f"{plugin_class} is not a Plugin subclass")
        
        self._types[type_name] = plugin_class
        logger.info(f"Registered plugin type '{type_name}' -> {plugin_class.__name__}")
    
    async def create_plugin(self, config: Dict[str, Any]) -> Plugin:
        """Create plugin by type name.
        
        Args:
            config: Configuration with 'type' key and plugin-specific config
            
        Returns:
            Plugin instance
            
        Raises:
            PluginLoadError: If type not found or creation fails
        """
        if not self.validate_config(config):
            raise PluginLoadError("Invalid configuration for configurable factory")
        
        plugin_type = config["type"]
        
        if plugin_type not in self._types:
            available_types = list(self._types.keys())
            raise PluginLoadError(f"Unknown plugin type '{plugin_type}'. Available: {available_types}")
        
        plugin_class = self._types[plugin_type]
        
        try:
            # Extract constructor arguments
            args = config.get("args", [])
            kwargs = config.get("kwargs", {})
            
            # Create instance
            plugin = plugin_class(*args, **kwargs)
            
            # Initialize with plugin-specific config
            init_config = {k: v for k, v in config.items() 
                          if k not in ["type", "args", "kwargs"]}
            if init_config:
                await plugin.initialize(init_config)
            
            return plugin
            
        except Exception as e:
            raise PluginLoadError(f"Failed to create {plugin_type} plugin: {e}")
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """Validate configurable factory configuration."""
        if "type" not in config:
            raise ValueError("Configurable factory requires 'type' in configuration")
        
        return True
    
    def get_config_schema(self) -> Dict[str, Any]:
        """Get configuration schema for configurable factory."""
        return {
            "type": "object",
            "properties": {
                "type": {
                    "type": "string",
                    "enum": list(self._types.keys()),
                    "description": "Plugin type to create"
                },
                "args": {
                    "type": "array",
                    "description": "Positional arguments for constructor"
                },
                "kwargs": {
                    "type": "object", 
                    "description": "Keyword arguments for constructor"
                }
            },
            "required": ["type"]
        }
