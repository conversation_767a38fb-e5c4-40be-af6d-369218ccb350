name: rag_example
nodes:
  - id: set_docs
    handler: set
    params:
      docs:
        - {id: d1, text: "the quick brown fox"}
        - {id: d2, text: "jumps over the lazy dog"}
        - {id: d3, text: "lorem ipsum dolor sit amet"}
  - id: set_query
    handler: set
    params:
      query: "quick fox"
  - id: retrieve
    handler: retrieve
    params:
      doc_state_key: docs
      query_key: query
      result_key: hits
      result_mode: texts
      top_k: 2
  - id: echo
    handler: echo
    params:
      input_key: hits
      output_key: echoed_hits
  - id: end
    handler: noop
edges:
  - {from: set_docs, to: set_query}
  - {from: set_query, to: retrieve}
  - {from: retrieve, to: echo}
  - {from: echo, to: end}
