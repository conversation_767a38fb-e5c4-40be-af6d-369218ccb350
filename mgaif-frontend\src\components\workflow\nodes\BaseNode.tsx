import React from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { Card, Typography, Tag, Space } from 'antd';
import { 
  ExclamationCircleOutlined, 
  CheckCircleOutlined,
  DeleteOutlined,
  SettingOutlined 
} from '@ant-design/icons';
import type { WorkflowNode } from '../../../types/workflow';

interface BaseNodeProps extends NodeProps {
  data: WorkflowNode['data'];
  icon?: string;
  color?: string;
  showHandles?: {
    source?: boolean;
    target?: boolean;
  };
  onDelete?: (nodeId: string) => void;
  onConfigure?: (nodeId: string) => void;
}

export const BaseNode: React.FC<BaseNodeProps> = ({
  id,
  data,
  selected,
  icon = '⚡',
  color = '#1890ff',
  showHandles = { source: true, target: true },
  onDelete,
  onConfigure,
}) => {
  const hasErrors = data.errors && data.errors.length > 0;
  const isValid = data.isValid !== false;

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete?.(id);
  };

  const handleConfigure = (e: React.MouseEvent) => {
    e.stopPropagation();
    onConfigure?.(id);
  };

  return (
    <>
      {/* Target Handle */}
      {showHandles.target && (
        <Handle
          type="target"
          position={Position.Left}
          style={{
            background: color,
            width: 12,
            height: 12,
            border: '2px solid white',
          }}
        />
      )}

      <Card
        size="small"
        style={{
          minWidth: 200,
          maxWidth: 250,
          border: selected ? `2px solid ${color}` : '1px solid #d9d9d9',
          borderRadius: 8,
          boxShadow: selected ? `0 0 0 2px ${color}20` : '0 2px 8px rgba(0,0,0,0.1)',
          background: hasErrors ? '#fff2f0' : 'white',
        }}
        bodyStyle={{ padding: '12px' }}
      >
        {/* Header */}
        <div style={{ 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          marginBottom: '8px' 
        }}>
          <Space>
            <span style={{ fontSize: '16px' }}>{icon}</span>
            <Typography.Text strong style={{ color: color }}>
              {data.label}
            </Typography.Text>
          </Space>
          
          <Space size="small">
            {/* Status Indicator */}
            {hasErrors ? (
              <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
            ) : isValid ? (
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
            ) : null}
            
            {/* Action Buttons */}
            {selected && (
              <>
                <SettingOutlined 
                  style={{ cursor: 'pointer', color: '#666' }}
                  onClick={handleConfigure}
                />
                <DeleteOutlined 
                  style={{ cursor: 'pointer', color: '#ff4d4f' }}
                  onClick={handleDelete}
                />
              </>
            )}
          </Space>
        </div>

        {/* Description */}
        {data.description && (
          <Typography.Text 
            type="secondary" 
            style={{ fontSize: '12px', display: 'block', marginBottom: '8px' }}
          >
            {data.description}
          </Typography.Text>
        )}

        {/* Configuration Preview */}
        <div style={{ marginBottom: '8px' }}>
          {Object.entries(data.config).slice(0, 2).map(([key, value]) => (
            <div key={key} style={{ marginBottom: '4px' }}>
              <Typography.Text style={{ fontSize: '11px', color: '#666' }}>
                {key}: 
              </Typography.Text>
              <Typography.Text style={{ fontSize: '11px', marginLeft: '4px' }}>
                {typeof value === 'string' && value.length > 30 
                  ? `${value.substring(0, 30)}...` 
                  : String(value)
                }
              </Typography.Text>
            </div>
          ))}
        </div>

        {/* Error Messages */}
        {hasErrors && (
          <div style={{ marginTop: '8px' }}>
            {data.errors!.map((error, index) => (
              <Tag key={index} color="error" style={{ fontSize: '10px', marginBottom: '2px' }}>
                {error}
              </Tag>
            ))}
          </div>
        )}
      </Card>

      {/* Source Handle */}
      {showHandles.source && (
        <Handle
          type="source"
          position={Position.Right}
          style={{
            background: color,
            width: 12,
            height: 12,
            border: '2px solid white',
          }}
        />
      )}
    </>
  );
};
