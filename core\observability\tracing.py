"""Distributed tracing for M-GAIF using OpenTelemetry.

This module provides distributed tracing capabilities for tracking requests
across M-GAIF components and services.
"""

import os
import logging
from contextlib import contextmanager
from typing import Any, Dict, Optional, Union
from functools import wraps

# OpenTelemetry imports (optional)
try:
    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor, ConsoleSpanExporter
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter
    from opentelemetry.sdk.resources import Resource
    from opentelemetry.semconv.resource import ResourceAttributes
    from opentelemetry.trace import Status, StatusCode
    from opentelemetry.util.http import get_excluded_urls
    
    OTEL_AVAILABLE = True
except ImportError:
    OTEL_AVAILABLE = False
    # Create mock classes for when OpenTelemetry is not available
    class MockSpan:
        def set_attribute(self, key: str, value: Any) -> None:
            pass
        
        def set_status(self, status: Any, description: str = "") -> None:
            pass
        
        def record_exception(self, exception: Exception) -> None:
            pass
        
        def __enter__(self):
            return self
        
        def __exit__(self, exc_type, exc_val, exc_tb):
            pass
    
    class MockTracer:
        def start_span(self, name: str, **kwargs) -> MockSpan:
            return MockSpan()
    
    trace = None
    Status = None
    StatusCode = None

logger = logging.getLogger(__name__)


class TracingContext:
    """Context manager for distributed tracing."""
    
    def __init__(self, tracer: Optional["trace.Tracer"], span_name: str, **span_kwargs):
        """Initialize tracing context.
        
        Args:
            tracer: OpenTelemetry tracer instance
            span_name: Name of the span
            **span_kwargs: Additional span attributes
        """
        self.tracer = tracer
        self.span_name = span_name
        self.span_kwargs = span_kwargs
        self.span = None
    
    def __enter__(self):
        """Start tracing span."""
        if self.tracer and OTEL_AVAILABLE:
            self.span = self.tracer.start_span(self.span_name, **self.span_kwargs)
            return self.span
        else:
            return MockSpan()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """End tracing span."""
        if self.span and OTEL_AVAILABLE:
            if exc_type:
                self.span.set_status(Status(StatusCode.ERROR, str(exc_val)))
                self.span.record_exception(exc_val)
            else:
                self.span.set_status(Status(StatusCode.OK))
            self.span.end()


def configure_tracing(
    service_name: str = "mgaif",
    service_version: str = "1.0.0",
    otlp_endpoint: Optional[str] = None,
    console_export: bool = False,
    sample_rate: float = 1.0
) -> Optional["trace.Tracer"]:
    """Configure OpenTelemetry tracing.
    
    Args:
        service_name: Name of the service
        service_version: Version of the service
        otlp_endpoint: OTLP collector endpoint URL
        console_export: Whether to export traces to console
        sample_rate: Sampling rate (0.0 to 1.0)
    
    Returns:
        Configured tracer instance or None if OpenTelemetry is not available
    """
    if not OTEL_AVAILABLE:
        logger.warning("OpenTelemetry not available, tracing disabled")
        return None
    
    # Create resource
    resource = Resource.create({
        ResourceAttributes.SERVICE_NAME: service_name,
        ResourceAttributes.SERVICE_VERSION: service_version,
    })
    
    # Create tracer provider
    provider = TracerProvider(resource=resource)
    
    # Configure exporters
    exporters = []
    
    # OTLP exporter
    if otlp_endpoint:
        try:
            otlp_exporter = OTLPSpanExporter(endpoint=f"{otlp_endpoint}/v1/traces")
            exporters.append(otlp_exporter)
            logger.info(f"Configured OTLP trace export to {otlp_endpoint}")
        except Exception as e:
            logger.error(f"Failed to configure OTLP exporter: {e}")
    
    # Console exporter
    if console_export:
        console_exporter = ConsoleSpanExporter()
        exporters.append(console_exporter)
        logger.info("Configured console trace export")
    
    # Add span processors
    for exporter in exporters:
        processor = BatchSpanProcessor(exporter)
        provider.add_span_processor(processor)
    
    # Set global tracer provider
    trace.set_tracer_provider(provider)
    
    # Get tracer
    tracer = trace.get_tracer(service_name, service_version)
    
    logger.info(f"Tracing configured for service '{service_name}' v{service_version}")
    return tracer


def get_tracer(
    name: str,
    version: str = "1.0.0",
    auto_configure: bool = True
) -> Union["trace.Tracer", MockTracer]:
    """Get a tracer instance.
    
    Args:
        name: Tracer name (typically service or component name)
        version: Tracer version
        auto_configure: Whether to auto-configure tracing from environment
    
    Returns:
        Tracer instance or MockTracer if OpenTelemetry is not available
    """
    if not OTEL_AVAILABLE:
        return MockTracer()
    
    # Auto-configure if requested and not already configured
    if auto_configure and (not hasattr(trace, '_tracer_provider') or trace.get_tracer_provider() is None):
        otlp_endpoint = os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
        service_name = os.getenv("OTEL_SERVICE_NAME", name)
        service_version = os.getenv("OTEL_SERVICE_VERSION", version)
        console_export = os.getenv("OTEL_CONSOLE_EXPORT", "false").lower() == "true"
        
        configure_tracing(
            service_name=service_name,
            service_version=service_version,
            otlp_endpoint=otlp_endpoint,
            console_export=console_export
        )
    
    return trace.get_tracer(name, version)


def trace_function(
    span_name: Optional[str] = None,
    tracer_name: Optional[str] = None,
    attributes: Optional[Dict[str, Any]] = None
):
    """Decorator to trace function execution.
    
    Args:
        span_name: Name of the span (defaults to function name)
        tracer_name: Name of the tracer (defaults to module name)
        attributes: Additional span attributes
    
    Returns:
        Decorated function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get tracer
            tracer_name_resolved = tracer_name or func.__module__
            tracer = get_tracer(tracer_name_resolved)
            
            # Get span name
            span_name_resolved = span_name or f"{func.__module__}.{func.__name__}"
            
            # Start span
            with TracingContext(tracer, span_name_resolved) as span:
                # Add attributes
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                
                # Add function metadata
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                
                # Execute function
                try:
                    result = func(*args, **kwargs)
                    span.set_attribute("function.result_type", type(result).__name__)
                    return result
                except Exception as e:
                    span.set_attribute("function.error", str(e))
                    raise
        
        return wrapper
    return decorator


async def trace_async_function(
    span_name: Optional[str] = None,
    tracer_name: Optional[str] = None,
    attributes: Optional[Dict[str, Any]] = None
):
    """Decorator to trace async function execution.
    
    Args:
        span_name: Name of the span (defaults to function name)
        tracer_name: Name of the tracer (defaults to module name)
        attributes: Additional span attributes
    
    Returns:
        Decorated async function
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Get tracer
            tracer_name_resolved = tracer_name or func.__module__
            tracer = get_tracer(tracer_name_resolved)
            
            # Get span name
            span_name_resolved = span_name or f"{func.__module__}.{func.__name__}"
            
            # Start span
            with TracingContext(tracer, span_name_resolved) as span:
                # Add attributes
                if attributes:
                    for key, value in attributes.items():
                        span.set_attribute(key, value)
                
                # Add function metadata
                span.set_attribute("function.name", func.__name__)
                span.set_attribute("function.module", func.__module__)
                span.set_attribute("function.async", True)
                
                # Execute function
                try:
                    result = await func(*args, **kwargs)
                    span.set_attribute("function.result_type", type(result).__name__)
                    return result
                except Exception as e:
                    span.set_attribute("function.error", str(e))
                    raise
        
        return wrapper
    return decorator


@contextmanager
def trace_operation(
    operation_name: str,
    tracer_name: str = "mgaif",
    attributes: Optional[Dict[str, Any]] = None
):
    """Context manager for tracing operations.
    
    Args:
        operation_name: Name of the operation
        tracer_name: Name of the tracer
        attributes: Additional span attributes
    
    Yields:
        Span instance for adding additional attributes
    """
    tracer = get_tracer(tracer_name)
    
    with TracingContext(tracer, operation_name) as span:
        # Add attributes
        if attributes:
            for key, value in attributes.items():
                span.set_attribute(key, value)
        
        yield span


def add_span_attributes(span, **attributes):
    """Add attributes to a span.
    
    Args:
        span: OpenTelemetry span instance
        **attributes: Key-value pairs to add as span attributes
    """
    if span and OTEL_AVAILABLE:
        for key, value in attributes.items():
            span.set_attribute(key, value)


def record_span_exception(span, exception: Exception):
    """Record an exception in a span.
    
    Args:
        span: OpenTelemetry span instance
        exception: Exception to record
    """
    if span and OTEL_AVAILABLE:
        span.record_exception(exception)
        span.set_status(Status(StatusCode.ERROR, str(exception)))


# Auto-configure tracing from environment
def _init_from_env():
    """Initialize tracing from environment variables."""
    if OTEL_AVAILABLE:
        otlp_endpoint = os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
        service_name = os.getenv("OTEL_SERVICE_NAME", "mgaif")
        service_version = os.getenv("OTEL_SERVICE_VERSION", "1.0.0")
        console_export = os.getenv("OTEL_CONSOLE_EXPORT", "false").lower() == "true"
        
        if otlp_endpoint or console_export:
            configure_tracing(
                service_name=service_name,
                service_version=service_version,
                otlp_endpoint=otlp_endpoint,
                console_export=console_export
            )


# Auto-initialize if environment variables are set
_init_from_env()
