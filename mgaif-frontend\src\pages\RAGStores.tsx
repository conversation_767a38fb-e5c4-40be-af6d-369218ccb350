import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Upload,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Progress,
  message,
  Tabs,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Divider,
} from 'antd';
import {
  UploadOutlined,
  FileTextOutlined,
  SearchOutlined,
  SettingOutlined,
  DeleteOutlined,
  EyeOutlined,
  PlusOutlined,
  DatabaseOutlined,
  CloudUploadOutlined,
} from '@ant-design/icons';
import type { UploadProps, TableColumnsType } from 'antd';
import { api } from '../services/api';
import type { RAGDocument, ChunkingStrategy, VectorStore, SearchResult } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;

export const RAGStores: React.FC = () => {
  const [documents, setDocuments] = useState<RAGDocument[]>([]);
  const [vectorStores, setVectorStores] = useState<VectorStore[]>([]);
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [chunkingModalVisible, setChunkingModalVisible] = useState(false);
  const [searchModalVisible, setSearchModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('documents');
  
  const [form] = Form.useForm();
  const [chunkingForm] = Form.useForm();
  const [searchForm] = Form.useForm();

  // Load data on component mount
  useEffect(() => {
    loadDocuments();
    loadVectorStores();
  }, []);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const response = await api.rag.getDocuments();
      setDocuments(response.data || []);
    } catch (error) {
      console.error('Failed to load documents:', error);
      // Use mock data for development
      setDocuments([
        {
          id: '1',
          name: 'Product Manual.pdf',
          type: 'pdf',
          size: 2048576,
          uploadedAt: '2024-01-15T10:30:00Z',
          status: 'indexed',
          chunks: 45,
          metadata: { pages: 12, language: 'en' }
        },
        {
          id: '2',
          name: 'FAQ Document.txt',
          type: 'txt',
          size: 51200,
          uploadedAt: '2024-01-14T15:45:00Z',
          status: 'processing',
          chunks: 0,
          metadata: { encoding: 'utf-8' }
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const loadVectorStores = async () => {
    try {
      const response = await api.rag.getVectorStores();
      setVectorStores(response.data || []);
    } catch (error) {
      console.error('Failed to load vector stores:', error);
      // Use mock data for development
      setVectorStores([
        {
          id: '1',
          name: 'Product Knowledge Base',
          description: 'Contains product manuals and documentation',
          embeddingModel: 'text-embedding-ada-002',
          dimensions: 1536,
          documentCount: 25,
          chunkCount: 1250,
          createdAt: '2024-01-10T09:00:00Z',
          updatedAt: '2024-01-15T10:30:00Z'
        }
      ]);
    }
  };

  const handleFileUpload: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;
    
    try {
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        onProgress?.({ percent: progress });
        if (progress >= 100) {
          clearInterval(interval);
        }
      }, 200);

      const response = await api.rag.uploadDocument(file as File, {
        category: form.getFieldValue('category'),
        tags: form.getFieldValue('tags'),
      });

      onSuccess?.(response);
      message.success(`${file.name} uploaded successfully`);
      loadDocuments();
      setUploadModalVisible(false);
      form.resetFields();
    } catch (error) {
      onError?.(error as Error);
      message.error(`Failed to upload ${file.name}`);
    }
  };

  const handleDeleteDocument = async (id: string) => {
    try {
      await api.rag.deleteDocument(id);
      message.success('Document deleted successfully');
      loadDocuments();
    } catch (error) {
      message.error('Failed to delete document');
    }
  };

  const handleConfigureChunking = async () => {
    try {
      const values = await chunkingForm.validateFields();
      const strategy: ChunkingStrategy = {
        type: values.type,
        chunkSize: values.chunkSize,
        chunkOverlap: values.chunkOverlap,
        separators: values.separators?.split(',').map((s: string) => s.trim()),
        metadata: values.metadata ? JSON.parse(values.metadata) : {}
      };

      await api.rag.configureChunking(strategy);
      message.success('Chunking strategy configured successfully');
      setChunkingModalVisible(false);
      chunkingForm.resetFields();
    } catch (error) {
      message.error('Failed to configure chunking strategy');
    }
  };

  const handleSearch = async () => {
    try {
      const values = await searchForm.validateFields();
      setLoading(true);
      
      const response = await api.rag.searchDocuments(values.query, {
        top_k: values.topK || 5,
        threshold: values.threshold || 0.7
      });
      
      setSearchResults(response.data || []);
      message.success(`Found ${response.data?.length || 0} results`);
    } catch (error) {
      message.error('Search failed');
      // Mock search results for development
      setSearchResults([
        {
          id: '1',
          content: 'This is a sample search result showing relevant content from the documents...',
          score: 0.95,
          metadata: { page: 5, section: 'Installation' },
          documentId: '1',
          documentName: 'Product Manual.pdf'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  const documentColumns: TableColumnsType<RAGDocument> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <FileTextOutlined />
          <Text strong>{name}</Text>
          <Tag color={record.status === 'indexed' ? 'green' : record.status === 'processing' ? 'blue' : 'red'}>
            {record.status}
          </Tag>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size',
      render: (size) => `${(size / 1024 / 1024).toFixed(2)} MB`,
    },
    {
      title: 'Chunks',
      dataIndex: 'chunks',
      key: 'chunks',
      render: (chunks) => <Text>{chunks}</Text>,
    },
    {
      title: 'Uploaded',
      dataIndex: 'uploadedAt',
      key: 'uploadedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger 
              onClick={() => handleDeleteDocument(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const vectorStoreColumns: TableColumnsType<VectorStore> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <div>
          <Text strong>{name}</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.description}
          </Text>
        </div>
      ),
    },
    {
      title: 'Model',
      dataIndex: 'embeddingModel',
      key: 'embeddingModel',
      render: (model) => <Tag color="blue">{model}</Tag>,
    },
    {
      title: 'Documents',
      dataIndex: 'documentCount',
      key: 'documentCount',
    },
    {
      title: 'Chunks',
      dataIndex: 'chunkCount',
      key: 'chunkCount',
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button icon={<SettingOutlined />} size="small">
            Configure
          </Button>
          <Button icon={<SearchOutlined />} size="small">
            Test
          </Button>
        </Space>
      ),
    },
  ];

  const searchResultColumns: TableColumnsType<SearchResult> = [
    {
      title: 'Content',
      dataIndex: 'content',
      key: 'content',
      render: (content) => (
        <Paragraph ellipsis={{ rows: 2, expandable: true }}>
          {content}
        </Paragraph>
      ),
    },
    {
      title: 'Score',
      dataIndex: 'score',
      key: 'score',
      render: (score) => (
        <Progress 
          percent={Math.round(score * 100)} 
          size="small" 
          status={score > 0.8 ? 'success' : score > 0.6 ? 'normal' : 'exception'}
        />
      ),
    },
    {
      title: 'Source',
      dataIndex: 'documentName',
      key: 'documentName',
      render: (name, record) => (
        <div>
          <Text>{name}</Text>
          {record.metadata?.page && (
            <div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Page {record.metadata.page}
              </Text>
            </div>
          )}
        </div>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>RAG Data Stores</Title>
        <Paragraph type="secondary">
          Manage your documents, configure chunking strategies, and test retrieval performance
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Documents"
              value={documents.length}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Indexed Documents"
              value={documents.filter(d => d.status === 'indexed').length}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Chunks"
              value={documents.reduce((sum, doc) => sum + doc.chunks, 0)}
              prefix={<CloudUploadOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Vector Stores"
              value={vectorStores.length}
              prefix={<DatabaseOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              Upload Documents
            </Button>
            <Button 
              icon={<SettingOutlined />}
              onClick={() => setChunkingModalVisible(true)}
            >
              Configure Chunking
            </Button>
            <Button 
              icon={<SearchOutlined />}
              onClick={() => setSearchModalVisible(true)}
            >
              Test Search
            </Button>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Documents" key="documents">
            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          
          <TabPane tab="Vector Stores" key="vectorStores">
            <Table
              columns={vectorStoreColumns}
              dataSource={vectorStores}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          
          <TabPane tab="Search Results" key="searchResults">
            <Table
              columns={searchResultColumns}
              dataSource={searchResults}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 5 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Upload Modal */}
      <Modal
        title="Upload Documents"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item label="Category" name="category">
            <Select placeholder="Select document category">
              <Select.Option value="manual">Manual</Select.Option>
              <Select.Option value="faq">FAQ</Select.Option>
              <Select.Option value="policy">Policy</Select.Option>
              <Select.Option value="guide">Guide</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item label="Tags" name="tags">
            <Input placeholder="Enter tags separated by commas" />
          </Form.Item>
          
          <Form.Item label="Files">
            <Upload.Dragger
              customRequest={handleFileUpload}
              multiple
              accept=".pdf,.txt,.docx,.md"
              showUploadList={{ showRemoveIcon: true }}
            >
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">
                Click or drag files to this area to upload
              </p>
              <p className="ant-upload-hint">
                Support PDF, TXT, DOCX, and Markdown files
              </p>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>

      {/* Chunking Configuration Modal */}
      <Modal
        title="Configure Chunking Strategy"
        open={chunkingModalVisible}
        onOk={handleConfigureChunking}
        onCancel={() => setChunkingModalVisible(false)}
        width={600}
      >
        <Form form={chunkingForm} layout="vertical">
          <Form.Item 
            label="Chunking Type" 
            name="type" 
            initialValue="recursive"
            rules={[{ required: true }]}
          >
            <Select>
              <Select.Option value="fixed">Fixed Size</Select.Option>
              <Select.Option value="semantic">Semantic</Select.Option>
              <Select.Option value="recursive">Recursive</Select.Option>
            </Select>
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                label="Chunk Size" 
                name="chunkSize" 
                initialValue={1000}
                rules={[{ required: true }]}
              >
                <Input type="number" min={100} max={4000} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                label="Chunk Overlap" 
                name="chunkOverlap" 
                initialValue={200}
                rules={[{ required: true }]}
              >
                <Input type="number" min={0} max={500} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item label="Separators" name="separators">
            <Input placeholder="Enter separators separated by commas (e.g., \n\n, \n, ., !)" />
          </Form.Item>
          
          <Form.Item label="Metadata (JSON)" name="metadata">
            <TextArea 
              rows={3} 
              placeholder='{"preserve_formatting": true, "include_headers": true}'
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Search Test Modal */}
      <Modal
        title="Test Document Search"
        open={searchModalVisible}
        onOk={handleSearch}
        onCancel={() => setSearchModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form form={searchForm} layout="vertical">
          <Form.Item 
            label="Search Query" 
            name="query"
            rules={[{ required: true, message: 'Please enter a search query' }]}
          >
            <TextArea 
              rows={3} 
              placeholder="Enter your search query here..."
            />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Top K Results" name="topK" initialValue={5}>
                <Input type="number" min={1} max={20} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Similarity Threshold" name="threshold" initialValue={0.7}>
                <Input type="number" min={0} max={1} step={0.1} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};
