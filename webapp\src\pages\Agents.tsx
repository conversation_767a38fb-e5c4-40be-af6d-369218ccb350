import React, { useState, useEffect } from 'react'
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Slider,
  Row,
  Col,
  Divider,
  message,
  Tag,
  Tooltip,
  Tabs,
  Steps,
  List,
  Avatar,
  Badge,
  Progress,
  Spin,
  Empty,
  Popconfirm,
  Drawer,
  Alert,
} from 'antd'
import {
  PlusOutlined,
  RobotOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  BrainOutlined,
  ToolOutlined,
  DatabaseOutlined,
  MessageOutlined,
  ThunderboltOutlined,
  SafetyCertificateOutlined,
  ClockCircleOutlined,
  UserOutlined,
} from '@ant-design/icons'
import type { TableColumnsType } from 'antd'

const { Title, Text, Paragraph } = Typography
const { TabPane } = Tabs
const { TextArea } = Input
const { Step } = Steps

// Agent Types (matching the backend types)
interface AgentConfig {
  id: string
  name: string
  description: string
  purpose: string
  capabilities: AgentCapability[]
  tools: AgentTool[]
  planningStrategy: 'chain-of-thought' | 'react' | 'plan-and-execute'
  memoryConfig: AgentMemoryConfig
  personality: AgentPersonality
  constraints: AgentConstraint[]
  status: 'draft' | 'testing' | 'active' | 'inactive'
  createdAt: string
  updatedAt: string
  metrics?: AgentMetrics
}

interface AgentCapability {
  type: 'reasoning' | 'planning' | 'memory' | 'tool-use' | 'communication'
  enabled: boolean
  configuration?: Record<string, any>
}

interface AgentTool {
  id: string
  name: string
  type: 'built-in' | 'custom' | 'mcp-integration'
  description: string
  parameters: Record<string, any>
  enabled: boolean
}

interface AgentMemoryConfig {
  episodic: {
    enabled: boolean
    maxEntries: number
    decayRate: number
  }
  semantic: {
    enabled: boolean
    embeddingModel: string
    maxFacts: number
  }
  persistent: {
    enabled: boolean
    storageType: 'local' | 'database' | 'vector-store'
  }
}

interface AgentPersonality {
  tone: 'professional' | 'friendly' | 'casual' | 'formal'
  verbosity: 'concise' | 'detailed' | 'verbose'
  creativity: number // 0-1
  riskTolerance: number // 0-1
  customInstructions?: string
}

interface AgentConstraint {
  type: 'time' | 'resource' | 'ethical' | 'domain'
  description: string
  value: any
  enforced: boolean
}

interface AgentMetrics {
  totalExecutions: number
  successRate: number
  avgExecutionTime: number
  totalCost: number
  lastExecuted?: string
}

interface AgentExecution {
  id: string
  agentId: string
  goal: string
  status: 'running' | 'completed' | 'failed' | 'cancelled'
  startTime: string
  endTime?: string
  steps: ExecutionStep[]
  result?: string
  error?: string
}

interface ExecutionStep {
  id: string
  type: 'planning' | 'tool-use' | 'reasoning' | 'communication'
  description: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  startTime: string
  endTime?: string
  result?: any
  error?: string
}

export default function Agents() {
  const [agents, setAgents] = useState<AgentConfig[]>([])
  const [currentAgent, setCurrentAgent] = useState<AgentConfig | null>(null)
  const [loading, setLoading] = useState(false)
  const [createModalVisible, setCreateModalVisible] = useState(false)
  const [configureModalVisible, setConfigureModalVisible] = useState(false)
  const [testModalVisible, setTestModalVisible] = useState(false)
  const [executionDrawerVisible, setExecutionDrawerVisible] = useState(false)
  const [currentExecution, setCurrentExecution] = useState<AgentExecution | null>(null)
  const [currentStep, setCurrentStep] = useState(0)
  const [activeTab, setActiveTab] = useState('agents')

  const [form] = Form.useForm()
  const [configForm] = Form.useForm()
  const [testForm] = Form.useForm()

  // Load data on component mount
  useEffect(() => {
    loadAgents()
  }, [])

  const loadAgents = async () => {
    try {
      setLoading(true)
      // Mock data for development - in production this would call the API
      const mockAgents: AgentConfig[] = [
        {
          id: '1',
          name: 'Customer Support Agent',
          description: 'Handles customer inquiries and provides support across multiple channels',
          purpose: 'Provide excellent customer service and resolve issues efficiently',
          capabilities: [
            { type: 'reasoning', enabled: true, configuration: { depth: 'advanced' } },
            { type: 'planning', enabled: true, configuration: { strategy: 'reactive' } },
            { type: 'memory', enabled: true, configuration: { retention: 'session' } },
            { type: 'tool-use', enabled: true, configuration: { safety: 'high' } },
            { type: 'communication', enabled: true, configuration: { style: 'professional' } }
          ],
          tools: [
            {
              id: 'knowledge-search',
              name: 'Knowledge Base Search',
              type: 'built-in',
              description: 'Search company knowledge base for answers',
              parameters: { index: 'support-kb' },
              enabled: true
            },
            {
              id: 'ticket-system',
              name: 'Ticket Management',
              type: 'mcp-integration',
              description: 'Create and update support tickets',
              parameters: { system: 'zendesk' },
              enabled: true
            }
          ],
          planningStrategy: 'react',
          memoryConfig: {
            episodic: { enabled: true, maxEntries: 100, decayRate: 0.1 },
            semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 1000 },
            persistent: { enabled: true, storageType: 'database' }
          },
          personality: {
            tone: 'professional',
            verbosity: 'detailed',
            creativity: 0.3,
            riskTolerance: 0.2,
            customInstructions: 'Always be helpful and empathetic. Escalate complex issues to human agents.'
          },
          constraints: [
            { type: 'time', description: 'Response time under 30 seconds', value: 30, enforced: true },
            { type: 'ethical', description: 'No personal data sharing', value: 'strict', enforced: true }
          ],
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-16T14:20:00Z',
          metrics: {
            totalExecutions: 1247,
            successRate: 0.94,
            avgExecutionTime: 12.5,
            totalCost: 45.67,
            lastExecuted: '2024-01-16T14:15:00Z'
          }
        },
        {
          id: '2',
          name: 'Research Assistant',
          description: 'Conducts research and analysis across multiple data sources',
          purpose: 'Gather, analyze, and synthesize information from various sources',
          capabilities: [
            { type: 'reasoning', enabled: true, configuration: { depth: 'expert' } },
            { type: 'planning', enabled: true, configuration: { strategy: 'plan-and-execute' } },
            { type: 'memory', enabled: true, configuration: { retention: 'persistent' } },
            { type: 'tool-use', enabled: true, configuration: { safety: 'medium' } },
            { type: 'communication', enabled: true, configuration: { style: 'academic' } }
          ],
          tools: [
            {
              id: 'web-search',
              name: 'Web Search',
              type: 'built-in',
              description: 'Search the web for information',
              parameters: { engine: 'google' },
              enabled: true
            },
            {
              id: 'document-analysis',
              name: 'Document Analyzer',
              type: 'custom',
              description: 'Analyze and extract insights from documents',
              parameters: { formats: ['pdf', 'docx', 'txt'] },
              enabled: true
            }
          ],
          planningStrategy: 'plan-and-execute',
          memoryConfig: {
            episodic: { enabled: true, maxEntries: 500, decayRate: 0.05 },
            semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 5000 },
            persistent: { enabled: true, storageType: 'vector-store' }
          },
          personality: {
            tone: 'formal',
            verbosity: 'verbose',
            creativity: 0.7,
            riskTolerance: 0.5,
            customInstructions: 'Be thorough and cite sources. Provide balanced perspectives on controversial topics.'
          },
          constraints: [
            { type: 'resource', description: 'Max 10 web searches per query', value: 10, enforced: true },
            { type: 'domain', description: 'Academic and professional sources only', value: 'restricted', enforced: true }
          ],
          status: 'testing',
          createdAt: '2024-01-14T15:45:00Z',
          updatedAt: '2024-01-16T09:20:00Z',
          metrics: {
            totalExecutions: 89,
            successRate: 0.87,
            avgExecutionTime: 45.2,
            totalCost: 12.34,
            lastExecuted: '2024-01-16T09:15:00Z'
          }
        },
        {
          id: '3',
          name: 'Code Review Assistant',
          description: 'Reviews code for quality, security, and best practices',
          purpose: 'Ensure code quality and identify potential issues before deployment',
          capabilities: [
            { type: 'reasoning', enabled: true, configuration: { depth: 'expert' } },
            { type: 'planning', enabled: false },
            { type: 'memory', enabled: true, configuration: { retention: 'session' } },
            { type: 'tool-use', enabled: true, configuration: { safety: 'high' } },
            { type: 'communication', enabled: true, configuration: { style: 'technical' } }
          ],
          tools: [
            {
              id: 'static-analysis',
              name: 'Static Code Analysis',
              type: 'built-in',
              description: 'Analyze code for potential issues',
              parameters: { languages: ['python', 'javascript', 'typescript'] },
              enabled: true
            },
            {
              id: 'security-scan',
              name: 'Security Scanner',
              type: 'custom',
              description: 'Scan for security vulnerabilities',
              parameters: { severity: 'medium' },
              enabled: true
            }
          ],
          planningStrategy: 'chain-of-thought',
          memoryConfig: {
            episodic: { enabled: false, maxEntries: 0, decayRate: 0 },
            semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 2000 },
            persistent: { enabled: false, storageType: 'local' }
          },
          personality: {
            tone: 'professional',
            verbosity: 'detailed',
            creativity: 0.2,
            riskTolerance: 0.1,
            customInstructions: 'Focus on security, performance, and maintainability. Provide specific recommendations.'
          },
          constraints: [
            { type: 'domain', description: 'Code review only', value: 'code-review', enforced: true },
            { type: 'ethical', description: 'No code execution', value: 'no-exec', enforced: true }
          ],
          status: 'draft',
          createdAt: '2024-01-16T08:00:00Z',
          updatedAt: '2024-01-16T08:30:00Z',
          metrics: {
            totalExecutions: 0,
            successRate: 0,
            avgExecutionTime: 0,
            totalCost: 0
          }
        }
      ]

      setAgents(mockAgents)
    } catch (error) {
      console.error('Failed to load agents:', error)
      message.error('Failed to load agents')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateAgent = async () => {
    try {
      const values = await form.validateFields()

      const newAgent: Partial<AgentConfig> = {
        id: `agent_${Date.now()}`,
        name: values.name,
        description: values.description,
        purpose: values.purpose,
        capabilities: [
          { type: 'reasoning', enabled: true },
          { type: 'planning', enabled: true },
          { type: 'memory', enabled: true },
          { type: 'tool-use', enabled: true },
          { type: 'communication', enabled: true }
        ],
        tools: [],
        planningStrategy: values.planningStrategy || 'react',
        memoryConfig: {
          episodic: { enabled: true, maxEntries: 100, decayRate: 0.1 },
          semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 1000 },
          persistent: { enabled: false, storageType: 'local' }
        },
        personality: {
          tone: 'professional',
          verbosity: 'detailed',
          creativity: 0.5,
          riskTolerance: 0.3
        },
        constraints: [],
        status: 'draft',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metrics: {
          totalExecutions: 0,
          successRate: 0,
          avgExecutionTime: 0,
          totalCost: 0
        }
      }

      // In real implementation, call API
      // await api.agents.create(newAgent)

      setAgents(prev => [...prev, newAgent as AgentConfig])
      message.success('Agent created successfully!')
      setCreateModalVisible(false)
      form.resetFields()
    } catch (error) {
      message.error('Failed to create agent')
    }
  }

  const handleToggleStatus = async (agent: AgentConfig) => {
    try {
      const newStatus = agent.status === 'active' ? 'inactive' : 'active'

      // In real implementation, call API
      // await api.agents.updateStatus(agent.id, newStatus)

      setAgents(prev => prev.map(a =>
        a.id === agent.id
          ? { ...a, status: newStatus, updatedAt: new Date().toISOString() }
          : a
      ))

      message.success(`Agent ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`)
    } catch (error) {
      message.error('Failed to update agent status')
    }
  }

  const handleDeleteAgent = async (agentId: string) => {
    try {
      // In real implementation, call API
      // await api.agents.delete(agentId)

      setAgents(prev => prev.filter(a => a.id !== agentId))
      message.success('Agent deleted successfully')
    } catch (error) {
      message.error('Failed to delete agent')
    }
  }

  const handleTestAgent = async (agent: AgentConfig, testGoal: string) => {
    try {
      // Create mock execution with realistic simulation
      const execution: AgentExecution = {
        id: `exec_${Date.now()}`,
        agentId: agent.id,
        goal: testGoal,
        status: 'running',
        startTime: new Date().toISOString(),
        steps: [
          {
            id: 'step_1',
            type: 'planning',
            description: 'Analyzing goal and creating execution plan',
            status: 'running',
            startTime: new Date().toISOString()
          }
        ]
      }

      setCurrentExecution(execution)
      setExecutionDrawerVisible(true)

      // Simulate realistic agent execution steps
      const simulateExecution = async () => {
        // Step 1: Planning (2 seconds)
        await new Promise(resolve => setTimeout(resolve, 2000))
        setCurrentExecution(prev => prev ? {
          ...prev,
          steps: [
            { ...prev.steps[0], status: 'completed', endTime: new Date().toISOString(), result: 'Execution plan created successfully' },
            {
              id: 'step_2',
              type: 'tool-use',
              description: `Using ${agent.tools.length > 0 ? agent.tools[0].name : 'available tools'} to gather information`,
              status: 'running',
              startTime: new Date().toISOString()
            }
          ]
        } : null)

        // Step 2: Tool execution (3 seconds)
        await new Promise(resolve => setTimeout(resolve, 3000))
        setCurrentExecution(prev => prev ? {
          ...prev,
          steps: [
            ...prev.steps.slice(0, -1),
            { ...prev.steps[prev.steps.length - 1], status: 'completed', endTime: new Date().toISOString(), result: 'Information gathered successfully' },
            {
              id: 'step_3',
              type: 'reasoning',
              description: 'Processing information and generating response',
              status: 'running',
              startTime: new Date().toISOString()
            }
          ]
        } : null)

        // Step 3: Reasoning (2 seconds)
        await new Promise(resolve => setTimeout(resolve, 2000))
        setCurrentExecution(prev => prev ? {
          ...prev,
          steps: [
            ...prev.steps.slice(0, -1),
            { ...prev.steps[prev.steps.length - 1], status: 'completed', endTime: new Date().toISOString(), result: 'Analysis completed' },
            {
              id: 'step_4',
              type: 'communication',
              description: 'Formatting and presenting final results',
              status: 'running',
              startTime: new Date().toISOString()
            }
          ]
        } : null)

        // Step 4: Final response (1 second)
        await new Promise(resolve => setTimeout(resolve, 1000))
        setCurrentExecution(prev => prev ? {
          ...prev,
          status: 'completed',
          endTime: new Date().toISOString(),
          result: `Task completed successfully! I have analyzed your goal "${testGoal}" and executed the necessary steps using the ${agent.name} agent. The agent utilized its ${agent.planningStrategy} planning strategy and ${agent.tools.length} available tools to provide a comprehensive response.`,
          steps: [
            ...prev.steps.slice(0, -1),
            { ...prev.steps[prev.steps.length - 1], status: 'completed', endTime: new Date().toISOString(), result: 'Response formatted and ready' }
          ]
        } : null)
      }

      // Start the simulation
      simulateExecution()

      message.success('Agent test execution started')
    } catch (error) {
      console.error('Agent test failed:', error)
      message.error('Failed to start agent test')
    }
  }

  const agentColumns: TableColumnsType<AgentConfig> = [
    {
      title: 'Agent',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <Avatar
            icon={<RobotOutlined />}
            style={{
              backgroundColor: record.status === 'active' ? '#52c41a' :
                              record.status === 'testing' ? '#faad14' : '#d9d9d9'
            }}
          />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Strategy',
      dataIndex: 'planningStrategy',
      key: 'planningStrategy',
      render: (strategy) => {
        const colors = {
          'chain-of-thought': 'blue',
          'react': 'green',
          'plan-and-execute': 'orange'
        }
        const labels = {
          'chain-of-thought': 'Chain of Thought',
          'react': 'ReAct',
          'plan-and-execute': 'Plan & Execute'
        }
        return <Tag color={colors[strategy]}>{labels[strategy]}</Tag>
      },
    },
    {
      title: 'Tools',
      key: 'tools',
      render: (_, record) => (
        <div>
          <Text>{record.tools.length} tools</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '11px' }}>
            {record.tools.filter(t => t.enabled).length} active
          </Text>
        </div>
      ),
    },
    {
      title: 'Performance',
      key: 'performance',
      render: (_, record) => (
        <div>
          {record.metrics ? (
            <>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <Text style={{ fontSize: '12px' }}>Success Rate:</Text>
                <Progress
                  percent={Math.round(record.metrics.successRate * 100)}
                  size="small"
                  style={{ width: '60px' }}
                  strokeColor={record.metrics.successRate > 0.8 ? '#52c41a' : record.metrics.successRate > 0.6 ? '#faad14' : '#ff4d4f'}
                />
              </div>
              <Text type="secondary" style={{ fontSize: '11px' }}>
                {record.metrics.totalExecutions} executions
              </Text>
            </>
          ) : (
            <Text type="secondary">No data</Text>
          )}
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          draft: { color: 'default', text: 'Draft' },
          testing: { color: 'processing', text: 'Testing' },
          active: { color: 'success', text: 'Active' },
          inactive: { color: 'error', text: 'Inactive' }
        }

        const config = statusConfig[status]
        return <Badge status={config.color as any} text={config.text} />
      },
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Configure">
            <Button
              icon={<SettingOutlined />}
              size="small"
              onClick={() => {
                setCurrentAgent(record)
                setConfigureModalVisible(true)
              }}
            />
          </Tooltip>
          <Tooltip title="Test">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => {
                setCurrentAgent(record)
                setTestModalVisible(true)
              }}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? 'Deactivate' : 'Activate'}>
            <Button
              icon={record.status === 'active' ? <StopOutlined /> : <PlayCircleOutlined />}
              size="small"
              type={record.status === 'active' ? 'default' : 'primary'}
              onClick={() => handleToggleStatus(record)}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Popconfirm
            title="Delete Agent"
            description="Are you sure you want to delete this agent?"
            onConfirm={() => handleDeleteAgent(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Tooltip title="Delete">
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <RobotOutlined style={{ marginRight: '12px', color: '#1890ff' }} />
          AI Agents
        </Title>
        <Paragraph type="secondary">
          Design and deploy intelligent AI agents with custom capabilities, tool access, and planning strategies
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                {agents.length}
              </Title>
              <Text type="secondary">Total Agents</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                {agents.filter(a => a.status === 'active').length}
              </Title>
              <Text type="secondary">Active</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#faad14' }}>
                {agents.filter(a => a.status === 'testing').length}
              </Title>
              <Text type="secondary">Testing</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#f5222d' }}>
                {agents.reduce((sum, agent) => sum + agent.tools.length, 0)}
              </Title>
              <Text type="secondary">Total Tools</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>Your AI Agents</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
            size="large"
          >
            Create Agent
          </Button>
        </div>

        {loading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
          </div>
        ) : agents.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No agents created yet"
            style={{ padding: '50px' }}
          >
            <Button type="primary" icon={<PlusOutlined />} onClick={() => setCreateModalVisible(true)}>
              Create Your First Agent
            </Button>
          </Empty>
        ) : (
          <Table
            columns={agentColumns}
            dataSource={agents}
            rowKey="id"
            pagination={{ pageSize: 10, showSizeChanger: true }}
            scroll={{ x: 1200 }}
          />
        )}
      </Card>

      {/* Create Agent Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <RobotOutlined style={{ color: '#1890ff' }} />
            Create New AI Agent
          </div>
        }
        open={createModalVisible}
        onOk={handleCreateAgent}
        onCancel={() => {
          setCreateModalVisible(false)
          form.resetFields()
        }}
        width={600}
        okText="Create Agent"
      >
        <Form form={form} layout="vertical" style={{ marginTop: '16px' }}>
          <Form.Item
            label="Agent Name"
            name="name"
            rules={[{ required: true, message: 'Please enter agent name' }]}
          >
            <Input placeholder="Enter a descriptive name for your agent" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea
              rows={3}
              placeholder="Describe what this agent does and its primary functions"
            />
          </Form.Item>

          <Form.Item
            label="Purpose"
            name="purpose"
            rules={[{ required: true, message: 'Please enter agent purpose' }]}
          >
            <TextArea
              rows={2}
              placeholder="Define the agent's primary purpose and goals"
            />
          </Form.Item>

          <Form.Item
            label="Planning Strategy"
            name="planningStrategy"
            rules={[{ required: true, message: 'Please select planning strategy' }]}
            tooltip="Choose how the agent approaches problem-solving"
          >
            <Select placeholder="Select planning strategy">
              <Select.Option value="chain-of-thought">
                <div>
                  <div>Chain of Thought</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    Step-by-step reasoning for complex problems
                  </div>
                </div>
              </Select.Option>
              <Select.Option value="react">
                <div>
                  <div>ReAct (Reasoning + Acting)</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    Interleaves reasoning and action steps
                  </div>
                </div>
              </Select.Option>
              <Select.Option value="plan-and-execute">
                <div>
                  <div>Plan and Execute</div>
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    Creates complete plan before execution
                  </div>
                </div>
              </Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Test Agent Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <PlayCircleOutlined style={{ color: '#52c41a' }} />
            Test Agent: {currentAgent?.name}
          </div>
        }
        open={testModalVisible}
        onCancel={() => {
          setTestModalVisible(false)
          testForm.resetFields()
        }}
        footer={null}
        width={600}
      >
        <div style={{ marginTop: '16px' }}>
          <Alert
            message="Agent Testing"
            description="Enter a goal or task for the agent to execute. This will create a test execution that you can monitor in real-time."
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />

          <Form
            form={testForm}
            layout="vertical"
            onFinish={async (values) => {
              if (currentAgent) {
                await handleTestAgent(currentAgent, values.goal)
                setTestModalVisible(false)
                testForm.resetFields()
              }
            }}
          >
            <Form.Item
              label="Test Goal"
              name="goal"
              rules={[{ required: true, message: 'Please enter a test goal' }]}
            >
              <TextArea
                rows={4}
                placeholder="Enter a specific goal or task for the agent to execute..."
              />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
              <Space>
                <Button onClick={() => setTestModalVisible(false)}>
                  Cancel
                </Button>
                <Button type="primary" htmlType="submit" icon={<PlayCircleOutlined />}>
                  Start Test
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </div>
      </Modal>

      {/* Agent Execution Drawer */}
      <Drawer
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <ThunderboltOutlined style={{ color: '#faad14' }} />
            Agent Execution Monitor
          </div>
        }
        placement="right"
        onClose={() => setExecutionDrawerVisible(false)}
        open={executionDrawerVisible}
        width={600}
      >
        {currentExecution && (
          <div>
            <Card size="small" style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong>Goal:</Text>
                  <div style={{ marginTop: '4px' }}>
                    <Text>{currentExecution.goal}</Text>
                  </div>
                </div>
                <Badge
                  status={
                    currentExecution.status === 'running' ? 'processing' :
                    currentExecution.status === 'completed' ? 'success' :
                    currentExecution.status === 'failed' ? 'error' : 'default'
                  }
                  text={currentExecution.status.toUpperCase()}
                />
              </div>
            </Card>

            <Card title="Execution Steps" size="small">
              <List
                dataSource={currentExecution.steps}
                renderItem={(step, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar
                          size="small"
                          icon={
                            step.type === 'planning' ? <BrainOutlined /> :
                            step.type === 'tool-use' ? <ToolOutlined /> :
                            step.type === 'reasoning' ? <BrainOutlined /> :
                            <MessageOutlined />
                          }
                          style={{
                            backgroundColor:
                              step.status === 'completed' ? '#52c41a' :
                              step.status === 'running' ? '#faad14' :
                              step.status === 'failed' ? '#ff4d4f' : '#d9d9d9'
                          }}
                        />
                      }
                      title={
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                          <Text strong>Step {index + 1}</Text>
                          <Tag color={
                            step.status === 'completed' ? 'success' :
                            step.status === 'running' ? 'processing' :
                            step.status === 'failed' ? 'error' : 'default'
                          }>
                            {step.status}
                          </Tag>
                          {step.status === 'running' && <Spin size="small" />}
                        </div>
                      }
                      description={
                        <div>
                          <div>{step.description}</div>
                          {step.result && (
                            <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#f6ffed', borderRadius: '4px' }}>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                Result: {step.result}
                              </Text>
                            </div>
                          )}
                          {step.error && (
                            <div style={{ marginTop: '8px', padding: '8px', backgroundColor: '#fff2f0', borderRadius: '4px' }}>
                              <Text type="danger" style={{ fontSize: '12px' }}>
                                Error: {step.error}
                              </Text>
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Card>

            {currentExecution.result && (
              <Card title="Final Result" size="small" style={{ marginTop: '16px' }}>
                <div style={{ padding: '12px', backgroundColor: '#f6ffed', borderRadius: '4px' }}>
                  <Text>{currentExecution.result}</Text>
                </div>
              </Card>
            )}

            {currentExecution.error && (
              <Card title="Execution Error" size="small" style={{ marginTop: '16px' }}>
                <div style={{ padding: '12px', backgroundColor: '#fff2f0', borderRadius: '4px' }}>
                  <Text type="danger">{currentExecution.error}</Text>
                </div>
              </Card>
            )}
          </div>
        )}
      </Drawer>
    </div>
  )
}
