import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { MainLayout } from './src/components/layout/MainLayout';
import { Dashboard } from './src/pages/Dashboard';
import { Workflows } from './src/pages/Workflows';
import { Chatbots } from './src/pages/Chatbots';
import { Agents } from './src/pages/Agents';
import { Templates } from './src/pages/Templates';
import { Settings } from './src/pages/Settings';

function App() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
        },
      }}
    >
      <Router>
        <MainLayout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/workflows" element={<Workflows />} />
            <Route path="/chatbots" element={<Chatbots />} />
            <Route path="/agents" element={<Agents />} />
            <Route path="/templates" element={<Templates />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </MainLayout>
      </Router>
    </ConfigProvider>
  );
}

export default App;
