"""Observability framework for M-GAIF.

This module provides comprehensive observability capabilities including
structured logging, distributed tracing, metrics collection, and monitoring
for all M-GAIF components.

The observability framework supports:
- Structured JSON logging with correlation IDs
- OpenTelemetry distributed tracing
- Prometheus metrics collection
- Health monitoring and alerting
- Performance profiling and analysis

Key Components:
- Logger: Structured logging with context
- Tracer: Distributed tracing with OpenTelemetry
- MetricsCollector: Prometheus metrics collection
- HealthMonitor: System health monitoring
- PerformanceProfiler: Performance analysis

Example:
    >>> from core.observability import get_logger, get_tracer, MetricsCollector
    >>> logger = get_logger("my_component")
    >>> tracer = get_tracer("my_service")
    >>> metrics = MetricsCollector("my_service")
    >>> 
    >>> with tracer.start_span("operation") as span:
    ...     logger.info("Starting operation", extra={"operation_id": "123"})
    ...     metrics.increment_counter("operations_total")
    ...     # Do work
    ...     logger.info("Operation completed")
"""

from .logging import get_logger, configure_logging, StructuredLogger
from .tracing import get_tracer, configure_tracing, TracingContext
from .metrics import MetricsCollector, get_metrics_collector, configure_metrics
from .health import HealthMonitor, HealthStatus, HealthCheck
from .profiler import PerformanceProfiler, ProfilerContext

__all__ = [
    # Logging
    "get_logger",
    "configure_logging", 
    "StructuredLogger",
    
    # Tracing
    "get_tracer",
    "configure_tracing",
    "TracingContext",
    
    # Metrics
    "MetricsCollector",
    "get_metrics_collector",
    "configure_metrics",
    
    # Health monitoring
    "HealthMonitor",
    "HealthStatus",
    "HealthCheck",
    
    # Performance profiling
    "PerformanceProfiler",
    "ProfilerContext",
]
