"""Memory systems for agents."""

from __future__ import annotations

import async<PERSON>
import json
from abc import ABC, abstractmethod
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4

from ..contracts.retrieval import VectorRecord, SearchResult
from ..stores.in_memory_vector import InMemoryVectorStore
from ..text.embeddings import Embedder
from plugins.embedders.simple_embedder import SimpleEmbedder


@dataclass
class MemoryItem:
    """A single item stored in agent memory."""
    id: str
    content: str
    memory_type: str
    importance: float
    timestamp: datetime
    metadata: Dict[str, Any]
    embedding: Optional[List[float]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'MemoryItem':
        """Create from dictionary."""
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


class Memory(ABC):
    """Abstract base class for agent memory systems.
    
    Provides persistent storage and retrieval of agent experiences,
    knowledge, and learned patterns.
    
    Memory types:
    - Episodic: Specific experiences and events
    - Semantic: General knowledge and facts
    - Procedural: Skills and procedures
    - Working: Temporary context and state
    
    Example:
        >>> memory = EpisodicMemory()
        >>> await memory.store("user_preference", "User likes coffee", importance=0.8)
        >>> items = await memory.retrieve("coffee preferences", top_k=5)
    """
    
    def __init__(self, embedder: Embedder = None):
        """Initialize memory system.
        
        Args:
            embedder: Embedder for semantic similarity search
        """
        self.embedder = embedder or SimpleEmbedder(dim=128)
        self._lock = asyncio.Lock()
    
    @abstractmethod
    async def store(self, key: str, content: str, importance: float = 0.5,
                   metadata: Dict[str, Any] = None) -> str:
        """Store a memory item.
        
        Args:
            key: Unique key for the memory
            content: Content to store
            importance: Importance score (0.0 to 1.0)
            metadata: Additional metadata
            
        Returns:
            Memory item ID
        """
        pass
    
    @abstractmethod
    async def retrieve(self, query: str, top_k: int = 5,
                      min_importance: float = 0.0) -> List[MemoryItem]:
        """Retrieve relevant memory items.
        
        Args:
            query: Query string for retrieval
            top_k: Maximum number of items to return
            min_importance: Minimum importance threshold
            
        Returns:
            List of relevant memory items
        """
        pass
    
    @abstractmethod
    async def update(self, item_id: str, content: str = None,
                    importance: float = None, metadata: Dict[str, Any] = None) -> bool:
        """Update an existing memory item.
        
        Args:
            item_id: ID of item to update
            content: New content (optional)
            importance: New importance (optional)
            metadata: New metadata (optional)
            
        Returns:
            True if update successful
        """
        pass
    
    @abstractmethod
    async def delete(self, item_id: str) -> bool:
        """Delete a memory item.
        
        Args:
            item_id: ID of item to delete
            
        Returns:
            True if deletion successful
        """
        pass
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get memory statistics.
        
        Returns:
            Dictionary with memory statistics
        """
        return {
            "total_items": 0,
            "memory_type": self.__class__.__name__,
            "avg_importance": 0.0,
        }


class EpisodicMemory(Memory):
    """Episodic memory for storing specific experiences and events.
    
    Stores time-ordered experiences with automatic importance decay
    and semantic similarity-based retrieval.
    
    Features:
    - Time-based importance decay
    - Semantic similarity search
    - Experience clustering
    - Automatic cleanup of old memories
    
    Example:
        >>> memory = EpisodicMemory(decay_rate=0.1)
        >>> await memory.store("meeting", "Had productive meeting with team", importance=0.9)
        >>> recent = await memory.get_recent_memories(hours=24)
    """
    
    def __init__(self, embedder: Embedder = None, decay_rate: float = 0.01,
                 max_memories: int = 10000):
        """Initialize episodic memory.

        Args:
            embedder: Embedder for semantic search
            decay_rate: Daily importance decay rate
            max_memories: Maximum number of memories to store
        """
        super().__init__(embedder)
        self.decay_rate = decay_rate
        self.max_memories = max_memories

        self._memories: Dict[str, MemoryItem] = {}
        self._vector_store = InMemoryVectorStore()

    @property
    def memories(self) -> List[MemoryItem]:
        """Get all memories as a list (for backward compatibility with tests)."""
        return list(self._memories.values())
    
    async def store(self, key: str, content: str, importance: float = 0.5,
                   metadata: Dict[str, Any] = None) -> str:
        """Store an episodic memory."""
        async with self._lock:
            # Generate embedding
            embedding = self.embedder.embed([content])[0]
            
            # Create memory item
            item_id = str(uuid4())
            memory_item = MemoryItem(
                id=item_id,
                content=content,
                memory_type="episodic",
                importance=importance,
                timestamp=datetime.now(),
                metadata=metadata or {},
                embedding=embedding
            )
            
            # Store in memory
            self._memories[item_id] = memory_item
            
            # Store in vector store
            vector_record = VectorRecord(
                id=item_id,
                vector=embedding,
                metadata={
                    "content": content,
                    "importance": importance,
                    "timestamp": memory_item.timestamp.isoformat(),
                    "key": key,
                    **memory_item.metadata
                }
            )
            self._vector_store.add([vector_record])
            
            # Cleanup if needed
            if len(self._memories) > self.max_memories:
                await self._cleanup_old_memories()
            
            return item_id
    
    async def retrieve(self, query: str, top_k: int = 5,
                      min_importance: float = 0.0) -> List[MemoryItem]:
        """Retrieve relevant episodic memories."""
        # Generate query embedding
        query_embedding = self.embedder.embed([query])[0]
        
        # Search vector store
        search_results = self._vector_store.search(query_embedding, top_k=top_k * 2)
        
        # Filter and convert to memory items
        relevant_memories = []
        for result in search_results:
            if result.id in self._memories:
                memory_item = self._memories[result.id]
                
                # Apply importance decay
                current_importance = self._calculate_decayed_importance(memory_item)
                
                if current_importance >= min_importance:
                    # Update importance in memory item
                    memory_item.importance = current_importance
                    relevant_memories.append(memory_item)
        
        return relevant_memories[:top_k]
    
    async def update(self, item_id: str, content: str = None,
                    importance: float = None, metadata: Dict[str, Any] = None) -> bool:
        """Update an episodic memory."""
        async with self._lock:
            if item_id not in self._memories:
                return False
            
            memory_item = self._memories[item_id]
            
            # Update fields
            if content is not None:
                memory_item.content = content
                # Regenerate embedding
                memory_item.embedding = self.embedder.embed([content])[0]
            
            if importance is not None:
                memory_item.importance = importance
            
            if metadata is not None:
                memory_item.metadata.update(metadata)
            
            # Update vector store
            if content is not None or importance is not None:
                vector_record = VectorRecord(
                    id=item_id,
                    vector=memory_item.embedding,
                    metadata={
                        "content": memory_item.content,
                        "importance": memory_item.importance,
                        "timestamp": memory_item.timestamp.isoformat(),
                        **memory_item.metadata
                    }
                )
                self._vector_store.add([vector_record])
            
            return True
    
    async def delete(self, item_id: str) -> bool:
        """Delete an episodic memory."""
        async with self._lock:
            if item_id in self._memories:
                del self._memories[item_id]
                # Note: InMemoryVectorStore doesn't have delete method
                # In production, use a vector store with delete capability
                return True
            return False
    
    async def get_recent_memories(self, hours: int = 24) -> List[MemoryItem]:
        """Get recent memories within specified time window."""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        recent_memories = [
            memory for memory in self._memories.values()
            if memory.timestamp >= cutoff_time
        ]
        
        # Sort by timestamp (most recent first)
        recent_memories.sort(key=lambda m: m.timestamp, reverse=True)
        
        return recent_memories
    
    def _calculate_decayed_importance(self, memory_item: MemoryItem) -> float:
        """Calculate importance with time-based decay."""
        days_old = (datetime.now() - memory_item.timestamp).days
        decay_factor = (1 - self.decay_rate) ** days_old
        return memory_item.importance * decay_factor
    
    async def _cleanup_old_memories(self) -> None:
        """Remove least important old memories."""
        # Calculate current importance for all memories
        memory_importance = [
            (item_id, self._calculate_decayed_importance(memory))
            for item_id, memory in self._memories.items()
        ]
        
        # Sort by importance (lowest first)
        memory_importance.sort(key=lambda x: x[1])
        
        # Remove least important memories
        memories_to_remove = len(self._memories) - self.max_memories + 100  # Remove extra for buffer
        
        for i in range(min(memories_to_remove, len(memory_importance))):
            item_id = memory_importance[i][0]
            del self._memories[item_id]
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get episodic memory statistics."""
        if not self._memories:
            return {
                "total_items": 0,
                "memory_type": "EpisodicMemory",
                "avg_importance": 0.0,
            }
        
        current_importances = [
            self._calculate_decayed_importance(memory)
            for memory in self._memories.values()
        ]
        
        return {
            "total_items": len(self._memories),
            "memory_type": "EpisodicMemory",
            "avg_importance": sum(current_importances) / len(current_importances),
            "oldest_memory": min(m.timestamp for m in self._memories.values()).isoformat(),
            "newest_memory": max(m.timestamp for m in self._memories.values()).isoformat(),
            "decay_rate": self.decay_rate,
        }

    # Sync wrapper methods for backward compatibility with tests
    def store_sync(self, key: str, content: str, importance: float = 0.5,
                   metadata: Dict[str, Any] = None) -> str:
        """Synchronous wrapper for store method."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.store(key, content, importance, metadata))
        finally:
            loop.close()

    def retrieve_sync(self, query: str, top_k: int = 5,
                      min_importance: float = 0.0) -> List[MemoryItem]:
        """Synchronous wrapper for retrieve method."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(self.retrieve(query, top_k, min_importance))
        finally:
            loop.close()

    def retrieve_by_importance(self, min_importance: float) -> List[MemoryItem]:
        """Retrieve memories by importance threshold (sync method for tests)."""
        return [memory for memory in self._memories.values()
                if self._calculate_decayed_importance(memory) >= min_importance]

    def decay_memories(self, decay_rate: float = None):
        """Apply decay to all memories (sync method for tests)."""
        if decay_rate is None:
            decay_rate = self.decay_rate

        for memory in self._memories.values():
            # Apply decay directly for testing purposes
            memory.importance *= (1 - decay_rate)

    def consolidate(self):
        """Consolidate memories by removing least important ones (sync method for tests)."""
        if len(self._memories) <= self.max_memories:
            return

        # Sort by current importance (with decay applied)
        memories_by_importance = sorted(
            self._memories.values(),
            key=lambda m: self._calculate_decayed_importance(m),
            reverse=True
        )

        # Keep only the most important memories
        memories_to_keep = memories_by_importance[:self.max_memories]

        # Clear and rebuild memory store
        self._memories.clear()
        for memory in memories_to_keep:
            self._memories[memory.id] = memory


class SemanticMemory(Memory):
    """Semantic memory for storing general knowledge and facts.
    
    Stores factual knowledge with concept clustering and
    relationship mapping for efficient knowledge retrieval.
    
    Features:
    - Concept-based organization
    - Fact verification and updating
    - Knowledge graph relationships
    - Automatic fact consolidation
    
    Example:
        >>> memory = SemanticMemory()
        >>> await memory.store("fact", "Paris is the capital of France", importance=1.0)
        >>> facts = await memory.retrieve("European capitals")
    """
    
    def __init__(self, embedder: Embedder = None, embedding_dim: int = None):
        """Initialize semantic memory.

        Args:
            embedder: Embedder for semantic search
            embedding_dim: Dimension of embeddings (for backward compatibility, ignored)
        """
        super().__init__(embedder)
        self._facts: Dict[str, MemoryItem] = {}
        self._vector_store = InMemoryVectorStore()
        self._concept_clusters: Dict[str, List[str]] = {}

    @property
    def facts(self) -> Dict[str, str]:
        """Get facts as a simple dict (for backward compatibility with tests)."""
        return {key: item.content for key, item in self._facts.items()}

    @property
    def embeddings(self) -> List[List[float]]:
        """Get embeddings as a list (for backward compatibility with tests)."""
        return [item.embedding for item in self._facts.values() if item.embedding]
    
    async def store(self, key: str, content: str, importance: float = 0.5,
                   metadata: Dict[str, Any] = None) -> str:
        """Store a semantic memory (fact or knowledge)."""
        async with self._lock:
            # Generate embedding
            embedding = self.embedder.embed([content])[0]
            
            # Create memory item
            item_id = str(uuid4())
            memory_item = MemoryItem(
                id=item_id,
                content=content,
                memory_type="semantic",
                importance=importance,
                timestamp=datetime.now(),
                metadata=metadata or {},
                embedding=embedding
            )
            
            # Store in memory
            self._facts[item_id] = memory_item
            
            # Store in vector store
            vector_record = VectorRecord(
                id=item_id,
                vector=embedding,
                metadata={
                    "content": content,
                    "importance": importance,
                    "key": key,
                    **memory_item.metadata
                }
            )
            self._vector_store.add([vector_record])
            
            return item_id
    
    async def retrieve(self, query: str, top_k: int = 5,
                      min_importance: float = 0.0) -> List[MemoryItem]:
        """Retrieve relevant semantic memories."""
        # Generate query embedding
        query_embedding = self.embedder.embed([query])[0]
        
        # Search vector store
        search_results = self._vector_store.search(query_embedding, top_k=top_k)
        
        # Convert to memory items and filter by importance
        relevant_memories = []
        for result in search_results:
            if result.id in self._facts:
                memory_item = self._facts[result.id]
                if memory_item.importance >= min_importance:
                    relevant_memories.append(memory_item)
        
        return relevant_memories
    
    async def update(self, item_id: str, content: str = None,
                    importance: float = None, metadata: Dict[str, Any] = None) -> bool:
        """Update a semantic memory."""
        async with self._lock:
            if item_id not in self._facts:
                return False
            
            memory_item = self._facts[item_id]
            
            # Update fields
            if content is not None:
                memory_item.content = content
                memory_item.embedding = self.embedder.embed([content])[0]
            
            if importance is not None:
                memory_item.importance = importance
            
            if metadata is not None:
                memory_item.metadata.update(metadata)
            
            return True
    
    async def delete(self, item_id: str) -> bool:
        """Delete a semantic memory."""
        async with self._lock:
            if item_id in self._facts:
                del self._facts[item_id]
                return True
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get semantic memory statistics."""
        if not self._facts:
            return {
                "total_items": 0,
                "memory_type": "SemanticMemory",
                "avg_importance": 0.0,
            }
        
        importances = [memory.importance for memory in self._facts.values()]
        
        return {
            "total_items": len(self._facts),
            "memory_type": "SemanticMemory",
            "avg_importance": sum(importances) / len(importances),
            "concept_clusters": len(self._concept_clusters),
        }

    # Sync wrapper methods for backward compatibility with tests
    def store_fact(self, key: str, content: str, importance: float = 1.0):
        """Store a fact synchronously (for backward compatibility with tests)."""
        # Generate a simple embedding (for test compatibility)
        embedding = self.embedder.embed([content])[0] if self.embedder else [0.1] * 8

        # Create a simple memory item for the facts dict
        memory_item = MemoryItem(
            id=key,
            content=content,
            memory_type="semantic",
            importance=importance,
            timestamp=datetime.now(),
            metadata={},
            embedding=embedding
        )

        # Store in the facts dict using the provided key
        self._facts[key] = memory_item

    def retrieve_fact(self, key: str) -> Optional[str]:
        """Retrieve a fact by key (for backward compatibility with tests)."""
        if key in self._facts:
            return self._facts[key].content
        return None

    def retrieve_similar(self, query: str, top_k: int = 5) -> List[str]:
        """Retrieve similar facts (for backward compatibility with tests)."""
        # Simple similarity based on content matching
        results = []
        query_lower = query.lower()

        for key, item in self._facts.items():
            if query_lower in item.content.lower():
                results.append(item.content)

        return results[:top_k]

    def update_fact(self, key: str, new_content: str):
        """Update a fact (for backward compatibility with tests)."""
        if key in self._facts:
            # Generate new embedding
            embedding = self.embedder.embed([new_content])[0] if self.embedder else [0.1] * 8

            # Update the memory item
            self._facts[key].content = new_content
            self._facts[key].embedding = embedding
            self._facts[key].timestamp = datetime.now()

    def remove_fact(self, key: str):
        """Remove a fact (for backward compatibility with tests)."""
        if key in self._facts:
            del self._facts[key]
