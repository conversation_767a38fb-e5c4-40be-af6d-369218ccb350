import React, { useMemo, useRef, useState } from 'react'
import { Button, Card, Col, Form, Input, Radio, Row, Space, Switch, Typography } from 'antd'
import { api, ChatMessage } from '../api/client'

export default function LLMs() {
  const [form] = Form.useForm()
  const [output, setOutput] = useState('')
  const streaming = useRef(false)

  const initial = useMemo(() => ({
    mode: 'mcp',
    model: 'llama3',
    prompt: 'Say hello.',
    stream: true,
  }), [])

  async function onSubmit(values: any) {
    setOutput('')
    const messages: ChatMessage[] = [{ role: 'user', content: values.prompt }]
    if (values.mode === 'edge') {
      if (values.stream) {
        streaming.current = true
        await api.edgeChatStream(messages, (delta) => {
          if (!streaming.current) return
          setOutput((prev) => prev + delta)
        }, values.model)
        streaming.current = false
      } else {
        const res = await api.edgeChat(messages, values.model)
        const text = res?.choices?.[0]?.message?.content || ''
        setOutput(text)
      }
    } else {
      const res = await api.mcpChat(messages)
      const text = res?.choices?.[0]?.message?.content || ''
      setOutput(text)
    }
  }

  return (
    <Row gutter={16}>
      <Col xs={24} lg={10}>
        <Card title="LLM Tester">
          <Form layout="vertical" initialValues={initial} form={form} onFinish={onSubmit}>
            <Form.Item label="Backend" name="mode">
              <Radio.Group>
                <Radio.Button value="edge">Edge (OpenAI-compatible)</Radio.Button>
                <Radio.Button value="mcp">MCP LLM Tool</Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="Model (Edge)" name="model">
              <Input placeholder="llama3" />
            </Form.Item>
            <Form.Item label="Prompt" name="prompt" rules={[{ required: true }]}>
              <Input.TextArea rows={6} placeholder="Enter your prompt..." />
            </Form.Item>
            <Space>
              <Form.Item label="Stream (Edge)" name="stream" valuePropName="checked">
                <Switch />
              </Form.Item>
              <Button type="primary" htmlType="submit">Send</Button>
            </Space>
          </Form>
        </Card>
      </Col>
      <Col xs={24} lg={14}>
        <Card title="Output">
          <Typography.Paragraph>
            <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{output}</pre>
          </Typography.Paragraph>
        </Card>
      </Col>
    </Row>
  )
}
