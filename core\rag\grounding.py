"""Grounding validation for RAG pipeline.

This module implements grounding validation to ensure that generated responses
are properly grounded in the retrieved context and to detect hallucinations.

The grounding system provides:
- Hallucination detection using multiple strategies
- Source attribution and citation validation
- Factual consistency checking
- Confidence scoring for generated content

Key Components:
- GroundingValidator: Base grounding validation interface
- HallucinationDetector: Detects hallucinated content
- SourceAttributor: Links generated content to sources
- FactualConsistencyChecker: Validates factual consistency

Example:
    >>> from core.rag import GroundingValidator, HallucinationDetector
    >>> validator = GroundingValidator()
    >>> detector = HallucinationDetector()
    >>> 
    >>> # Validate grounding
    >>> is_grounded = await validator.validate_grounding(
    ...     query="What is AI?",
    ...     context=retrieved_chunks,
    ...     response="AI is artificial intelligence..."
    ... )
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple, Set
from dataclasses import dataclass, field
from datetime import datetime
import re

from .retrieval import RetrievalResult
from .base import Chunk, RAGError

logger = logging.getLogger(__name__)


@dataclass
class GroundingResult:
    """Result from grounding validation."""
    is_grounded: bool
    confidence: float
    hallucination_score: float
    source_attributions: List[Dict[str, Any]] = field(default_factory=list)
    factual_consistency_score: float = 0.0
    explanation: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class SourceAttribution:
    """Attribution of generated content to source."""
    text_span: str
    source_chunk: Chunk
    confidence: float
    start_pos: int = 0
    end_pos: int = 0
    attribution_type: str = "direct"  # direct, paraphrased, inferred


class GroundingValidator(ABC):
    """Base class for grounding validation systems."""
    
    def __init__(self, confidence_threshold: float = 0.7):
        self.confidence_threshold = confidence_threshold
    
    @abstractmethod
    async def validate_grounding(self, query: str, context: List[RetrievalResult], 
                               response: str) -> GroundingResult:
        """Validate that the response is properly grounded in the context."""
        pass
    
    async def batch_validate(self, queries: List[str], 
                           contexts: List[List[RetrievalResult]], 
                           responses: List[str]) -> List[GroundingResult]:
        """Validate grounding for multiple query-context-response triplets."""
        tasks = [self.validate_grounding(query, context, response) 
                for query, context, response in zip(queries, contexts, responses)]
        return await asyncio.gather(*tasks)


class HallucinationDetector:
    """Detects hallucinated content in generated responses."""
    
    def __init__(self, similarity_threshold: float = 0.5, 
                 coverage_threshold: float = 0.3):
        self.similarity_threshold = similarity_threshold
        self.coverage_threshold = coverage_threshold
        self.embedder = None
    
    async def _get_embedder(self):
        """Lazy load embedder."""
        if self.embedder is None:
            from core.text.embeddings import get_embedder
            self.embedder = get_embedder("text-embedding-ada-002")
        return self.embedder
    
    def _extract_claims(self, text: str) -> List[str]:
        """Extract factual claims from text."""
        # Simple sentence splitting - can be enhanced with NLP
        sentences = re.split(r'[.!?]+', text)
        claims = [s.strip() for s in sentences if s.strip() and len(s.strip()) > 10]
        return claims
    
    async def _compute_semantic_similarity(self, text1: str, text2: str) -> float:
        """Compute semantic similarity between two texts."""
        try:
            embedder = await self._get_embedder()
            embeddings = await embedder.embed_batch([text1, text2])
            
            import numpy as np
            emb1, emb2 = np.array(embeddings[0]), np.array(embeddings[1])
            
            # Cosine similarity
            similarity = np.dot(emb1, emb2) / (np.linalg.norm(emb1) * np.linalg.norm(emb2))
            return float(similarity)
            
        except Exception as e:
            logger.error(f"Similarity computation failed: {e}")
            return 0.0
    
    async def detect_hallucination(self, response: str, 
                                 context: List[RetrievalResult]) -> Tuple[float, Dict[str, Any]]:
        """
        Detect hallucination in response given context.
        
        Returns:
            Tuple of (hallucination_score, details) where score is 0-1 
            (0 = no hallucination, 1 = complete hallucination)
        """
        if not context:
            return 1.0, {"reason": "no_context", "claims_checked": 0}
        
        try:
            # Extract claims from response
            response_claims = self._extract_claims(response)
            if not response_claims:
                return 0.0, {"reason": "no_claims", "claims_checked": 0}
            
            # Combine all context text
            context_text = " ".join([result.chunk.content for result in context])
            
            # Check each claim against context
            claim_scores = []
            unsupported_claims = []
            
            for claim in response_claims:
                # Compute similarity with full context
                similarity = await self._compute_semantic_similarity(claim, context_text)
                
                if similarity < self.similarity_threshold:
                    # Check against individual chunks for more granular analysis
                    max_chunk_similarity = 0.0
                    best_chunk = None
                    
                    for result in context:
                        chunk_similarity = await self._compute_semantic_similarity(
                            claim, result.chunk.content
                        )
                        if chunk_similarity > max_chunk_similarity:
                            max_chunk_similarity = chunk_similarity
                            best_chunk = result.chunk.id
                    
                    if max_chunk_similarity < self.similarity_threshold:
                        unsupported_claims.append({
                            "claim": claim,
                            "max_similarity": max_chunk_similarity,
                            "best_chunk": best_chunk
                        })
                        claim_scores.append(1.0)  # Hallucinated
                    else:
                        claim_scores.append(1.0 - max_chunk_similarity)
                else:
                    claim_scores.append(1.0 - similarity)
            
            # Compute overall hallucination score
            if claim_scores:
                avg_hallucination = sum(claim_scores) / len(claim_scores)
            else:
                avg_hallucination = 0.0
            
            # Coverage-based adjustment
            coverage_ratio = len(unsupported_claims) / len(response_claims)
            if coverage_ratio > self.coverage_threshold:
                avg_hallucination = min(avg_hallucination + 0.3, 1.0)
            
            details = {
                "claims_checked": len(response_claims),
                "unsupported_claims": len(unsupported_claims),
                "coverage_ratio": coverage_ratio,
                "unsupported_details": unsupported_claims[:3],  # Limit for brevity
                "avg_similarity": 1.0 - avg_hallucination
            }
            
            return avg_hallucination, details
            
        except Exception as e:
            logger.error(f"Hallucination detection failed: {e}")
            return 0.5, {"error": str(e), "claims_checked": 0}


class SourceAttributor:
    """Attributes generated content to specific sources."""
    
    def __init__(self, attribution_threshold: float = 0.6):
        self.attribution_threshold = attribution_threshold
        self.embedder = None
    
    async def _get_embedder(self):
        """Lazy load embedder."""
        if self.embedder is None:
            from core.text.embeddings import get_embedder
            self.embedder = get_embedder("text-embedding-ada-002")
        return self.embedder
    
    def _segment_text(self, text: str, segment_size: int = 50) -> List[Tuple[str, int, int]]:
        """Segment text into overlapping spans for attribution."""
        words = text.split()
        segments = []
        
        for i in range(0, len(words), segment_size // 2):  # 50% overlap
            segment_words = words[i:i + segment_size]
            if len(segment_words) < 5:  # Skip very short segments
                continue
            
            segment_text = " ".join(segment_words)
            start_pos = len(" ".join(words[:i]))
            end_pos = start_pos + len(segment_text)
            
            segments.append((segment_text, start_pos, end_pos))
        
        return segments
    
    async def attribute_sources(self, response: str, 
                              context: List[RetrievalResult]) -> List[SourceAttribution]:
        """Attribute parts of the response to specific source chunks."""
        if not context:
            return []
        
        try:
            # Segment response into attributable spans
            response_segments = self._segment_text(response)
            attributions = []
            
            embedder = await self._get_embedder()
            
            for segment_text, start_pos, end_pos in response_segments:
                best_attribution = None
                best_score = 0.0
                
                # Find best matching chunk for this segment
                for result in context:
                    chunk_text = result.chunk.content
                    
                    # Compute similarity
                    embeddings = await embedder.embed_batch([segment_text, chunk_text])
                    
                    import numpy as np
                    seg_emb, chunk_emb = np.array(embeddings[0]), np.array(embeddings[1])
                    similarity = np.dot(seg_emb, chunk_emb) / (
                        np.linalg.norm(seg_emb) * np.linalg.norm(chunk_emb)
                    )
                    
                    if similarity > best_score and similarity > self.attribution_threshold:
                        best_score = similarity
                        
                        # Determine attribution type
                        attribution_type = "direct" if similarity > 0.8 else "paraphrased"
                        
                        best_attribution = SourceAttribution(
                            text_span=segment_text,
                            source_chunk=result.chunk,
                            confidence=float(similarity),
                            start_pos=start_pos,
                            end_pos=end_pos,
                            attribution_type=attribution_type
                        )
                
                if best_attribution:
                    attributions.append(best_attribution)
            
            logger.debug(f"Generated {len(attributions)} source attributions")
            return attributions
            
        except Exception as e:
            logger.error(f"Source attribution failed: {e}")
            return []


class FactualConsistencyChecker:
    """Checks factual consistency between response and context."""
    
    def __init__(self, llm_adapter=None):
        self.llm_adapter = llm_adapter
        self.consistency_prompt = self._default_consistency_prompt()
    
    def _default_consistency_prompt(self) -> str:
        """Default prompt for factual consistency checking."""
        return """Check if the following response is factually consistent with the given context. Rate the consistency on a scale of 0-10, where 0 means completely inconsistent and 10 means perfectly consistent.

Context:
{context}

Response:
{response}

Consider:
1. Are all facts in the response supported by the context?
2. Are there any contradictions between the response and context?
3. Does the response make claims not found in the context?

Provide only a numeric score (0-10) as your response."""
    
    async def _get_llm_adapter(self):
        """Get LLM adapter for consistency checking."""
        if self.llm_adapter is None:
            from core.adapters.echo_adapter import EchoAdapter
            self.llm_adapter = EchoAdapter()
            logger.warning("Using EchoAdapter for consistency checking (not functional)")
        return self.llm_adapter
    
    async def check_consistency(self, response: str, 
                              context: List[RetrievalResult]) -> Tuple[float, Dict[str, Any]]:
        """
        Check factual consistency between response and context.
        
        Returns:
            Tuple of (consistency_score, details) where score is 0-1
        """
        if not context:
            return 0.0, {"reason": "no_context"}
        
        try:
            # Combine context
            context_text = "\n\n".join([
                f"Source {i+1}: {result.chunk.content}" 
                for i, result in enumerate(context)
            ])
            
            # Use LLM to check consistency
            llm = await self._get_llm_adapter()
            prompt = self.consistency_prompt.format(
                context=context_text[:2000],  # Limit context length
                response=response[:1000]      # Limit response length
            )
            
            from core.contracts.llm import ChatMessage, ChatCompletionRequest
            request = ChatCompletionRequest(
                messages=[ChatMessage(role="user", content=prompt)],
                temperature=0.0,
                max_tokens=10
            )
            
            llm_response = await llm.chat(request)
            score_text = llm_response.choices[0].message.content.strip()
            
            # Extract numeric score
            import re
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', score_text)
            if score_match:
                score = float(score_match.group(1))
                normalized_score = min(max(score, 0.0), 10.0) / 10.0
                
                details = {
                    "raw_score": score,
                    "llm_response": score_text,
                    "context_length": len(context_text),
                    "response_length": len(response)
                }
                
                return normalized_score, details
            else:
                logger.warning(f"Could not extract consistency score: {score_text}")
                return 0.5, {"error": "score_extraction_failed", "llm_response": score_text}
                
        except Exception as e:
            logger.error(f"Consistency checking failed: {e}")
            return 0.5, {"error": str(e)}


class ComprehensiveGroundingValidator(GroundingValidator):
    """Comprehensive grounding validator using multiple validation strategies."""
    
    def __init__(self, hallucination_weight: float = 0.4, 
                 attribution_weight: float = 0.3, 
                 consistency_weight: float = 0.3, **kwargs):
        super().__init__(**kwargs)
        self.hallucination_weight = hallucination_weight
        self.attribution_weight = attribution_weight
        self.consistency_weight = consistency_weight
        
        # Initialize components
        self.hallucination_detector = HallucinationDetector()
        self.source_attributor = SourceAttributor()
        self.consistency_checker = FactualConsistencyChecker()
    
    async def validate_grounding(self, query: str, context: List[RetrievalResult], 
                               response: str) -> GroundingResult:
        """Comprehensive grounding validation using multiple strategies."""
        try:
            # Run all validation components
            hallucination_task = self.hallucination_detector.detect_hallucination(response, context)
            attribution_task = self.source_attributor.attribute_sources(response, context)
            consistency_task = self.consistency_checker.check_consistency(response, context)
            
            (hallucination_score, hall_details), attributions, (consistency_score, cons_details) = await asyncio.gather(
                hallucination_task, attribution_task, consistency_task
            )
            
            # Compute attribution score
            if attributions:
                attribution_coverage = len(attributions) / max(len(response.split()), 1)
                avg_attribution_confidence = sum(attr.confidence for attr in attributions) / len(attributions)
                attribution_score = min(attribution_coverage * avg_attribution_confidence, 1.0)
            else:
                attribution_score = 0.0
            
            # Compute overall grounding confidence
            grounding_confidence = (
                (1.0 - hallucination_score) * self.hallucination_weight +
                attribution_score * self.attribution_weight +
                consistency_score * self.consistency_weight
            )
            
            # Determine if response is grounded
            is_grounded = grounding_confidence >= self.confidence_threshold
            
            # Create source attribution details
            attribution_details = [
                {
                    "text_span": attr.text_span,
                    "source_id": attr.source_chunk.id,
                    "confidence": attr.confidence,
                    "type": attr.attribution_type,
                    "start_pos": attr.start_pos,
                    "end_pos": attr.end_pos
                }
                for attr in attributions
            ]
            
            # Generate explanation
            explanation_parts = []
            if hallucination_score > 0.5:
                explanation_parts.append(f"High hallucination risk ({hallucination_score:.2f})")
            if attribution_score < 0.3:
                explanation_parts.append(f"Low source attribution ({attribution_score:.2f})")
            if consistency_score < 0.5:
                explanation_parts.append(f"Factual inconsistencies detected ({consistency_score:.2f})")
            
            explanation = "; ".join(explanation_parts) if explanation_parts else "Response appears well-grounded"
            
            result = GroundingResult(
                is_grounded=is_grounded,
                confidence=grounding_confidence,
                hallucination_score=hallucination_score,
                source_attributions=attribution_details,
                factual_consistency_score=consistency_score,
                explanation=explanation,
                metadata={
                    "hallucination_details": hall_details,
                    "consistency_details": cons_details,
                    "attribution_count": len(attributions),
                    "weights": {
                        "hallucination": self.hallucination_weight,
                        "attribution": self.attribution_weight,
                        "consistency": self.consistency_weight
                    }
                }
            )
            
            logger.debug(f"Grounding validation completed: {grounding_confidence:.3f} confidence")
            return result
            
        except Exception as e:
            logger.error(f"Grounding validation failed: {e}")
            return GroundingResult(
                is_grounded=False,
                confidence=0.0,
                hallucination_score=1.0,
                explanation=f"Validation failed: {e}",
                metadata={"error": str(e)}
            )
