"""Planning algorithms for agents."""

from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from ..contracts.llm import ChatCompletionRequest, ChatMessage
from ..adapters.base import BaseModelAdapter
from ..adapters.openai_adapter import EchoAdapter

logger = logging.getLogger(__name__)


class PlanStatus(Enum):
    """Status of plan execution."""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class PlanStep:
    """A single step in an execution plan."""
    id: str
    description: str
    action: str
    parameters: Dict[str, Any]
    dependencies: List[str]
    expected_output: str
    timeout_seconds: float
    retry_count: int
    status: PlanStatus = PlanStatus.PENDING
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "description": self.description,
            "action": self.action,
            "parameters": self.parameters,
            "dependencies": self.dependencies,
            "expected_output": self.expected_output,
            "timeout_seconds": self.timeout_seconds,
            "retry_count": self.retry_count,
            "status": self.status.value
        }


@dataclass
class Plan:
    """A complete execution plan with multiple steps."""
    id: str
    goal: str
    reasoning: str
    steps: List[PlanStep]
    estimated_time: float
    confidence: float
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "goal": self.goal,
            "reasoning": self.reasoning,
            "steps": [step.to_dict() for step in self.steps],
            "estimated_time": self.estimated_time,
            "confidence": self.confidence,
            "metadata": self.metadata
        }


class Planner(ABC):
    """Abstract base class for planning algorithms.
    
    Planners generate step-by-step plans to achieve given goals
    using available tools and considering constraints.
    
    Common planning approaches:
    - Chain of Thought (CoT): Sequential reasoning
    - Tree of Thoughts (ToT): Branching exploration
    - ReAct: Reasoning + Acting cycles
    - MCTS: Monte Carlo Tree Search
    
    Example:
        >>> planner = ReActPlanner(llm_adapter)
        >>> plan = await planner.create_plan(
        ...     goal="Find the weather in Paris",
        ...     available_tools=["search", "weather_api"],
        ...     constraints=["max_steps: 5"]
        ... )
    """
    
    def __init__(self, llm_adapter: BaseModelAdapter = None):
        """Initialize planner with LLM adapter.
        
        Args:
            llm_adapter: Language model for plan generation
        """
        self.llm_adapter = llm_adapter or EchoAdapter()
    
    @abstractmethod
    async def create_plan(self, goal: str, available_tools: List[str] = None,
                         constraints: List[str] = None, context: Dict[str, Any] = None) -> Plan:
        """Create a plan to achieve the given goal.
        
        Args:
            goal: The objective to achieve
            available_tools: List of available tools/actions
            constraints: List of constraints to consider
            context: Additional context information
            
        Returns:
            Plan with steps to achieve the goal
        """
        pass
    
    async def refine_plan(self, plan: Plan, feedback: str) -> Plan:
        """Refine an existing plan based on feedback.
        
        Args:
            plan: Original plan to refine
            feedback: Feedback on plan execution or quality
            
        Returns:
            Refined plan
        """
        # Default implementation: return original plan
        return plan
    
    def validate_plan(self, plan: Plan) -> List[str]:
        """Validate a plan and return list of issues.
        
        Args:
            plan: Plan to validate
            
        Returns:
            List of validation issues (empty if valid)
        """
        issues = []
        
        # Check for circular dependencies
        step_ids = {step.id for step in plan.steps}
        for step in plan.steps:
            for dep in step.dependencies:
                if dep not in step_ids:
                    issues.append(f"Step {step.id} depends on non-existent step {dep}")
        
        # Check for self-dependencies
        for step in plan.steps:
            if step.id in step.dependencies:
                issues.append(f"Step {step.id} has circular dependency on itself")
        
        return issues


class CoTPlanner(Planner):
    """Chain of Thought planner using sequential reasoning.
    
    Generates plans by breaking down goals into sequential steps
    using natural language reasoning with an LLM.
    
    Features:
    - Step-by-step decomposition
    - Natural language reasoning
    - Dependency tracking
    - Confidence estimation
    
    Example:
        >>> planner = CoTPlanner(llm_adapter)
        >>> plan = await planner.create_plan("Book a flight to Tokyo")
    """
    
    async def create_plan(self, goal: str, available_tools: List[str] = None,
                         constraints: List[str] = None, context: Dict[str, Any] = None) -> Plan:
        """Create plan using Chain of Thought reasoning."""
        
        # Build prompt for plan generation
        prompt = self._build_planning_prompt(goal, available_tools, constraints, context)
        
        # Generate plan using LLM
        request = ChatCompletionRequest(
            messages=[ChatMessage(role="user", content=prompt)],
            temperature=0.7,
            max_tokens=1000
        )
        
        response = await self.llm_adapter.chat(request)
        plan_text = response.choices[0].message.content
        
        # Parse plan from LLM response
        plan = self._parse_plan_response(goal, plan_text)
        
        return plan
    
    def _build_planning_prompt(self, goal: str, available_tools: List[str] = None,
                              constraints: List[str] = None, context: Dict[str, Any] = None) -> str:
        """Build prompt for plan generation."""
        
        prompt_parts = [
            f"Goal: {goal}",
            "",
            "Please create a step-by-step plan to achieve this goal.",
            "Think through this step by step:",
            "",
            "1. What is the main objective?",
            "2. What are the key sub-tasks?",
            "3. What is the logical order of steps?",
            "4. What tools or resources are needed?",
            ""
        ]
        
        if available_tools:
            prompt_parts.extend([
                "Available tools:",
                *[f"- {tool}" for tool in available_tools],
                ""
            ])
        
        if constraints:
            prompt_parts.extend([
                "Constraints:",
                *[f"- {constraint}" for constraint in constraints],
                ""
            ])
        
        if context:
            prompt_parts.extend([
                "Additional context:",
                *[f"- {k}: {v}" for k, v in context.items()],
                ""
            ])
        
        prompt_parts.extend([
            "Please provide your plan in this format:",
            "REASONING: [Your step-by-step thinking]",
            "",
            "PLAN:",
            "Step 1: [Description] - Action: [action_name] - Expected: [expected_output]",
            "Step 2: [Description] - Action: [action_name] - Expected: [expected_output]",
            "...",
            "",
            "CONFIDENCE: [0.0-1.0]",
            "ESTIMATED_TIME: [minutes]"
        ])
        
        return "\n".join(prompt_parts)
    
    def _parse_plan_response(self, goal: str, response: str) -> Plan:
        """Parse LLM response into a Plan object."""
        
        # Simple parsing - in production, use more robust parsing
        lines = response.strip().split('\n')
        
        reasoning = ""
        steps = []
        confidence = 0.7
        estimated_time = 30.0
        
        current_section = None
        
        for line in lines:
            line = line.strip()
            
            if line.startswith("REASONING:"):
                current_section = "reasoning"
                reasoning = line[10:].strip()
            elif line.startswith("PLAN:"):
                current_section = "plan"
            elif line.startswith("CONFIDENCE:"):
                try:
                    confidence = float(line[11:].strip())
                except ValueError:
                    confidence = 0.7
            elif line.startswith("ESTIMATED_TIME:"):
                try:
                    estimated_time = float(line[15:].strip())
                except ValueError:
                    estimated_time = 30.0
            elif current_section == "reasoning" and line:
                reasoning += " " + line
            elif current_section == "plan" and line.startswith("Step"):
                # Parse step: "Step 1: Description - Action: action - Expected: output"
                try:
                    parts = line.split(" - ")
                    if len(parts) >= 3:
                        step_desc = parts[0].split(": ", 1)[1] if ": " in parts[0] else parts[0]
                        action = parts[1].split(": ", 1)[1] if ": " in parts[1] else "unknown"
                        expected = parts[2].split(": ", 1)[1] if ": " in parts[2] else "unknown"
                        
                        step = PlanStep(
                            id=f"step_{len(steps) + 1}",
                            description=step_desc,
                            action=action,
                            parameters={},
                            dependencies=[],
                            expected_output=expected,
                            timeout_seconds=300.0,
                            retry_count=3
                        )
                        steps.append(step)
                except Exception as e:
                    logger.warning(f"Failed to parse step: {line} - {e}")
        
        # Create plan
        plan = Plan(
            id=f"plan_{hash(goal) % 10000}",
            goal=goal,
            reasoning=reasoning.strip(),
            steps=steps,
            estimated_time=estimated_time,
            confidence=confidence,
            metadata={"planner": "CoTPlanner"}
        )
        
        return plan


class ReActPlanner(Planner):
    """ReAct (Reasoning + Acting) planner.
    
    Generates plans that interleave reasoning and action steps,
    allowing for dynamic adaptation based on intermediate results.
    
    Features:
    - Interleaved reasoning and action
    - Dynamic plan adaptation
    - Observation-based replanning
    - Tool-aware planning
    
    Example:
        >>> planner = ReActPlanner(llm_adapter)
        >>> plan = await planner.create_plan("Research AI safety papers")
    """
    
    async def create_plan(self, goal: str, available_tools: List[str] = None,
                         constraints: List[str] = None, context: Dict[str, Any] = None) -> Plan:
        """Create plan using ReAct methodology."""
        
        # Build ReAct prompt
        prompt = self._build_react_prompt(goal, available_tools, constraints, context)
        
        # Generate plan using LLM
        request = ChatCompletionRequest(
            messages=[ChatMessage(role="user", content=prompt)],
            temperature=0.7,
            max_tokens=1200
        )
        
        response = await self.llm_adapter.chat(request)
        plan_text = response.choices[0].message.content
        
        # Parse ReAct plan
        plan = self._parse_react_plan(goal, plan_text)
        
        return plan
    
    def _build_react_prompt(self, goal: str, available_tools: List[str] = None,
                           constraints: List[str] = None, context: Dict[str, Any] = None) -> str:
        """Build ReAct planning prompt."""
        
        prompt_parts = [
            f"Goal: {goal}",
            "",
            "Create a ReAct plan that interleaves Thought, Action, and Observation steps.",
            "Use this format:",
            "",
            "Thought 1: [Your reasoning about what to do first]",
            "Action 1: [Specific action to take]",
            "Observation 1: [Expected result/observation]",
            "",
            "Thought 2: [Reasoning based on previous observation]", 
            "Action 2: [Next action to take]",
            "Observation 2: [Expected result/observation]",
            "",
            "Continue until the goal is achieved.",
            ""
        ]
        
        if available_tools:
            prompt_parts.extend([
                "Available tools:",
                *[f"- {tool}" for tool in available_tools],
                ""
            ])
        
        if constraints:
            prompt_parts.extend([
                "Constraints:",
                *[f"- {constraint}" for constraint in constraints],
                ""
            ])
        
        prompt_parts.extend([
            "End with:",
            "CONFIDENCE: [0.0-1.0]",
            "ESTIMATED_TIME: [minutes]"
        ])
        
        return "\n".join(prompt_parts)
    
    def _parse_react_plan(self, goal: str, response: str) -> Plan:
        """Parse ReAct response into Plan object."""
        
        lines = response.strip().split('\n')
        steps = []
        confidence = 0.7
        estimated_time = 45.0
        reasoning_parts = []
        
        current_thought = ""
        current_action = ""
        current_observation = ""
        
        for line in lines:
            line = line.strip()
            
            if line.startswith("Thought"):
                if current_action:  # Save previous step
                    step = PlanStep(
                        id=f"step_{len(steps) + 1}",
                        description=f"Think: {current_thought}",
                        action="reasoning",
                        parameters={"thought": current_thought},
                        dependencies=[],
                        expected_output=current_observation,
                        timeout_seconds=60.0,
                        retry_count=1
                    )
                    steps.append(step)
                
                current_thought = line.split(": ", 1)[1] if ": " in line else line
                reasoning_parts.append(current_thought)
                
            elif line.startswith("Action"):
                current_action = line.split(": ", 1)[1] if ": " in line else line
                
            elif line.startswith("Observation"):
                current_observation = line.split(": ", 1)[1] if ": " in line else line
                
                # Create action step
                if current_action:
                    step = PlanStep(
                        id=f"step_{len(steps) + 1}",
                        description=f"Execute: {current_action}",
                        action=current_action.lower().replace(" ", "_"),
                        parameters={"description": current_action},
                        dependencies=[],
                        expected_output=current_observation,
                        timeout_seconds=300.0,
                        retry_count=3
                    )
                    steps.append(step)
                
                current_action = ""
                
            elif line.startswith("CONFIDENCE:"):
                try:
                    confidence = float(line[11:].strip())
                except ValueError:
                    confidence = 0.7
                    
            elif line.startswith("ESTIMATED_TIME:"):
                try:
                    estimated_time = float(line[15:].strip())
                except ValueError:
                    estimated_time = 45.0
        
        # Handle final step if exists
        if current_action:
            step = PlanStep(
                id=f"step_{len(steps) + 1}",
                description=f"Execute: {current_action}",
                action=current_action.lower().replace(" ", "_"),
                parameters={"description": current_action},
                dependencies=[],
                expected_output=current_observation,
                timeout_seconds=300.0,
                retry_count=3
            )
            steps.append(step)
        
        plan = Plan(
            id=f"react_plan_{hash(goal) % 10000}",
            goal=goal,
            reasoning=" -> ".join(reasoning_parts),
            steps=steps,
            estimated_time=estimated_time,
            confidence=confidence,
            metadata={"planner": "ReActPlanner", "methodology": "ReAct"}
        )
        
        return plan
