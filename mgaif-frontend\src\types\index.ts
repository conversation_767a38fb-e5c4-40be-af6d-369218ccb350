// User and Authentication Types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Project Types
export interface Project {
  id: string;
  name: string;
  description: string;
  type: 'workflow' | 'chatbot' | 'agent';
  status: 'draft' | 'active' | 'archived';
  createdAt: string;
  updatedAt: string;
  userId: string;
}

export interface ProjectFilters {
  type?: string[];
  status?: string[];
  sortBy?: 'name' | 'createdAt' | 'updatedAt';
  sortOrder?: 'asc' | 'desc';
  search?: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Types
export interface LoginFormData {
  email: string;
  password: string;
}

export interface RegisterFormData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  details?: Record<string, any>;
}

// Loading States
export type LoadingState = 'idle' | 'loading' | 'success' | 'error';

// Theme Types
export type Theme = 'light' | 'dark';

// RAG Data Store Types
export interface RAGDocument {
  id: string;
  name: string;
  type: 'pdf' | 'txt' | 'docx' | 'md' | 'url';
  size: number;
  uploadedAt: string;
  status: 'pending' | 'processing' | 'indexed' | 'failed';
  chunks: number;
  metadata: Record<string, any>;
  error?: string;
}

export interface ChunkingStrategy {
  type: 'fixed' | 'semantic' | 'recursive';
  chunkSize: number;
  chunkOverlap: number;
  separators?: string[];
  metadata?: Record<string, any>;
}

export interface VectorStore {
  id: string;
  name: string;
  description: string;
  embeddingModel: string;
  dimensions: number;
  documentCount: number;
  chunkCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface SearchResult {
  id: string;
  content: string;
  score: number;
  metadata: Record<string, any>;
  documentId: string;
  documentName: string;
}

// Small Language Model Types
export interface SLMConfig {
  id: string;
  name: string;
  description: string;
  baseModel: string;
  modelType: 'chat' | 'completion' | 'embedding';
  parameters: {
    temperature: number;
    maxTokens: number;
    topP: number;
    frequencyPenalty: number;
    presencePenalty: number;
  };
  fineTuning?: FineTuningConfig;
  status: 'draft' | 'training' | 'ready' | 'deployed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface FineTuningConfig {
  trainingData: TrainingDataset[];
  hyperparameters: {
    learningRate: number;
    batchSize: number;
    epochs: number;
    warmupSteps: number;
  };
  validationSplit: number;
  status: 'pending' | 'running' | 'completed' | 'failed';
  progress?: number;
  metrics?: TrainingMetrics;
}

export interface TrainingDataset {
  id: string;
  name: string;
  type: 'chat' | 'completion' | 'classification';
  size: number;
  examples: TrainingExample[];
  uploadedAt: string;
}

export interface TrainingExample {
  input: string;
  output: string;
  metadata?: Record<string, any>;
}

export interface TrainingMetrics {
  loss: number;
  accuracy: number;
  perplexity: number;
  validationLoss: number;
  validationAccuracy: number;
}

// OpenAPI Builder Types
export interface APIEndpoint {
  id: string;
  path: string;
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  summary: string;
  description: string;
  parameters: APIParameter[];
  requestBody?: APIRequestBody;
  responses: APIResponse[];
  security?: string[];
}

export interface APIParameter {
  name: string;
  in: 'query' | 'path' | 'header' | 'cookie';
  required: boolean;
  schema: JSONSchema;
  description?: string;
}

export interface APIRequestBody {
  required: boolean;
  content: Record<string, { schema: JSONSchema }>;
  description?: string;
}

export interface APIResponse {
  statusCode: string;
  description: string;
  content?: Record<string, { schema: JSONSchema }>;
}

export interface JSONSchema {
  type: 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object';
  properties?: Record<string, JSONSchema>;
  items?: JSONSchema;
  required?: string[];
  enum?: any[];
  format?: string;
  example?: any;
}

export interface APISpec {
  id: string;
  name: string;
  description: string;
  version: string;
  baseUrl: string;
  endpoints: APIEndpoint[];
  security: SecurityScheme[];
  createdAt: string;
  updatedAt: string;
}

export interface SecurityScheme {
  type: 'apiKey' | 'http' | 'oauth2' | 'openIdConnect';
  name?: string;
  in?: 'query' | 'header' | 'cookie';
  scheme?: string;
  bearerFormat?: string;
}

// MCP Integration Types
export interface MCPIntegration {
  id: string;
  name: string;
  description: string;
  toolType: 'web-api' | 'database' | 'file-system' | 'external-service';
  configuration: MCPConfiguration;
  status: 'draft' | 'testing' | 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface MCPConfiguration {
  endpoint?: string;
  authentication?: {
    type: 'none' | 'api-key' | 'bearer' | 'basic';
    credentials?: Record<string, string>;
  };
  parameters?: MCPParameter[];
  responseMapping?: Record<string, string>;
  errorHandling?: {
    retries: number;
    timeout: number;
    fallback?: string;
  };
}

export interface MCPParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

// Agent Configuration Types (Extended)
export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  purpose: string;
  capabilities: AgentCapability[];
  tools: AgentTool[];
  planningStrategy: 'chain-of-thought' | 'react' | 'plan-and-execute';
  memoryConfig: AgentMemoryConfig;
  personality: AgentPersonality;
  constraints: AgentConstraint[];
  status: 'draft' | 'testing' | 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
}

export interface AgentCapability {
  type: 'reasoning' | 'planning' | 'memory' | 'tool-use' | 'communication';
  enabled: boolean;
  configuration?: Record<string, any>;
}

export interface AgentTool {
  id: string;
  name: string;
  type: 'built-in' | 'custom' | 'mcp-integration';
  description: string;
  parameters: Record<string, any>;
  enabled: boolean;
}

export interface AgentMemoryConfig {
  episodic: {
    enabled: boolean;
    maxEntries: number;
    decayRate: number;
  };
  semantic: {
    enabled: boolean;
    embeddingModel: string;
    maxFacts: number;
  };
  persistent: {
    enabled: boolean;
    storageType: 'local' | 'database' | 'vector-store';
  };
}

export interface AgentPersonality {
  tone: 'professional' | 'friendly' | 'casual' | 'formal';
  verbosity: 'concise' | 'detailed' | 'verbose';
  creativity: number; // 0-1
  riskTolerance: number; // 0-1
  customInstructions?: string;
}

export interface AgentConstraint {
  type: 'time' | 'resource' | 'ethical' | 'domain';
  description: string;
  value: any;
  enforced: boolean;
}
