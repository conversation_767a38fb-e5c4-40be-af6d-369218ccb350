#!/usr/bin/env python3
"""Simple benchmark runner for M-GAIF."""

import asyncio
import time
import json
import logging
from typing import Dict, Any

# Import core components for local benchmarking
from plugins.tokenizers.simple_tokenizer import SimpleTokenizer
from plugins.embedders.simple_embedder import SimpleEmbedder
from plugins.retrievers.in_memory_retriever import InMemoryRetriever
from core.workflow.engine import WorkflowEngine
from core.workflow.schema import WorkflowSpec

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LocalBenchmark:
    """Local performance benchmarking without API calls."""
    
    def __init__(self):
        self.results = {}
    
    def benchmark_tokenizer(self, num_iterations: int = 1000) -> Dict[str, Any]:
        """Benchmark tokenizer performance."""
        logger.info(f"Benchmarking tokenizer with {num_iterations} iterations")
        
        tokenizer = SimpleTokenizer()
        test_texts = [
            "Hello, world!",
            "This is a longer test sentence with more words to tokenize.",
            "The quick brown fox jumps over the lazy dog. " * 10,
            "A" * 1000,  # Long single character
        ]
        
        times = []
        total_tokens = 0
        
        start_time = time.time()
        
        for i in range(num_iterations):
            text = test_texts[i % len(test_texts)]
            iter_start = time.time()
            tokens = tokenizer.encode(text)
            iter_end = time.time()
            
            times.append(iter_end - iter_start)
            total_tokens += len(tokens)
        
        end_time = time.time()
        
        return {
            "component": "tokenizer",
            "iterations": num_iterations,
            "total_time": end_time - start_time,
            "avg_time_per_operation": sum(times) / len(times),
            "min_time": min(times),
            "max_time": max(times),
            "operations_per_second": num_iterations / (end_time - start_time),
            "total_tokens_processed": total_tokens,
            "tokens_per_second": total_tokens / (end_time - start_time)
        }
    
    def benchmark_embedder(self, num_iterations: int = 100) -> Dict[str, Any]:
        """Benchmark embedder performance."""
        logger.info(f"Benchmarking embedder with {num_iterations} iterations")
        
        embedder = SimpleEmbedder()
        test_texts = [
            "Hello, world!",
            "This is a test sentence for embedding.",
            "Machine learning and artificial intelligence are fascinating fields.",
            "The quick brown fox jumps over the lazy dog.",
        ]
        
        times = []
        total_embeddings = 0
        
        start_time = time.time()
        
        for i in range(num_iterations):
            text = test_texts[i % len(test_texts)]
            iter_start = time.time()
            embedding = embedder.embed(text)
            iter_end = time.time()
            
            times.append(iter_end - iter_start)
            total_embeddings += 1
        
        end_time = time.time()
        
        return {
            "component": "embedder",
            "iterations": num_iterations,
            "total_time": end_time - start_time,
            "avg_time_per_operation": sum(times) / len(times),
            "min_time": min(times),
            "max_time": max(times),
            "operations_per_second": num_iterations / (end_time - start_time),
            "total_embeddings_generated": total_embeddings
        }
    
    def benchmark_retriever(self, num_docs: int = 1000, num_queries: int = 100) -> Dict[str, Any]:
        """Benchmark retriever performance."""
        logger.info(f"Benchmarking retriever with {num_docs} docs and {num_queries} queries")
        
        retriever = InMemoryRetriever()
        
        # Index documents
        docs_to_index = [(f"doc_{i}", f"This is document {i} with some test content.") for i in range(num_docs)]

        add_start = time.time()
        retriever.index(docs_to_index)
        add_end = time.time()

        # Query documents
        query_times = []
        query_start = time.time()

        for i in range(num_queries):
            q_start = time.time()
            results = retriever.search(f"test query {i}", top_k=5)
            q_end = time.time()
            query_times.append(q_end - q_start)
        
        query_end = time.time()
        
        return {
            "component": "retriever",
            "num_documents": num_docs,
            "num_queries": num_queries,
            "document_index_time": add_end - add_start,
            "query_time": query_end - query_start,
            "avg_query_time": sum(query_times) / len(query_times),
            "documents_per_second": num_docs / (add_end - add_start),
            "queries_per_second": num_queries / (query_end - query_start)
        }
    
    def benchmark_workflow_engine(self, num_workflows: int = 50) -> Dict[str, Any]:
        """Benchmark workflow engine performance."""
        logger.info(f"Benchmarking workflow engine with {num_workflows} workflows")
        
        # Simple workflow spec
        workflow_spec = WorkflowSpec(
            name="test_workflow",
            nodes=[
                {
                    "id": "tokenize",
                    "handler": "tokenize",
                    "params": {"text": "Hello, world!"}
                },
                {
                    "id": "embed",
                    "handler": "embed",
                    "params": {"text": "Hello, world!"}
                }
            ],
            edges=[
                {"from": "tokenize", "to": "embed"}
            ]
        )

        engine = WorkflowEngine(workflow_spec)
        
        times = []
        successful_runs = 0
        
        start_time = time.time()
        
        for i in range(num_workflows):
            try:
                run_start = time.time()
                result = asyncio.run(engine.run())
                run_end = time.time()
                
                times.append(run_end - run_start)
                successful_runs += 1
            except Exception as e:
                logger.warning(f"Workflow {i} failed: {e}")
        
        end_time = time.time()
        
        return {
            "component": "workflow_engine",
            "total_workflows": num_workflows,
            "successful_workflows": successful_runs,
            "failed_workflows": num_workflows - successful_runs,
            "total_time": end_time - start_time,
            "avg_time_per_workflow": sum(times) / len(times) if times else 0,
            "min_time": min(times) if times else 0,
            "max_time": max(times) if times else 0,
            "workflows_per_second": successful_runs / (end_time - start_time) if (end_time - start_time) > 0 else 0
        }
    
    def run_all_benchmarks(self) -> Dict[str, Any]:
        """Run all local benchmarks."""
        logger.info("Starting local performance benchmarks")
        
        results = {
            "timestamp": time.time(),
            "benchmarks": {}
        }
        
        # Run individual benchmarks
        results["benchmarks"]["tokenizer"] = self.benchmark_tokenizer(1000)
        results["benchmarks"]["embedder"] = self.benchmark_embedder(100)
        results["benchmarks"]["retriever"] = self.benchmark_retriever(500, 50)
        results["benchmarks"]["workflow_engine"] = self.benchmark_workflow_engine(25)
        
        # Calculate overall metrics
        results["summary"] = self._calculate_summary(results["benchmarks"])
        
        return results
    
    def _calculate_summary(self, benchmarks: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary metrics."""
        total_operations = 0
        total_time = 0
        
        for component, data in benchmarks.items():
            if "iterations" in data:
                total_operations += data["iterations"]
                total_time += data["total_time"]
            elif "total_workflows" in data:
                total_operations += data["successful_workflows"]
                total_time += data["total_time"]
            elif "num_queries" in data:
                total_operations += data["num_queries"]
                total_time += data["query_time"]
        
        return {
            "total_operations": total_operations,
            "total_time": total_time,
            "overall_operations_per_second": total_operations / total_time if total_time > 0 else 0
        }


def main():
    """Main benchmark execution."""
    benchmark = LocalBenchmark()
    
    try:
        results = benchmark.run_all_benchmarks()
        
        # Print results
        print("\n" + "="*60)
        print("LOCAL PERFORMANCE BENCHMARK RESULTS")
        print("="*60)
        
        for component, data in results["benchmarks"].items():
            print(f"\n{component.upper()}:")
            for key, value in data.items():
                if isinstance(value, float):
                    print(f"  {key}: {value:.4f}")
                else:
                    print(f"  {key}: {value}")
        
        print(f"\nSUMMARY:")
        summary = results["summary"]
        for key, value in summary.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.4f}")
            else:
                print(f"  {key}: {value}")
        
        # Save results
        with open("local_benchmark_results.json", "w") as f:
            json.dump(results, f, indent=2)
        
        logger.info("Local benchmark results saved to local_benchmark_results.json")
        
        return 0
        
    except Exception as e:
        logger.error(f"Benchmark failed: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
