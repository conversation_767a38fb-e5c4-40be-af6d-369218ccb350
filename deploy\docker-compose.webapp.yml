services:
  webapp:
    image: node:18-alpine
    working_dir: /app
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"
    ports:
      - "5173:5173"
    environment:
      # Point the frontend proxy to your backend (Edge/MCP) running on the host
      - VITE_API_BASE=http://host.docker.internal:8000
    volumes:
      # Mount source for live reload
      - "Z:/Modular Generative AI Framework/webapp:/app"
      # Keep node_modules inside the container (avoids Windows permission issues)
      - webapp_node_modules:/app/node_modules
    extra_hosts:
      - "host.docker.internal:host-gateway"

  webapp_nomount:
    build:
      context: ../webapp
      dockerfile: Dockerfile.dev
    environment:
      - VITE_API_BASE=http://host.docker.internal:8000
    ports:
      - "5173:5173"
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  webapp_node_modules: {}
