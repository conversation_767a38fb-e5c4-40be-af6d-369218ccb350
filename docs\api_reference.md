# M-GAIF API Reference

## Overview

M-GAIF provides multiple API interfaces for different use cases:

- **Edge API**: OpenAI-compatible chat completions with streaming
- **MCP API**: Tool-based endpoints for modular operations
- **Plugin API**: Dynamic plugin management and health monitoring
- **Agent API**: Agent execution and coordination endpoints
- **Security API**: Authentication and authorization endpoints

## Authentication

All API endpoints (except health checks) require authentication via JWT Bearer token:

```http
Authorization: Bearer <jwt_token>
```

### Obtaining a Token

**POST /auth/login**

```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer",
  "expires_in": 86400,
  "user_id": "user_123",
  "roles": ["developer"]
}
```

## Edge API

OpenAI-compatible interface for chat completions with enterprise security.

### POST /v1/chat/completions

Execute chat completion with optional streaming.

**Request:**
```json
{
  "model": "echo",
  "messages": [
    {"role": "system", "content": "You are a helpful assistant."},
    {"role": "user", "content": "Hello, how are you?"}
  ],
  "stream": false,
  "temperature": 0.7,
  "max_tokens": 150
}
```

**Response:**
```json
{
  "id": "chatcmpl-123",
  "object": "chat.completion",
  "created": **********,
  "model": "echo",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Hello! I'm doing well, thank you for asking."
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 20,
    "completion_tokens": 12,
    "total_tokens": 32
  }
}
```

### GET /health

Health check endpoint for monitoring.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "version": "1.0.0",
  "components": {
    "plugin_system": "healthy",
    "agent_system": "healthy",
    "security_system": "healthy"
  }
}
```

### GET /metrics

Prometheus-compatible metrics endpoint.

## MCP API

Tool-based endpoints for modular operations with fine-grained access control.

### POST /mcp/tools/tokenize

Tokenize text using configured tokenizer plugin.

**Request:**
```json
{
  "text": "Hello, world! How are you today?",
  "tokenizer": "simple"
}
```

**Response:**
```json
{
  "tokens": ["Hello", ",", "world", "!", "How", "are", "you", "today", "?"],
  "token_count": 9,
  "tokenizer_used": "simple",
  "metadata": {
    "processing_time": 0.001,
    "vocab_size": 10000
  }
}
```

### POST /mcp/tools/embed

Generate embeddings for text using configured embedder plugin.

**Request:**
```json
{
  "texts": ["Hello world", "How are you?"],
  "embedder": "simple",
  "normalize": true
}
```

**Response:**
```json
{
  "embeddings": [
    [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8],
    [0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
  ],
  "dimensions": 8,
  "embedder_used": "simple",
  "metadata": {
    "processing_time": 0.005,
    "normalized": true
  }
}
```

### POST /mcp/tools/retrieve

Retrieve relevant documents using configured retriever plugin.

**Request:**
```json
{
  "query": "machine learning algorithms",
  "retriever": "in_memory",
  "top_k": 5,
  "threshold": 0.7
}
```

**Response:**
```json
{
  "results": [
    {
      "document_id": "doc_123",
      "content": "Machine learning algorithms are computational methods...",
      "score": 0.95,
      "metadata": {
        "title": "Introduction to ML",
        "author": "Jane Doe"
      }
    }
  ],
  "query_embedding": [0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8],
  "retriever_used": "in_memory",
  "metadata": {
    "processing_time": 0.010,
    "total_documents": 1000
  }
}
```

### POST /mcp/tools/workflow.run

Execute a workflow using the workflow engine.

**Request:**
```json
{
  "workflow_id": "rag_pipeline",
  "inputs": {
    "query": "What is artificial intelligence?",
    "documents": ["doc1.txt", "doc2.txt"]
  },
  "config": {
    "max_steps": 10,
    "timeout": 300
  }
}
```

**Response:**
```json
{
  "workflow_id": "rag_pipeline",
  "execution_id": "exec_456",
  "status": "completed",
  "result": {
    "answer": "Artificial intelligence is the simulation of human intelligence...",
    "sources": ["doc1.txt", "doc2.txt"],
    "confidence": 0.92
  },
  "metadata": {
    "steps_executed": 5,
    "execution_time": 1.2,
    "nodes_processed": ["retrieve", "rank", "generate", "validate"]
  }
}
```

## Plugin Management API

### GET /admin/plugins

List all registered plugins and their status.

**Response:**
```json
{
  "plugins": [
    {
      "name": "simple_tokenizer",
      "type": "tokenizer",
      "status": "active",
      "version": "1.0.0",
      "health": "healthy",
      "metrics": {
        "requests_processed": 1234,
        "avg_processing_time": 0.001
      }
    }
  ],
  "total_plugins": 5,
  "active_plugins": 4
}
```

### POST /admin/plugins/{plugin_name}/hot-swap

Hot-swap a plugin with zero downtime.

**Request:**
```json
{
  "old_version": "simple_tokenizer_v1",
  "new_version": "simple_tokenizer_v2",
  "config": {
    "vocab_size": 50000,
    "special_tokens": ["<pad>", "<unk>"]
  },
  "strategy": "canary",
  "traffic_split": 0.1
}
```

**Response:**
```json
{
  "swap_id": "swap_789",
  "status": "in_progress",
  "strategy": "canary",
  "traffic_split": 0.1,
  "estimated_completion": "2024-01-15T10:35:00Z"
}
```

## Agent API

### POST /agents/execute

Execute an agent task.

**Request:**
```json
{
  "agent_type": "research_agent",
  "goal": "Research the latest developments in quantum computing",
  "context": {
    "max_steps": 10,
    "timeout": 300,
    "available_tools": ["search", "summarize"]
  }
}
```

**Response:**
```json
{
  "execution_id": "exec_123",
  "agent_type": "research_agent",
  "status": "completed",
  "result": {
    "summary": "Recent developments in quantum computing include...",
    "sources": ["arxiv.org/paper1", "nature.com/article2"],
    "confidence": 0.89
  },
  "metadata": {
    "steps_taken": 7,
    "execution_time": 45.2,
    "tools_used": ["search", "summarize"],
    "reasoning": "I searched for recent papers and summarized key findings..."
  }
}
```

### POST /agents/coordinate

Execute multiple agents with coordination.

**Request:**
```json
{
  "coordination_strategy": "sequential",
  "tasks": [
    {
      "agent_name": "researcher",
      "goal": "Research AI safety",
      "dependencies": []
    },
    {
      "agent_name": "writer",
      "goal": "Write summary",
      "dependencies": ["researcher"]
    }
  ]
}
```

## Security API

### GET /security/threats

Get recent threat detections.

**Response:**
```json
{
  "threats": [
    {
      "id": "threat_456",
      "type": "prompt_injection",
      "level": "high",
      "confidence": 0.95,
      "timestamp": "2024-01-15T10:30:00Z",
      "description": "Potential prompt injection detected",
      "user_id": "user_123"
    }
  ],
  "total_threats": 5,
  "time_period": "24h"
}
```

### GET /security/audit

Get audit log entries.

**Response:**
```json
{
  "events": [
    {
      "event_id": "audit_789",
      "event_type": "authentication",
      "event_name": "user_login",
      "timestamp": "2024-01-15T10:30:00Z",
      "user_id": "user_123",
      "details": {
        "success": true,
        "ip_address": "*************"
      }
    }
  ],
  "total_events": 1000,
  "page": 1,
  "per_page": 50
}
```

## Error Responses

All APIs use consistent error response format:

```json
{
  "error": {
    "code": "AUTHENTICATION_FAILED",
    "message": "Invalid or expired token",
    "details": {
      "correlation_id": "corr_123",
      "timestamp": "2024-01-15T10:30:00Z"
    }
  }
}
```

### Common Error Codes

- `AUTHENTICATION_FAILED`: Invalid or missing authentication
- `AUTHORIZATION_DENIED`: Insufficient permissions
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `VALIDATION_ERROR`: Invalid request data
- `PLUGIN_NOT_FOUND`: Requested plugin not available
- `AGENT_EXECUTION_FAILED`: Agent task execution failed
- `WORKFLOW_ERROR`: Workflow execution error
- `INTERNAL_ERROR`: Unexpected server error
