"""Comprehensive audit logging system for M-GAIF."""

from __future__ import annotations

import asyncio
import json
import logging
import uuid
from dataclasses import dataclass, asdict
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional
from contextvars import ContextVar

# Context variable for correlation ID
correlation_id: ContextVar[str] = ContextVar('correlation_id', default='')


class AuditEventType(Enum):
    """Types of audit events."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    SECURITY = "security"
    COMPONENT = "component"
    AGENT = "agent"
    WORKFLOW = "workflow"
    EVALUATION = "evaluation"
    SYSTEM = "system"
    ERROR = "error"


class AuditLevel(Enum):
    """Audit event severity levels."""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class AuditEvent:
    """Structured audit event."""
    event_id: str
    event_type: AuditEventType
    event_name: str
    level: AuditLevel
    timestamp: datetime
    user_id: str
    session_id: Optional[str]
    correlation_id: str
    source_component: str
    details: Dict[str, Any]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        data['event_type'] = self.event_type.value
        data['level'] = self.level.value
        return data
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), default=str)


class AuditLogger:
    """Centralized audit logging system."""
    
    def __init__(self, log_file: Optional[Path] = None, 
                 console_logging: bool = True,
                 structured_logging: bool = True):
        """Initialize audit logger.
        
        Args:
            log_file: Optional file path for audit logs
            console_logging: Whether to log to console
            structured_logging: Whether to use structured JSON logging
        """
        self.log_file = log_file
        self.console_logging = console_logging
        self.structured_logging = structured_logging
        
        # Setup logging
        self.logger = logging.getLogger("mgaif.audit")
        self.logger.setLevel(logging.DEBUG)
        
        # Remove existing handlers
        self.logger.handlers.clear()
        
        # Console handler
        if console_logging:
            console_handler = logging.StreamHandler()
            if structured_logging:
                console_handler.setFormatter(
                    logging.Formatter('%(message)s')
                )
            else:
                console_handler.setFormatter(
                    logging.Formatter(
                        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    )
                )
            self.logger.addHandler(console_handler)
        
        # File handler
        if log_file:
            log_file.parent.mkdir(parents=True, exist_ok=True)
            file_handler = logging.FileHandler(log_file)
            if structured_logging:
                file_handler.setFormatter(
                    logging.Formatter('%(message)s')
                )
            else:
                file_handler.setFormatter(
                    logging.Formatter(
                        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                    )
                )
            self.logger.addHandler(file_handler)
        
        # Event buffer for in-memory storage
        self.event_buffer: List[AuditEvent] = []
        self.max_buffer_size = 10000
        self._buffer_lock = asyncio.Lock()
    
    def _get_correlation_id(self) -> str:
        """Get or generate correlation ID."""
        try:
            return correlation_id.get()
        except LookupError:
            return str(uuid.uuid4())
    
    async def log_event(self, event_type: AuditEventType, event_name: str,
                       level: AuditLevel, user_id: str, details: Dict[str, Any],
                       session_id: Optional[str] = None,
                       source_component: str = "unknown",
                       metadata: Dict[str, Any] = None) -> None:
        """Log an audit event.
        
        Args:
            event_type: Type of event
            event_name: Specific event name
            level: Event severity level
            user_id: User associated with event
            details: Event-specific details
            session_id: Optional session ID
            source_component: Component that generated the event
            metadata: Additional metadata
        """
        event = AuditEvent(
            event_id=str(uuid.uuid4()),
            event_type=event_type,
            event_name=event_name,
            level=level,
            timestamp=datetime.utcnow(),
            user_id=user_id,
            session_id=session_id,
            correlation_id=self._get_correlation_id(),
            source_component=source_component,
            details=details or {},
            metadata=metadata or {}
        )
        
        # Log to configured outputs
        if self.structured_logging:
            log_message = event.to_json()
        else:
            log_message = f"{event.event_type.value}:{event.event_name} - {event.user_id} - {event.details}"
        
        # Map audit level to logging level
        log_level_map = {
            AuditLevel.DEBUG: logging.DEBUG,
            AuditLevel.INFO: logging.INFO,
            AuditLevel.WARNING: logging.WARNING,
            AuditLevel.ERROR: logging.ERROR,
            AuditLevel.CRITICAL: logging.CRITICAL
        }
        
        self.logger.log(log_level_map[level], log_message)
        
        # Store in buffer
        async with self._buffer_lock:
            self.event_buffer.append(event)
            if len(self.event_buffer) > self.max_buffer_size:
                self.event_buffer = self.event_buffer[-self.max_buffer_size:]
    
    async def log_auth_event(self, event_name: str, user_id: str,
                            details: Dict[str, Any], session_id: str = None) -> None:
        """Log authentication/authorization event."""
        level = AuditLevel.INFO
        if "failed" in event_name or "error" in event_name:
            level = AuditLevel.WARNING
        
        await self.log_event(
            event_type=AuditEventType.AUTHENTICATION,
            event_name=event_name,
            level=level,
            user_id=user_id,
            details=details,
            session_id=session_id,
            source_component="auth_system"
        )
    
    async def log_security_event(self, event_name: str, user_id: str,
                                details: Dict[str, Any]) -> None:
        """Log security-related event."""
        level = AuditLevel.WARNING
        if "critical" in event_name or "attack" in event_name:
            level = AuditLevel.CRITICAL
        elif "threat" in event_name or "violation" in event_name:
            level = AuditLevel.ERROR
        
        await self.log_event(
            event_type=AuditEventType.SECURITY,
            event_name=event_name,
            level=level,
            user_id=user_id,
            details=details,
            source_component="security_system"
        )
    
    async def log_component_event(self, event_name: str, user_id: str,
                                 component_name: str, details: Dict[str, Any]) -> None:
        """Log component-related event."""
        level = AuditLevel.INFO
        if "error" in event_name or "failed" in event_name:
            level = AuditLevel.ERROR
        elif "warning" in event_name:
            level = AuditLevel.WARNING
        
        await self.log_event(
            event_type=AuditEventType.COMPONENT,
            event_name=event_name,
            level=level,
            user_id=user_id,
            details=details,
            source_component=component_name
        )
    
    async def log_agent_event(self, event_name: str, user_id: str,
                             agent_name: str, details: Dict[str, Any]) -> None:
        """Log agent-related event."""
        level = AuditLevel.INFO
        if "error" in event_name or "failed" in event_name:
            level = AuditLevel.ERROR
        
        await self.log_event(
            event_type=AuditEventType.AGENT,
            event_name=event_name,
            level=level,
            user_id=user_id,
            details=details,
            source_component=f"agent:{agent_name}"
        )
    
    async def log_workflow_event(self, event_name: str, user_id: str,
                                workflow_id: str, details: Dict[str, Any]) -> None:
        """Log workflow-related event."""
        level = AuditLevel.INFO
        if "error" in event_name or "failed" in event_name:
            level = AuditLevel.ERROR
        
        await self.log_event(
            event_type=AuditEventType.WORKFLOW,
            event_name=event_name,
            level=level,
            user_id=user_id,
            details=details,
            source_component=f"workflow:{workflow_id}"
        )
    
    async def log_evaluation_event(self, event_name: str, user_id: str,
                                  details: Dict[str, Any]) -> None:
        """Log evaluation-related event."""
        await self.log_event(
            event_type=AuditEventType.EVALUATION,
            event_name=event_name,
            level=AuditLevel.INFO,
            user_id=user_id,
            details=details,
            source_component="evaluation_system"
        )
    
    async def log_system_event(self, event_name: str, details: Dict[str, Any],
                              level: AuditLevel = AuditLevel.INFO) -> None:
        """Log system-level event."""
        await self.log_event(
            event_type=AuditEventType.SYSTEM,
            event_name=event_name,
            level=level,
            user_id="system",
            details=details,
            source_component="system"
        )
    
    async def log_error_event(self, error: Exception, user_id: str,
                             context: Dict[str, Any] = None) -> None:
        """Log error event with exception details."""
        details = {
            "error_type": type(error).__name__,
            "error_message": str(error),
            "context": context or {}
        }
        
        await self.log_event(
            event_type=AuditEventType.ERROR,
            event_name="exception_occurred",
            level=AuditLevel.ERROR,
            user_id=user_id,
            details=details,
            source_component="error_handler"
        )
    
    async def get_events(self, event_type: Optional[AuditEventType] = None,
                        user_id: Optional[str] = None,
                        start_time: Optional[datetime] = None,
                        end_time: Optional[datetime] = None,
                        limit: int = 1000) -> List[AuditEvent]:
        """Retrieve audit events with filtering.
        
        Args:
            event_type: Filter by event type
            user_id: Filter by user ID
            start_time: Filter events after this time
            end_time: Filter events before this time
            limit: Maximum number of events to return
            
        Returns:
            List of matching audit events
        """
        async with self._buffer_lock:
            events = self.event_buffer.copy()
        
        # Apply filters
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        if user_id:
            events = [e for e in events if e.user_id == user_id]
        
        if start_time:
            events = [e for e in events if e.timestamp >= start_time]
        
        if end_time:
            events = [e for e in events if e.timestamp <= end_time]
        
        # Sort by timestamp (newest first) and limit
        events.sort(key=lambda e: e.timestamp, reverse=True)
        return events[:limit]
    
    async def get_event_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get summary of audit events for specified time period."""
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours)
        
        events = await self.get_events(start_time=start_time, end_time=end_time)
        
        # Count by event type
        type_counts = {}
        level_counts = {}
        user_counts = {}
        component_counts = {}
        
        for event in events:
            # Event type counts
            event_type = event.event_type.value
            type_counts[event_type] = type_counts.get(event_type, 0) + 1
            
            # Level counts
            level = event.level.value
            level_counts[level] = level_counts.get(level, 0) + 1
            
            # User counts
            user_counts[event.user_id] = user_counts.get(event.user_id, 0) + 1
            
            # Component counts
            component = event.source_component
            component_counts[component] = component_counts.get(component, 0) + 1
        
        return {
            "time_period_hours": hours,
            "total_events": len(events),
            "event_type_counts": type_counts,
            "level_counts": level_counts,
            "top_users": dict(sorted(user_counts.items(), key=lambda x: x[1], reverse=True)[:10]),
            "top_components": dict(sorted(component_counts.items(), key=lambda x: x[1], reverse=True)[:10])
        }


# Context manager for correlation ID
class CorrelationContext:
    """Context manager for setting correlation ID."""
    
    def __init__(self, correlation_id_value: str = None):
        """Initialize with correlation ID."""
        self.correlation_id_value = correlation_id_value or str(uuid.uuid4())
        self.token = None
    
    def __enter__(self):
        """Enter context and set correlation ID."""
        self.token = correlation_id.set(self.correlation_id_value)
        return self.correlation_id_value
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit context and reset correlation ID."""
        if self.token:
            correlation_id.reset(self.token)
