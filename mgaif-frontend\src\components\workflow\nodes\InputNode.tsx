import React from 'react';
import { NodeProps } from 'reactflow';
import { BaseNode } from './BaseNode';
import type { InputNodeData } from '../../../types/workflow';

export const InputNode: React.FC<NodeProps> = (props) => {
  const data = props.data as InputNodeData;

  return (
    <BaseNode
      {...props}
      data={data}
      icon="📝"
      color="#52c41a"
      showHandles={{ source: true, target: false }}
    />
  );
};
