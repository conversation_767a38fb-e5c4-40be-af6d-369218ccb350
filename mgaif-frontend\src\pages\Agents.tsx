import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Slider,
  Row,
  Col,
  Divider,
  message,
  Tag,
  Tooltip,
  Tabs,
  Steps,
  List,
  Avatar,
  Badge,
  Progress,
} from 'antd';
import {
  PlusOutlined,
  RobotOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  BrainOutlined,
  ToolOutlined,
  DatabaseOutlined,
  MessageOutlined,
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { api } from '../services/api';
import type { AgentConfig, AgentCapability, AgentTool, AgentMemoryConfig, AgentPersonality } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Step } = Steps;

export const Agents: React.FC = () => {
  const [agents, setAgents] = useState<AgentConfig[]>([]);
  const [currentAgent, setCurrentAgent] = useState<AgentConfig | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [configureModalVisible, setConfigureModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [activeTab, setActiveTab] = useState('agents');

  const [form] = Form.useForm();
  const [configForm] = Form.useForm();
  const [testForm] = Form.useForm();

  // Load data on component mount
  useEffect(() => {
    loadAgents();
  }, []);

  const loadAgents = async () => {
    try {
      setLoading(true);
      // Mock data for development
      setAgents([
        {
          id: '1',
          name: 'Customer Support Agent',
          description: 'Handles customer inquiries and provides support across multiple channels',
          purpose: 'Provide excellent customer service and resolve issues efficiently',
          capabilities: [
            { type: 'reasoning', enabled: true, configuration: { depth: 'advanced' } },
            { type: 'planning', enabled: true, configuration: { strategy: 'reactive' } },
            { type: 'memory', enabled: true, configuration: { retention: 'session' } },
            { type: 'tool-use', enabled: true, configuration: { safety: 'high' } },
            { type: 'communication', enabled: true, configuration: { style: 'professional' } }
          ],
          tools: [
            {
              id: 'knowledge-search',
              name: 'Knowledge Base Search',
              type: 'built-in',
              description: 'Search company knowledge base for answers',
              parameters: { index: 'support-kb' },
              enabled: true
            },
            {
              id: 'ticket-system',
              name: 'Ticket Management',
              type: 'mcp-integration',
              description: 'Create and update support tickets',
              parameters: { system: 'zendesk' },
              enabled: true
            }
          ],
          planningStrategy: 'react',
          memoryConfig: {
            episodic: { enabled: true, maxEntries: 100, decayRate: 0.1 },
            semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 1000 },
            persistent: { enabled: true, storageType: 'database' }
          },
          personality: {
            tone: 'professional',
            verbosity: 'detailed',
            creativity: 0.3,
            riskTolerance: 0.2,
            customInstructions: 'Always be helpful and empathetic. Escalate complex issues to human agents.'
          },
          constraints: [
            { type: 'time', description: 'Response time under 30 seconds', value: 30, enforced: true },
            { type: 'ethical', description: 'No personal data sharing', value: 'strict', enforced: true }
          ],
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-16T14:20:00Z'
        },
        {
          id: '2',
          name: 'Research Assistant',
          description: 'Conducts research and analysis across multiple data sources',
          purpose: 'Gather, analyze, and synthesize information from various sources',
          capabilities: [
            { type: 'reasoning', enabled: true, configuration: { depth: 'expert' } },
            { type: 'planning', enabled: true, configuration: { strategy: 'plan-and-execute' } },
            { type: 'memory', enabled: true, configuration: { retention: 'persistent' } },
            { type: 'tool-use', enabled: true, configuration: { safety: 'medium' } },
            { type: 'communication', enabled: true, configuration: { style: 'academic' } }
          ],
          tools: [
            {
              id: 'web-search',
              name: 'Web Search',
              type: 'built-in',
              description: 'Search the web for information',
              parameters: { engine: 'google' },
              enabled: true
            },
            {
              id: 'document-analysis',
              name: 'Document Analyzer',
              type: 'custom',
              description: 'Analyze and extract insights from documents',
              parameters: { formats: ['pdf', 'docx', 'txt'] },
              enabled: true
            }
          ],
          planningStrategy: 'plan-and-execute',
          memoryConfig: {
            episodic: { enabled: true, maxEntries: 500, decayRate: 0.05 },
            semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 5000 },
            persistent: { enabled: true, storageType: 'vector-store' }
          },
          personality: {
            tone: 'formal',
            verbosity: 'verbose',
            creativity: 0.7,
            riskTolerance: 0.5,
            customInstructions: 'Be thorough and cite sources. Provide balanced perspectives on controversial topics.'
          },
          constraints: [
            { type: 'resource', description: 'Max 10 web searches per query', value: 10, enforced: true },
            { type: 'domain', description: 'Academic and professional sources only', value: 'restricted', enforced: true }
          ],
          status: 'testing',
          createdAt: '2024-01-14T15:45:00Z',
          updatedAt: '2024-01-16T09:20:00Z'
        }
      ]);
    } catch (error) {
      console.error('Failed to load agents:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAgent = async () => {
    try {
      const values = await form.validateFields();

      const newAgent: Partial<AgentConfig> = {
        name: values.name,
        description: values.description,
        purpose: values.purpose,
        capabilities: [
          { type: 'reasoning', enabled: true },
          { type: 'planning', enabled: true },
          { type: 'memory', enabled: true },
          { type: 'tool-use', enabled: true },
          { type: 'communication', enabled: true }
        ],
        tools: [],
        planningStrategy: values.planningStrategy || 'react',
        memoryConfig: {
          episodic: { enabled: true, maxEntries: 100, decayRate: 0.1 },
          semantic: { enabled: true, embeddingModel: 'text-embedding-ada-002', maxFacts: 1000 },
          persistent: { enabled: false, storageType: 'local' }
        },
        personality: {
          tone: 'professional',
          verbosity: 'detailed',
          creativity: 0.5,
          riskTolerance: 0.3
        },
        constraints: [],
        status: 'draft'
      };

      // In real implementation, call API
      // await api.agents.create(newAgent);

      message.success('Agent created successfully!');
      setCreateModalVisible(false);
      form.resetFields();
      loadAgents();
    } catch (error) {
      message.error('Failed to create agent');
    }
  };

  const handleToggleStatus = async (agent: AgentConfig) => {
    try {
      const newStatus = agent.status === 'active' ? 'inactive' : 'active';

      // In real implementation, call API
      // await api.agents.updateStatus(agent.id, newStatus);

      message.success(`Agent ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
      loadAgents();
    } catch (error) {
      message.error('Failed to update agent status');
    }
  };

  const agentColumns: TableColumnsType<AgentConfig> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <Avatar icon={<RobotOutlined />} />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Strategy',
      dataIndex: 'planningStrategy',
      key: 'planningStrategy',
      render: (strategy) => {
        const colors = {
          'chain-of-thought': 'blue',
          'react': 'green',
          'plan-and-execute': 'orange'
        };
        return <Tag color={colors[strategy as keyof typeof colors]}>{strategy.toUpperCase()}</Tag>;
      },
    },
    {
      title: 'Tools',
      key: 'tools',
      render: (_, record) => (
        <div>
          <Text>{record.tools.length} tools</Text>
          <br />
          <Text type="secondary" style={{ fontSize: '11px' }}>
            {record.tools.filter(t => t.enabled).length} active
          </Text>
        </div>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const colors = {
          draft: 'default',
          testing: 'processing',
          active: 'success',
          inactive: 'error'
        };

        return (
          <Badge
            status={status === 'active' ? 'success' : status === 'testing' ? 'processing' : 'default'}
            text={status.toUpperCase()}
          />
        );
      },
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Configure">
            <Button
              icon={<SettingOutlined />}
              size="small"
              onClick={() => {
                setCurrentAgent(record);
                setConfigureModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Test">
            <Button
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => {
                setCurrentAgent(record);
                setTestModalVisible(true);
              }}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? 'Deactivate' : 'Activate'}>
            <Button
              icon={record.status === 'active' ? <StopOutlined /> : <PlayCircleOutlined />}
              size="small"
              type={record.status === 'active' ? 'default' : 'primary'}
              onClick={() => handleToggleStatus(record)}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>AI Agents</Title>
        <Paragraph type="secondary">
          Design and deploy intelligent AI agents with custom capabilities, tool access, and planning strategies
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                {agents.length}
              </Title>
              <Text type="secondary">Total Agents</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                {agents.filter(a => a.status === 'active').length}
              </Title>
              <Text type="secondary">Active</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#faad14' }}>
                {agents.filter(a => a.status === 'testing').length}
              </Title>
              <Text type="secondary">Testing</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#f5222d' }}>
                {agents.reduce((sum, agent) => sum + agent.tools.length, 0)}
              </Title>
              <Text type="secondary">Total Tools</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4}>Your AI Agents</Title>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            Create Agent
          </Button>
        </div>

        <Table
          columns={agentColumns}
          dataSource={agents}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* Create Agent Modal */}
      <Modal
        title="Create New AI Agent"
        open={createModalVisible}
        onOk={handleCreateAgent}
        onCancel={() => setCreateModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Agent Name"
            name="name"
            rules={[{ required: true, message: 'Please enter agent name' }]}
          >
            <Input placeholder="Enter agent name" />
          </Form.Item>

          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe what this agent does" />
          </Form.Item>

          <Form.Item
            label="Purpose"
            name="purpose"
            rules={[{ required: true, message: 'Please enter agent purpose' }]}
          >
            <TextArea rows={2} placeholder="Define the agent's primary purpose and goals" />
          </Form.Item>

          <Form.Item
            label="Planning Strategy"
            name="planningStrategy"
            rules={[{ required: true, message: 'Please select planning strategy' }]}
          >
            <Select placeholder="Select planning strategy">
              <Select.Option value="chain-of-thought">Chain of Thought</Select.Option>
              <Select.Option value="react">ReAct (Reasoning + Acting)</Select.Option>
              <Select.Option value="plan-and-execute">Plan and Execute</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Test Agent Modal */}
      <Modal
        title={`Test ${currentAgent?.name}`}
        open={testModalVisible}
        onCancel={() => setTestModalVisible(false)}
        footer={null}
        width={600}
      >
        <div>
          <Title level={5}>Agent Testing</Title>
          <Paragraph type="secondary">
            Send a test message to your agent to see how it responds
          </Paragraph>

          <Form form={testForm} layout="vertical">
            <Form.Item label="Test Message">
              <TextArea
                rows={4}
                placeholder="Enter a test message for the agent..."
              />
            </Form.Item>

            <Form.Item>
              <Button type="primary" block>
                Send Test Message
              </Button>
            </Form.Item>
          </Form>

          <Divider>Response</Divider>

          <Card style={{ backgroundColor: '#f5f5f5' }}>
            <Text type="secondary">Agent response will appear here...</Text>
          </Card>
        </div>
      </Modal>
    </div>
  );
};
