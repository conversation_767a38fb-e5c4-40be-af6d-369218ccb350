"""Tests for RAG base components.

This module tests the foundational RAG components including Document,
Chunk, RAGResult, and related data structures.
"""

import pytest
from datetime import datetime, timedelta
from uuid import UUID

from core.rag.base import (
    Document, DocumentType, Chunk, RetrievalResult, RAGResult, RAGError,
    <PERSON>kingError, RetrievalError
)


class TestDocument:
    """Test Document data structure."""
    
    def test_document_creation(self):
        """Test basic document creation."""
        doc = Document(
            content="This is a test document about machine learning.",
            title="ML Test Document",
            doc_type=DocumentType.TEXT,
            metadata={"author": "Test Author", "category": "education"}
        )
        
        assert doc.content == "This is a test document about machine learning."
        assert doc.title == "ML Test Document"
        assert doc.doc_type == DocumentType.TEXT
        assert doc.metadata["author"] == "Test Author"
        assert doc.metadata["category"] == "education"
        assert doc.source is None
        assert isinstance(UUID(doc.id), UUID)  # Valid UUID
        assert isinstance(doc.created_at, datetime)
        assert doc.processed_at is None
    
    def test_document_with_source(self):
        """Test document creation with source path."""
        doc = Document(
            content="Content from file",
            source="/path/to/document.txt"
        )
        
        assert doc.title == "document.txt"  # Extracted from source
        assert doc.source == "/path/to/document.txt"
    
    def test_document_defaults(self):
        """Test document creation with minimal parameters."""
        doc = Document(content="Minimal content")
        
        assert doc.title == ""
        assert doc.doc_type == DocumentType.TEXT
        assert doc.metadata == {}
        assert doc.source is None
    
    def test_document_to_dict(self):
        """Test document serialization to dictionary."""
        doc = Document(
            content="Test content",
            title="Test Title",
            metadata={"key": "value"}
        )
        
        doc_dict = doc.to_dict()
        
        assert doc_dict["content"] == "Test content"
        assert doc_dict["title"] == "Test Title"
        assert doc_dict["doc_type"] == "text"
        assert doc_dict["metadata"] == {"key": "value"}
        assert "id" in doc_dict
        assert "created_at" in doc_dict
    
    def test_document_from_dict(self):
        """Test document deserialization from dictionary."""
        doc_dict = {
            "content": "Test content",
            "title": "Test Title",
            "doc_type": "text",
            "metadata": {"key": "value"},
            "id": "test-id-123",
            "created_at": "2023-01-01T00:00:00"
        }
        
        doc = Document.from_dict(doc_dict)
        
        assert doc.content == "Test content"
        assert doc.title == "Test Title"
        assert doc.doc_type == DocumentType.TEXT
        assert doc.metadata == {"key": "value"}
        assert doc.id == "test-id-123"
    
    def test_document_word_count(self):
        """Test document word count calculation."""
        doc = Document(content="This is a test document with ten words total.")
        
        assert doc.word_count() == 10
    
    def test_document_char_count(self):
        """Test document character count calculation."""
        doc = Document(content="Hello World!")
        
        assert doc.char_count() == 12
    
    def test_document_empty_content(self):
        """Test document with empty content."""
        doc = Document(content="")
        
        assert doc.word_count() == 0
        assert doc.char_count() == 0


class TestChunk:
    """Test Chunk data structure."""
    
    def test_chunk_creation(self):
        """Test basic chunk creation."""
        chunk = Chunk(
            content="This is a chunk of text.",
            start_char=0,
            end_char=24,
            metadata={"section": "introduction"}
        )
        
        assert chunk.content == "This is a chunk of text."
        assert chunk.start_char == 0
        assert chunk.end_char == 24
        assert chunk.metadata["section"] == "introduction"
        assert isinstance(UUID(chunk.id), UUID)
        assert isinstance(chunk.created_at, datetime)
    
    def test_chunk_from_document(self):
        """Test chunk creation from document."""
        doc = Document(
            content="This is the full document content.",
            title="Test Document"
        )
        
        chunk = Chunk.from_document(
            document=doc,
            content="This is the full",
            start_char=0,
            end_char=16
        )
        
        assert chunk.content == "This is the full"
        assert chunk.document_id == doc.id
        assert chunk.document_title == "Test Document"
        assert chunk.start_char == 0
        assert chunk.end_char == 16
    
    def test_chunk_to_dict(self):
        """Test chunk serialization to dictionary."""
        chunk = Chunk(
            content="Test chunk content",
            start_char=10,
            end_char=28,
            metadata={"type": "paragraph"}
        )
        
        chunk_dict = chunk.to_dict()
        
        assert chunk_dict["content"] == "Test chunk content"
        assert chunk_dict["start_char"] == 10
        assert chunk_dict["end_char"] == 28
        assert chunk_dict["metadata"] == {"type": "paragraph"}
        assert "id" in chunk_dict
        assert "created_at" in chunk_dict
    
    def test_chunk_word_count(self):
        """Test chunk word count calculation."""
        chunk = Chunk(content="This chunk has five words", start_char=0, end_char=25)
        
        assert chunk.word_count() == 5
    
    def test_chunk_char_count(self):
        """Test chunk character count calculation."""
        chunk = Chunk(content="Hello!", start_char=0, end_char=6)
        
        assert chunk.char_count() == 6


class TestRetrievalResult:
    """Test RetrievalResult data structure."""
    
    def test_retrieval_result_creation(self):
        """Test basic retrieval result creation."""
        chunk = Chunk(content="Retrieved chunk", start_char=0, end_char=15)
        
        result = RetrievalResult(
            chunk=chunk,
            score=0.85,
            rank=1,
            metadata={"retrieval_method": "vector"}
        )
        
        assert result.chunk == chunk
        assert result.score == 0.85
        assert result.rank == 1
        assert result.metadata["retrieval_method"] == "vector"
    
    def test_retrieval_result_to_dict(self):
        """Test retrieval result serialization."""
        chunk = Chunk(content="Test chunk", start_char=0, end_char=10)
        result = RetrievalResult(chunk=chunk, score=0.9, rank=2)
        
        result_dict = result.to_dict()
        
        assert result_dict["score"] == 0.9
        assert result_dict["rank"] == 2
        assert "chunk" in result_dict
        assert result_dict["chunk"]["content"] == "Test chunk"


class TestRAGResult:
    """Test RAGResult data structure."""
    
    def test_rag_result_creation(self):
        """Test basic RAG result creation."""
        chunk1 = Chunk(content="First chunk", start_char=0, end_char=11)
        chunk2 = Chunk(content="Second chunk", start_char=12, end_char=24)
        
        source_chunks = [
            RetrievalResult(chunk=chunk1, score=0.9, rank=1),
            RetrievalResult(chunk=chunk2, score=0.8, rank=2)
        ]
        
        result = RAGResult(
            query="What is the content?",
            response="The content includes first and second chunks.",
            source_chunks=source_chunks,
            grounding_score=0.85,
            processing_time=1.5,
            metadata={"model": "test-model"}
        )
        
        assert result.query == "What is the content?"
        assert result.response == "The content includes first and second chunks."
        assert len(result.source_chunks) == 2
        assert result.grounding_score == 0.85
        assert result.processing_time == 1.5
        assert result.metadata["model"] == "test-model"
        assert isinstance(result.timestamp, datetime)
    
    def test_rag_result_to_dict(self):
        """Test RAG result serialization."""
        chunk = Chunk(content="Test chunk", start_char=0, end_char=10)
        source_chunks = [RetrievalResult(chunk=chunk, score=0.9, rank=1)]
        
        result = RAGResult(
            query="Test query",
            response="Test response",
            source_chunks=source_chunks,
            grounding_score=0.8
        )
        
        result_dict = result.to_dict()
        
        assert result_dict["query"] == "Test query"
        assert result_dict["response"] == "Test response"
        assert result_dict["grounding_score"] == 0.8
        assert len(result_dict["source_chunks"]) == 1
        assert "timestamp" in result_dict


class TestRAGExceptions:
    """Test RAG exception classes."""
    
    def test_rag_error(self):
        """Test basic RAG error."""
        error = RAGError(
            message="Test error",
            component="test_component",
            details={"error_code": 123}
        )
        
        assert str(error) == "Test error"
        assert error.component == "test_component"
        assert error.details["error_code"] == 123
    
    def test_chunking_error(self):
        """Test chunking-specific error."""
        error = ChunkingError(
            message="Chunking failed",
            component="semantic_chunker",
            details={"chunk_size": 512}
        )
        
        assert str(error) == "Chunking failed"
        assert error.component == "semantic_chunker"
        assert error.details["chunk_size"] == 512
        assert isinstance(error, RAGError)
    
    def test_retrieval_error(self):
        """Test retrieval-specific error."""
        error = RetrievalError(
            message="Retrieval failed",
            component="vector_retriever",
            details={"query": "test query"}
        )
        
        assert str(error) == "Retrieval failed"
        assert error.component == "vector_retriever"
        assert error.details["query"] == "test query"
        assert isinstance(error, RAGError)


@pytest.mark.asyncio
async def test_document_processing_workflow():
    """Test complete document processing workflow."""
    # Create document
    doc = Document(
        content="This is a comprehensive test document. It contains multiple sentences. Each sentence provides different information.",
        title="Test Document",
        metadata={"category": "test", "priority": "high"}
    )
    
    # Verify document properties
    assert doc.word_count() > 0
    assert doc.char_count() > 0
    assert doc.doc_type == DocumentType.TEXT
    
    # Create chunks from document
    chunk1 = Chunk.from_document(
        document=doc,
        content="This is a comprehensive test document.",
        start_char=0,
        end_char=37
    )
    
    chunk2 = Chunk.from_document(
        document=doc,
        content="It contains multiple sentences.",
        start_char=38,
        end_char=69
    )
    
    # Verify chunks
    assert chunk1.document_id == doc.id
    assert chunk2.document_id == doc.id
    assert chunk1.document_title == "Test Document"
    assert chunk2.document_title == "Test Document"
    
    # Create retrieval results
    retrieval_results = [
        RetrievalResult(chunk=chunk1, score=0.9, rank=1),
        RetrievalResult(chunk=chunk2, score=0.8, rank=2)
    ]
    
    # Create RAG result
    rag_result = RAGResult(
        query="What is this document about?",
        response="This document is a comprehensive test containing multiple sentences with different information.",
        source_chunks=retrieval_results,
        grounding_score=0.85,
        processing_time=0.5
    )
    
    # Verify RAG result
    assert len(rag_result.source_chunks) == 2
    assert rag_result.grounding_score > 0.8
    assert rag_result.processing_time > 0
    
    # Test serialization
    rag_dict = rag_result.to_dict()
    assert "query" in rag_dict
    assert "response" in rag_dict
    assert "source_chunks" in rag_dict
    assert len(rag_dict["source_chunks"]) == 2
