import React from 'react';
import { NodeProps } from 'reactflow';
import { BaseNode } from './BaseNode';
import type { OutputNodeData } from '../../../types/workflow';

export const OutputNode: React.FC<NodeProps> = (props) => {
  const data = props.data as OutputNodeData;

  return (
    <BaseNode
      {...props}
      data={data}
      icon="📤"
      color="#fa8c16"
      showHandles={{ source: false, target: true }}
    />
  );
};
