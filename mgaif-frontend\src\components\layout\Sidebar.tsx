import React from 'react';
import { Layout, Menu, Typography } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  DashboardOutlined,
  ApiOutlined,
  RobotOutlined,
  MessageOutlined,
  AppstoreOutlined,
  SettingOutlined,
  DatabaseOutlined,
  BrainOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Sider } = Layout;

const menuItems: MenuProps['items'] = [
  {
    key: '/',
    icon: <DashboardOutlined />,
    label: 'Dashboard',
  },
  {
    key: '/workflows',
    icon: <ApiOutlined />,
    label: 'Workflows',
  },
  {
    key: '/chatbots',
    icon: <MessageOutlined />,
    label: 'Chatbots',
  },
  {
    key: '/agents',
    icon: <RobotOutlined />,
    label: 'Agents',
  },
  {
    key: '/models',
    icon: <BrainOutlined />,
    label: 'Models',
  },
  {
    key: '/api-builder',
    icon: <ApiOutlined />,
    label: 'API Builder',
  },
  {
    key: '/mcp-integrations',
    icon: <SettingOutlined />,
    label: 'MCP Integrations',
  },
  {
    key: '/rag-stores',
    icon: <DatabaseOutlined />,
    label: 'RAG Stores',
  },
  {
    key: '/templates',
    icon: <AppstoreOutlined />,
    label: 'Templates',
  },
  {
    type: 'divider',
  },
  {
    key: '/settings',
    icon: <SettingOutlined />,
    label: 'Settings',
  },
];

export const Sidebar: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleMenuClick: MenuProps['onClick'] = ({ key }) => {
    navigate(key);
  };

  return (
    <Sider
      width={250}
      style={{
        background: '#fff',
        borderRight: '1px solid #f0f0f0',
      }}
    >
      <div
        style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          textAlign: 'center',
        }}
      >
        <Typography.Title level={4} style={{ margin: 0, color: '#1890ff' }}>
          M-GAIF
        </Typography.Title>
        <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
          AI Platform
        </Typography.Text>
      </div>
      
      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        style={{ borderRight: 0, paddingTop: '8px' }}
      />
    </Sider>
  );
};
