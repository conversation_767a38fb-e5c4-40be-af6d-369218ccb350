import json

import pytest
from httpx import AsyncClient, ASGITransport

from core.mcp.api import app
from tests.realistic_data import get_api_payload, get_document_corpus, get_workflow_spec


@pytest.mark.asyncio
async def test_mcp_tokenize(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        payload = get_api_payload("tokenize", 0)
        # benchmark a few calls
        metrics = await async_bench(lambda: ac.post("/mcp/tools/tokenize", json=payload), iterations=20)
        assert metrics["p95_ms"] < 50
        resp = await ac.post("/mcp/tools/tokenize", json=payload)
        assert resp.status_code == 200
        data = resp.json()
        assert isinstance(data.get("tokens"), list)
        assert data.get("token_count") == len(data["tokens"]) >= 5  # Realistic text should have more tokens


@pytest.mark.asyncio
async def test_mcp_embed(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        payload = get_api_payload("embed", 0)
        resp = await ac.post("/mcp/tools/embed", json=payload)
        assert resp.status_code == 200
        vecs = resp.json()["vectors"]
        assert len(vecs) == len(payload["texts"])
        assert all(isinstance(x, list) and len(x) > 0 for x in vecs)
        metrics = await async_bench(lambda: ac.post("/mcp/tools/embed", json=payload), iterations=10)
        assert metrics["p95_ms"] < 100


@pytest.mark.asyncio
async def test_mcp_retriever_index_search(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        # Use realistic document corpus
        corpus = get_document_corpus(3)
        items = [{"id": doc["id"], "text": doc["content"]} for doc in corpus]

        r = await ac.post("/mcp/tools/retriever/index", json={"items": items})
        assert r.status_code == 200

        # Search for AI-related content
        search_payload = get_api_payload("retrieval", 0)
        s = await ac.post("/mcp/tools/retriever/search", json=search_payload)
        assert s.status_code == 200
        hits = s.json()["hits"]
        assert len(hits) <= search_payload["top_k"]
        assert len(hits) > 0  # Should find relevant documents

        metrics = await async_bench(lambda: ac.post("/mcp/tools/retriever/search", json=search_payload), iterations=20)
        assert metrics["p95_ms"] < 80


@pytest.mark.asyncio
async def test_mcp_llm_chat(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        payload = get_api_payload("chat", 0)
        r = await ac.post("/mcp/tools/llm/chat", json=payload)
        assert r.status_code == 200
        data = r.json()
        # Echo adapter returns the user's message content
        user_content = payload["messages"][0]["content"]
        assert data["choices"][0]["content"] == user_content
        m = await async_bench(lambda: ac.post("/mcp/tools/llm/chat", json=payload), iterations=30)
        assert m["p95_ms"] < 80


@pytest.mark.asyncio
async def test_mcp_workflow_run(async_bench):
    transport = ASGITransport(app=app)
    async with AsyncClient(transport=transport, base_url="http://test") as ac:
        spec = get_workflow_spec("document_analysis_pipeline")
        payload = {"spec": spec, "state": {}}
        r = await ac.post("/mcp/tools/workflow/run", json=payload)
        assert r.status_code == 200
        st = r.json()["state"]
        # Check that workflow executed and produced analysis output
        assert "analysis" in st
        assert "tokens" in st
        bm = await async_bench(lambda: ac.post("/mcp/tools/workflow/run", json=payload), iterations=10)
        assert bm["p95_ms"] < 120
