"""Asynchronous workflow execution engine.

This module provides the core workflow execution engine for M-GAIF. The engine
processes workflow specifications as directed graphs, executing nodes in sequence
while respecting conditional edges and maintaining execution state.

Features:
- Asynchronous node execution
- Conditional edge traversal
- State management and passing
- Built-in handler library
- Cycle detection and prevention
- Error handling and recovery

Built-in Handlers:
- noop: No-operation placeholder
- set: Set state variables
- echo: Echo input to output
- tokenize: Simple word tokenization
- retrieve: Document retrieval operations
- call_llm: Language model interactions

Example:
    >>> engine = WorkflowEngine(workflow_spec)
    >>> result_state = await engine.run(initial_state)
    >>> print(result_state)  # Final workflow state

Note:
    The engine maintains execution state throughout the workflow run,
    allowing nodes to communicate through shared state variables.
"""

from __future__ import annotations

from typing import Any, Awaitable, Callable, Dict, Optional

from .schema import WorkflowSpec, Node, Edge
from ..adapters.openai_adapter import EchoAdapter
from ..contracts.llm import ChatCompletionRequest, ChatMessage


Handler = Callable[[Node, Dict[str, Any]], Awaitable[None]]


class WorkflowEngine:
    """Minimal async workflow engine with linear/decision flows.

    - Supports built-ins: noop, set, echo
    - Edges may specify a condition that refers to a state key to check truthiness
    """

    def __init__(self, spec: WorkflowSpec) -> None:
        self.spec = spec
        self._echo = EchoAdapter()

    async def run(self, state: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        state = state or {}
        current_id = self.spec.entry
        visited = set()
        while current_id:
            if current_id in visited:
                raise RuntimeError(f"Cycle detected at node: {current_id}")
            visited.add(current_id)
            node = self.spec.node_map[current_id]
            await self._execute_node(node, state)
            current_id = self._next_node_id(current_id, state)
        return state

    def _next_node_id(self, node_id: str, state: Dict[str, Any]) -> Optional[str]:
        edges = self.spec.outgoing.get(node_id, [])
        if not edges:
            return None
        # evaluate conditions in order; first truthy wins; fallback to first edge
        for e in edges:
            if e.condition:
                if state.get(e.condition):
                    return e.to
            else:
                # if no condition and it's first, it's the default unless a later conditional matches
                default = e.to
                # keep scanning; if nothing matches, we'll use default
                # store in local variable
                break
        else:
            # no edges? impossible due to earlier check
            return None
        # re-scan to check any condition that might be after the first non-conditional
        for e in edges:
            if e.condition and state.get(e.condition):
                return e.to
        return default

    async def _execute_node(self, node: Node, state: Dict[str, Any]) -> None:
        handler_name = node.handler or node.type or "noop"
        if handler_name == "noop":
            return
        if handler_name == "set":
            # set key=value pairs from params into state
            for k, v in node.params.items():
                state[k] = v
            return
        if handler_name == "echo":
            # use EchoAdapter to echo input to output
            input_key = str(node.params.get("input_key", "input"))
            output_key = str(node.params.get("output_key", "output"))
            content = str(state.get(input_key, ""))
            req = ChatCompletionRequest(messages=[ChatMessage(role="user", content=content)])
            resp = await self._echo.chat(req)
            state[output_key] = resp.choices[0].message.content
            return
        if handler_name == "tokenize":
            # simple tokenization: split text into tokens (words)
            input_key = str(node.params.get("input_key", "input"))
            output_key = str(node.params.get("output_key", "tokens"))
            content = str(state.get(input_key, ""))
            # Simple word tokenization for testing
            tokens = content.split()
            state[output_key] = tokens
            return
        if handler_name == "retrieve":
            # Simple RAG retrieval: index provided docs (optional) and search query from state
            # params: docs (list[{id,text}]) or doc_state_key, query_key, result_key, top_k
            from plugins.retrievers.in_memory_retriever import InMemoryRetriever  # local import

            retriever = InMemoryRetriever()
            docs = node.params.get("docs")
            doc_state_key = node.params.get("doc_state_key")
            if docs is None and doc_state_key:
                docs = state.get(doc_state_key)
            if docs:
                # Expect list of {id, text}
                items = [(str(d["id"]), str(d["text"])) for d in docs]
                retriever.index(items)
            query_key = str(node.params.get("query_key", "query"))
            result_key = str(node.params.get("result_key", "retrieved"))
            top_k = int(node.params.get("top_k", 3))
            query_text = str(state.get(query_key, ""))
            results = retriever.search(query_text, top_k=top_k)
            mode = str(node.params.get("result_mode", "ids")).lower()
            if mode == "texts":
                state[result_key] = [r.metadata.get("text") for r in results]
            else:
                state[result_key] = [r.id for r in results]
            return
        if handler_name == "call_llm":
            # Invoke an LLM adapter with optional system message and context join
            # params:
            #   adapter: "echo" (default) | "ollama"
            #   input_key: state key for prompt (default: "input")
            #   context_key: optional state key whose list/str will be prepended as context
            #   join_sep: separator for joining context if list (default: "\n")
            #   output_key: state key to store assistant reply (default: "output")
            #   model, temperature, max_tokens: forwarded to adapter if applicable
            adapter_name = str(node.params.get("adapter", "echo")).lower()
            input_key = str(node.params.get("input_key", "input"))
            output_key = str(node.params.get("output_key", "output"))
            context_key = node.params.get("context_key")
            join_sep = str(node.params.get("join_sep", "\n"))
            system_text = node.params.get("system")
            model = node.params.get("model")
            temperature = float(node.params.get("temperature", 0.0))
            max_tokens = node.params.get("max_tokens")

            # Build messages
            messages = []
            if system_text:
                messages.append(ChatMessage(role="system", content=str(system_text)))
            # optional context
            if context_key and (ctx := state.get(str(context_key))):
                if isinstance(ctx, list):
                    ctx_text = join_sep.join(str(x) for x in ctx)
                else:
                    ctx_text = str(ctx)
                messages.append(ChatMessage(role="user", content=f"Context:\n{ctx_text}"))
            # main prompt
            prompt = str(state.get(input_key, ""))
            messages.append(ChatMessage(role="user", content=prompt))

            req = ChatCompletionRequest(messages=messages, model=model, temperature=temperature, max_tokens=max_tokens or None, stream=False)
            if adapter_name == "ollama":
                from ..adapters.ollama_adapter import OllamaAdapter  # local import

                adapter = OllamaAdapter()
            else:
                adapter = self._echo
            resp = await adapter.chat(req)
            state[output_key] = resp.choices[0].message.content
            return
        # Fallback: unrecognized handler
        raise ValueError(f"Unknown handler: {handler_name}")

