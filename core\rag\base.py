"""Base classes and data structures for RAG system."""

from __future__ import annotations

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union
from uuid import uuid4


class DocumentType(Enum):
    """Types of documents that can be processed."""
    TEXT = "text"
    PDF = "pdf"
    HTML = "html"
    MARKDOWN = "markdown"
    JSON = "json"
    CSV = "csv"
    UNKNOWN = "unknown"


@dataclass
class Document:
    """A document to be processed by the RAG system.
    
    Represents a single document with content, metadata, and processing information.
    Documents can be text files, PDFs, web pages, or other structured content.
    
    Attributes:
        id: Unique document identifier
        content: Raw document content
        title: Document title or filename
        doc_type: Type of document
        metadata: Additional document metadata
        source: Source URL or file path
        created_at: Document creation timestamp
        processed_at: When document was processed
        
    Example:
        >>> doc = Document(
        ...     content="Machine learning is a subset of AI...",
        ...     title="ML Introduction",
        ...     doc_type=DocumentType.TEXT,
        ...     metadata={"author": "<PERSON>", "category": "education"}
        ... )
    """
    content: str
    title: str = ""
    doc_type: DocumentType = DocumentType.TEXT
    metadata: Dict[str, Any] = field(default_factory=dict)
    source: Optional[str] = None
    id: str = field(default_factory=lambda: str(uuid4()))
    created_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if not self.title and self.source:
            # Extract title from source path
            import os
            self.title = os.path.basename(self.source)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert document to dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "title": self.title,
            "doc_type": self.doc_type.value,
            "metadata": self.metadata,
            "source": self.source,
            "created_at": self.created_at.isoformat(),
            "processed_at": self.processed_at.isoformat() if self.processed_at else None
        }


@dataclass
class Chunk:
    """A chunk of text extracted from a document.
    
    Represents a portion of a document that has been split for processing.
    Chunks maintain references to their source document and position.
    
    Attributes:
        id: Unique chunk identifier
        content: Chunk text content
        document_id: ID of source document
        chunk_index: Position within document
        start_char: Starting character position in document
        end_char: Ending character position in document
        metadata: Additional chunk metadata
        embedding: Optional vector embedding
        
    Example:
        >>> chunk = Chunk(
        ...     content="Machine learning algorithms learn patterns...",
        ...     document_id="doc123",
        ...     chunk_index=0,
        ...     start_char=0,
        ...     end_char=47
        ... )
    """
    content: str
    document_id: str
    chunk_index: int = 0
    start_char: int = 0
    end_char: int = 0
    metadata: Dict[str, Any] = field(default_factory=dict)
    embedding: Optional[List[float]] = None
    id: str = field(default_factory=lambda: str(uuid4()))
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.end_char == 0:
            self.end_char = len(self.content)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "document_id": self.document_id,
            "chunk_index": self.chunk_index,
            "start_char": self.start_char,
            "end_char": self.end_char,
            "metadata": self.metadata,
            "has_embedding": self.embedding is not None
        }


@dataclass
class RetrievalResult:
    """Result from a retrieval operation.
    
    Contains a chunk with its relevance score and retrieval metadata.
    
    Attributes:
        chunk: Retrieved text chunk
        score: Relevance score (higher = more relevant)
        retrieval_method: Method used for retrieval
        metadata: Additional retrieval metadata
        
    Example:
        >>> result = RetrievalResult(
        ...     chunk=chunk,
        ...     score=0.95,
        ...     retrieval_method="vector_similarity",
        ...     metadata={"query_expansion": True}
        ... )
    """
    chunk: Chunk
    score: float
    retrieval_method: str = "unknown"
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "chunk": self.chunk.to_dict(),
            "score": self.score,
            "retrieval_method": self.retrieval_method,
            "metadata": self.metadata
        }


@dataclass
class RAGResult:
    """Complete result from a RAG query.
    
    Contains the generated response along with source chunks,
    grounding information, and processing metadata.
    
    Attributes:
        query: Original query text
        response: Generated response
        source_chunks: Chunks used for generation
        grounding_score: Confidence in response grounding
        processing_time: Time taken to process query
        metadata: Additional processing metadata
        
    Example:
        >>> result = RAGResult(
        ...     query="What is machine learning?",
        ...     response="Machine learning is a subset of AI...",
        ...     source_chunks=[chunk1, chunk2],
        ...     grounding_score=0.92
        ... )
    """
    query: str
    response: str
    source_chunks: List[RetrievalResult]
    grounding_score: float = 0.0
    processing_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "query": self.query,
            "response": self.response,
            "source_chunks": [chunk.to_dict() for chunk in self.source_chunks],
            "grounding_score": self.grounding_score,
            "processing_time": self.processing_time,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }


class RAGError(Exception):
    """Base exception for RAG-related errors."""
    
    def __init__(self, message: str, component: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.component = component
        self.details = details or {}


class ChunkingError(RAGError):
    """Raised when document chunking fails."""
    pass


class RetrievalError(RAGError):
    """Raised when document retrieval fails."""
    pass


class GroundingError(RAGError):
    """Raised when grounding validation fails."""
    pass


# Type aliases for convenience
DocumentList = List[Document]
ChunkList = List[Chunk]
RetrievalResultList = List[RetrievalResult]
