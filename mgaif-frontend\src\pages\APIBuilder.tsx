import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Tree,
  Tabs,
  Row,
  Col,
  Divider,
  message,
  Tag,
  Tooltip,
  Collapse,
  Switch,
  CodeEditor,
} from 'antd';
import {
  PlusOutlined,
  ApiOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  CopyOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  FileTextOutlined,
  CloudOutlined,
  CodeOutlined,
} from '@ant-design/icons';
import type { TableColumnsType, TreeDataNode } from 'antd';
import { api } from '../services/api';
import type { APISpec, APIEndpoint, APIParameter, JSONSchema } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Panel } = Collapse;

export const APIBuilder: React.FC = () => {
  const [apiSpecs, setApiSpecs] = useState<APISpec[]>([]);
  const [currentSpec, setCurrentSpec] = useState<APISpec | null>(null);
  const [selectedEndpoint, setSelectedEndpoint] = useState<APIEndpoint | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [endpointModalVisible, setEndpointModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  const [form] = Form.useForm();
  const [endpointForm] = Form.useForm();
  const [testForm] = Form.useForm();

  // Load data on component mount
  useEffect(() => {
    loadAPISpecs();
  }, []);

  const loadAPISpecs = async () => {
    try {
      setLoading(true);
      // Mock data for development
      setApiSpecs([
        {
          id: '1',
          name: 'Customer Management API',
          description: 'RESTful API for managing customer data and interactions',
          version: '1.0.0',
          baseUrl: 'https://api.example.com/v1',
          endpoints: [
            {
              id: '1',
              path: '/customers',
              method: 'GET',
              summary: 'List customers',
              description: 'Retrieve a paginated list of customers',
              parameters: [
                {
                  name: 'page',
                  in: 'query',
                  required: false,
                  schema: { type: 'integer', example: 1 },
                  description: 'Page number for pagination'
                },
                {
                  name: 'limit',
                  in: 'query',
                  required: false,
                  schema: { type: 'integer', example: 10 },
                  description: 'Number of items per page'
                }
              ],
              responses: [
                {
                  statusCode: '200',
                  description: 'Successful response',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          customers: {
                            type: 'array',
                            items: { type: 'object' }
                          },
                          total: { type: 'integer' },
                          page: { type: 'integer' }
                        }
                      }
                    }
                  }
                }
              ]
            },
            {
              id: '2',
              path: '/customers',
              method: 'POST',
              summary: 'Create customer',
              description: 'Create a new customer record',
              requestBody: {
                required: true,
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        name: { type: 'string', example: 'John Doe' },
                        email: { type: 'string', format: 'email', example: '<EMAIL>' },
                        phone: { type: 'string', example: '+1234567890' }
                      },
                      required: ['name', 'email']
                    }
                  }
                }
              },
              responses: [
                {
                  statusCode: '201',
                  description: 'Customer created successfully',
                  content: {
                    'application/json': {
                      schema: {
                        type: 'object',
                        properties: {
                          id: { type: 'string' },
                          name: { type: 'string' },
                          email: { type: 'string' },
                          phone: { type: 'string' },
                          createdAt: { type: 'string', format: 'date-time' }
                        }
                      }
                    }
                  }
                }
              ],
              parameters: []
            }
          ],
          security: [
            {
              type: 'http',
              scheme: 'bearer',
              bearerFormat: 'JWT'
            }
          ],
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-16T14:20:00Z'
        }
      ]);
    } catch (error) {
      console.error('Failed to load API specs:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAPI = async () => {
    try {
      const values = await form.validateFields();
      
      const newSpec: Partial<APISpec> = {
        name: values.name,
        description: values.description,
        version: values.version || '1.0.0',
        baseUrl: values.baseUrl,
        endpoints: [],
        security: []
      };

      // In real implementation, call API
      // await api.apiBuilder.create(newSpec);
      
      message.success('API specification created successfully!');
      setCreateModalVisible(false);
      form.resetFields();
      loadAPISpecs();
    } catch (error) {
      message.error('Failed to create API specification');
    }
  };

  const handleAddEndpoint = async () => {
    try {
      const values = await endpointForm.validateFields();
      
      if (!currentSpec) return;

      const newEndpoint: Partial<APIEndpoint> = {
        path: values.path,
        method: values.method,
        summary: values.summary,
        description: values.description,
        parameters: [],
        responses: []
      };

      // In real implementation, call API
      // await api.apiBuilder.addEndpoint(currentSpec.id, newEndpoint);
      
      message.success('Endpoint added successfully!');
      setEndpointModalVisible(false);
      endpointForm.resetFields();
      loadAPISpecs();
    } catch (error) {
      message.error('Failed to add endpoint');
    }
  };

  const handleTestEndpoint = async () => {
    try {
      const values = await testForm.validateFields();
      
      if (!selectedEndpoint) return;

      // In real implementation, make actual API call
      // const response = await api.apiBuilder.testEndpoint(selectedEndpoint.id, values);
      
      message.success('API test completed successfully!');
      // Show response in modal or separate panel
    } catch (error) {
      message.error('API test failed');
    }
  };

  const generateOpenAPISpec = (spec: APISpec) => {
    const openApiSpec = {
      openapi: '3.0.0',
      info: {
        title: spec.name,
        description: spec.description,
        version: spec.version
      },
      servers: [
        {
          url: spec.baseUrl,
          description: 'Production server'
        }
      ],
      paths: spec.endpoints.reduce((paths, endpoint) => {
        const path = endpoint.path;
        const method = endpoint.method.toLowerCase();
        
        if (!paths[path]) {
          paths[path] = {};
        }
        
        paths[path][method] = {
          summary: endpoint.summary,
          description: endpoint.description,
          parameters: endpoint.parameters?.map(param => ({
            name: param.name,
            in: param.in,
            required: param.required,
            schema: param.schema,
            description: param.description
          })),
          requestBody: endpoint.requestBody,
          responses: endpoint.responses?.reduce((responses, response) => {
            responses[response.statusCode] = {
              description: response.description,
              content: response.content
            };
            return responses;
          }, {} as any)
        };
        
        return paths;
      }, {} as any),
      components: {
        securitySchemes: spec.security?.reduce((schemes, security) => {
          schemes[security.type] = {
            type: security.type,
            scheme: security.scheme,
            bearerFormat: security.bearerFormat
          };
          return schemes;
        }, {} as any)
      }
    };

    return JSON.stringify(openApiSpec, null, 2);
  };

  const apiSpecColumns: TableColumnsType<APISpec> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <ApiOutlined />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Version',
      dataIndex: 'version',
      key: 'version',
      render: (version) => <Tag color="blue">v{version}</Tag>,
    },
    {
      title: 'Endpoints',
      key: 'endpoints',
      render: (_, record) => `${record.endpoints.length} endpoints`,
    },
    {
      title: 'Base URL',
      dataIndex: 'baseUrl',
      key: 'baseUrl',
      render: (url) => (
        <Text code style={{ fontSize: '12px' }}>
          {url}
        </Text>
      ),
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Edit">
            <Button 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => setCurrentSpec(record)}
            />
          </Tooltip>
          <Tooltip title="Test">
            <Button 
              icon={<PlayCircleOutlined />} 
              size="small"
              onClick={() => setTestModalVisible(true)}
            />
          </Tooltip>
          <Tooltip title="Export">
            <Button 
              icon={<CopyOutlined />} 
              size="small"
              onClick={() => {
                const spec = generateOpenAPISpec(record);
                navigator.clipboard.writeText(spec);
                message.success('OpenAPI specification copied to clipboard');
              }}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const endpointColumns: TableColumnsType<APIEndpoint> = [
    {
      title: 'Method',
      dataIndex: 'method',
      key: 'method',
      render: (method) => {
        const colors = {
          GET: 'green',
          POST: 'blue',
          PUT: 'orange',
          DELETE: 'red',
          PATCH: 'purple'
        };
        return <Tag color={colors[method as keyof typeof colors]}>{method}</Tag>;
      },
    },
    {
      title: 'Path',
      dataIndex: 'path',
      key: 'path',
      render: (path) => <Text code>{path}</Text>,
    },
    {
      title: 'Summary',
      dataIndex: 'summary',
      key: 'summary',
    },
    {
      title: 'Parameters',
      key: 'parameters',
      render: (_, record) => `${record.parameters?.length || 0} params`,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button 
            icon={<EditOutlined />} 
            size="small"
            onClick={() => setSelectedEndpoint(record)}
          />
          <Button 
            icon={<PlayCircleOutlined />} 
            size="small"
            onClick={() => {
              setSelectedEndpoint(record);
              setTestModalVisible(true);
            }}
          />
          <Button 
            icon={<DeleteOutlined />} 
            size="small" 
            danger
          />
        </Space>
      ),
    },
  ];

  if (currentSpec) {
    return (
      <div style={{ padding: '24px' }}>
        <Card>
          <div style={{ marginBottom: '24px' }}>
            <Button 
              onClick={() => setCurrentSpec(null)}
              style={{ marginBottom: '16px' }}
            >
              ← Back to API Specifications
            </Button>
            <Title level={3}>{currentSpec.name}</Title>
            <Paragraph type="secondary">{currentSpec.description}</Paragraph>
          </div>

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="Endpoints" key="endpoints">
              <div style={{ marginBottom: '16px' }}>
                <Button 
                  type="primary" 
                  icon={<PlusOutlined />}
                  onClick={() => setEndpointModalVisible(true)}
                >
                  Add Endpoint
                </Button>
              </div>
              
              <Table
                columns={endpointColumns}
                dataSource={currentSpec.endpoints}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
            
            <TabPane tab="Security" key="security">
              <div>
                <Title level={4}>Security Schemes</Title>
                <Paragraph type="secondary">
                  Configure authentication and authorization for your API
                </Paragraph>
                
                <Card>
                  <Form layout="vertical">
                    <Form.Item label="Authentication Type">
                      <Select defaultValue="bearer">
                        <Select.Option value="none">None</Select.Option>
                        <Select.Option value="apiKey">API Key</Select.Option>
                        <Select.Option value="bearer">Bearer Token</Select.Option>
                        <Select.Option value="basic">Basic Auth</Select.Option>
                        <Select.Option value="oauth2">OAuth 2.0</Select.Option>
                      </Select>
                    </Form.Item>
                    
                    <Form.Item label="Token Format">
                      <Input placeholder="JWT" />
                    </Form.Item>
                    
                    <Button type="primary">Save Security Settings</Button>
                  </Form>
                </Card>
              </div>
            </TabPane>
            
            <TabPane tab="Documentation" key="documentation">
              <div>
                <Title level={4}>API Documentation</Title>
                <Paragraph type="secondary">
                  Auto-generated documentation based on your API specification
                </Paragraph>
                
                <Card>
                  <pre style={{ 
                    backgroundColor: '#f5f5f5', 
                    padding: '16px', 
                    borderRadius: '6px',
                    overflow: 'auto',
                    maxHeight: '400px'
                  }}>
                    {generateOpenAPISpec(currentSpec)}
                  </pre>
                </Card>
                
                <div style={{ marginTop: '16px' }}>
                  <Space>
                    <Button 
                      icon={<CopyOutlined />}
                      onClick={() => {
                        const spec = generateOpenAPISpec(currentSpec);
                        navigator.clipboard.writeText(spec);
                        message.success('OpenAPI specification copied to clipboard');
                      }}
                    >
                      Copy Specification
                    </Button>
                    <Button icon={<FileTextOutlined />}>
                      Generate Docs
                    </Button>
                    <Button icon={<CloudOutlined />}>
                      Deploy
                    </Button>
                  </Space>
                </div>
              </div>
            </TabPane>
          </Tabs>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>API Builder</Title>
        <Paragraph type="secondary">
          Design, build, and deploy RESTful APIs with visual tools and automatic documentation generation
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                {apiSpecs.length}
              </Title>
              <Text type="secondary">API Specifications</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                {apiSpecs.reduce((sum, spec) => sum + spec.endpoints.length, 0)}
              </Title>
              <Text type="secondary">Total Endpoints</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#faad14' }}>
                {apiSpecs.filter(spec => spec.endpoints.length > 0).length}
              </Title>
              <Text type="secondary">Active APIs</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#f5222d' }}>
                0
              </Title>
              <Text type="secondary">Deployed</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4}>Your API Specifications</Title>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => setCreateModalVisible(true)}
          >
            Create API
          </Button>
        </div>

        <Table
          columns={apiSpecColumns}
          dataSource={apiSpecs}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      {/* Create API Modal */}
      <Modal
        title="Create New API Specification"
        open={createModalVisible}
        onOk={handleCreateAPI}
        onCancel={() => setCreateModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="API Name"
            name="name"
            rules={[{ required: true, message: 'Please enter API name' }]}
          >
            <Input placeholder="Enter API name" />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe what this API does" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Version" name="version">
                <Input placeholder="1.0.0" defaultValue="1.0.0" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Base URL"
                name="baseUrl"
                rules={[{ required: true, message: 'Please enter base URL' }]}
              >
                <Input placeholder="https://api.example.com/v1" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Add Endpoint Modal */}
      <Modal
        title="Add New Endpoint"
        open={endpointModalVisible}
        onOk={handleAddEndpoint}
        onCancel={() => setEndpointModalVisible(false)}
        width={700}
      >
        <Form form={endpointForm} layout="vertical">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="Method"
                name="method"
                rules={[{ required: true, message: 'Please select method' }]}
              >
                <Select placeholder="Select HTTP method">
                  <Select.Option value="GET">GET</Select.Option>
                  <Select.Option value="POST">POST</Select.Option>
                  <Select.Option value="PUT">PUT</Select.Option>
                  <Select.Option value="DELETE">DELETE</Select.Option>
                  <Select.Option value="PATCH">PATCH</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                label="Path"
                name="path"
                rules={[{ required: true, message: 'Please enter path' }]}
              >
                <Input placeholder="/users/{id}" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            label="Summary"
            name="summary"
            rules={[{ required: true, message: 'Please enter summary' }]}
          >
            <Input placeholder="Brief description of the endpoint" />
          </Form.Item>
          
          <Form.Item label="Description" name="description">
            <TextArea rows={3} placeholder="Detailed description of the endpoint functionality" />
          </Form.Item>
        </Form>
      </Modal>

      {/* Test Endpoint Modal */}
      <Modal
        title={`Test ${selectedEndpoint?.method} ${selectedEndpoint?.path}`}
        open={testModalVisible}
        onOk={handleTestEndpoint}
        onCancel={() => setTestModalVisible(false)}
        width={800}
      >
        <Form form={testForm} layout="vertical">
          <Form.Item label="Request Headers">
            <TextArea 
              rows={3} 
              placeholder='{"Authorization": "Bearer token", "Content-Type": "application/json"}'
            />
          </Form.Item>
          
          <Form.Item label="Request Body">
            <TextArea 
              rows={5} 
              placeholder='{"key": "value"}'
            />
          </Form.Item>
          
          <Form.Item label="Query Parameters">
            <TextArea 
              rows={2} 
              placeholder='page=1&limit=10'
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
