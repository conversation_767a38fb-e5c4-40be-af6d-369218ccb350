# M-GAIF Web Console Quick Start Guide

## 🚀 Getting Started in 5 Minutes

This guide will get you up and running with the M-GAIF Web Console quickly.

### Step 1: Start the Backend

First, ensure the M-GAIF backend is running:

```bash
# In the main project directory
pip install -r requirements.txt

# Start the MCP API (port 8000)
uvicorn core.mcp.api:app --reload
```

### Step 2: Start the Web Console

```bash
# In the webapp directory
cd webapp
npm install
npm run dev
```

Open your browser to: **http://localhost:5173**

### Step 3: Verify Connection

1. Click on **Settings** in the sidebar
2. Leave the API Base field empty (uses automatic proxy)
3. Navigate back to **Dashboard**

You're ready to go! 🎉

## 📋 Essential Workflows

### Try Your First RAG Workflow

1. **Go to Workflows** → Click "Workflows" in sidebar
2. **Select Template** → Choose "RAG + LLM (echo)" from Templates tab
3. **Run Workflow** → Click the "Run" button
4. **View Results** → See the output in the right panel

**What happened?**
- Documents were indexed in memory
- A query was processed to find relevant documents
- An LLM (echo adapter) generated a response

### Index and Search Documents

1. **Go to RAG Stores** → Click "RAG Stores" in sidebar
2. **Add Documents** → In the left panel, enter some text:
   ```
   The quick brown fox jumps over the lazy dog
   Python is a programming language
   Machine learning uses algorithms to find patterns
   ```
3. **Index** → Click "Index" button
4. **Search** → In the right panel, search for "programming"
5. **View Results** → See similarity scores for your documents

### Test Language Models

1. **Go to LLMs** → Click "LLMs" in sidebar
2. **Choose Mode** → Select "Edge (OpenAI-compatible)"
3. **Enter Prompt** → Type: "Explain what a RAG system does"
4. **Enable Streaming** → Toggle the "Stream" switch
5. **Send** → Click "Send" and watch the response stream in real-time

## 🎯 Key Features Overview

### Dashboard
- **Purpose**: Central navigation hub
- **Use**: Quick access to all features
- **Tip**: Click any card to jump to that feature

### Workflows
- **Purpose**: Build and execute AI workflows
- **Templates**: Pre-built examples for common tasks
- **Editor**: YAML-based workflow definition
- **Output**: JSON state showing execution results

### RAG Stores
- **Purpose**: Document indexing and search
- **Left Panel**: Add and index documents
- **Right Panel**: Search and view results
- **Tip**: Use one document per line for bulk indexing

### LLMs
- **Purpose**: Test language model interactions
- **Edge Mode**: OpenAI-compatible API with streaming
- **MCP Mode**: Tool-based LLM interface
- **Tip**: Use Edge mode for streaming responses

### Settings
- **Purpose**: Configure API connections
- **Development**: Leave empty (uses proxy)
- **Production**: Set full backend URL
- **Tip**: Settings persist in your browser

## 🔧 Common Tasks

### Create a Custom Workflow

1. Go to **Workflows**
2. Start with a template or create from scratch
3. Modify the YAML:
   ```yaml
   name: my_custom_workflow
   nodes:
     - id: start
       handler: set
       params:
         message: "Hello from my workflow!"
     - id: end
       handler: noop
   edges:
     - from: start
       to: end
   ```
4. Click **Run** to execute

### Build a Knowledge Base

1. Go to **RAG Stores**
2. Prepare your documents (one per line)
3. Click **Index** to add them
4. Test searches with different queries
5. Note the similarity scores to understand relevance

### Compare LLM Responses

1. Go to **LLMs**
2. Try the same prompt with different modes:
   - **MCP Mode**: Uses echo adapter (deterministic)
   - **Edge Mode**: Can use different models
3. Compare response styles and capabilities

## 🛠️ Troubleshooting

### "Connection Failed" Error
- ✅ Check that backend is running on port 8000
- ✅ Verify Settings → API Base is empty or correct
- ✅ Try refreshing the page

### Workflow Won't Run
- ✅ Check YAML syntax (proper indentation)
- ✅ Ensure all node IDs are unique
- ✅ Verify edges connect existing nodes

### No Search Results
- ✅ Make sure documents are indexed first
- ✅ Try simpler or more specific queries
- ✅ Check that documents contain relevant content

### Streaming Not Working
- ✅ Use "Edge" mode for streaming
- ✅ Enable the "Stream" toggle
- ✅ Check browser console for errors

## 📚 Next Steps

### Learn More
- Read the full [User Guide](webapp_user_guide.md) for detailed features
- Explore the [Developer Guide](developer_guide.md) for technical details
- Check the [API Documentation](api.md) for backend integration

### Advanced Usage
- Create complex multi-step workflows
- Build domain-specific knowledge bases
- Integrate with external LLM providers
- Deploy to production environments

### Get Help
- Check the troubleshooting section above
- Review browser console for error messages
- Verify backend logs for API issues
- Ensure all prerequisites are met

## 🎉 Success!

You now have a working M-GAIF Web Console! You can:
- ✅ Build and execute AI workflows
- ✅ Index and search documents
- ✅ Test language model interactions
- ✅ Configure system settings

**Happy building!** 🚀

---

*For detailed documentation, see the complete [User Guide](webapp_user_guide.md)*
