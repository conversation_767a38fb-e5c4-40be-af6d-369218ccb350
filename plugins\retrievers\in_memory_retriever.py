"""In-memory document retriever implementation.

This module provides a complete document retrieval system that combines
text embedding and vector search for semantic document retrieval. It's
designed for development, testing, and small-scale applications.

Features:
- Document indexing with automatic embedding generation
- Semantic similarity search using vector embeddings
- In-memory storage for fast access
- Configurable embedding dimensions
- Simple document format (ID + text)

Architecture:
- Uses SimpleEmbedder for text-to-vector conversion
- Uses InMemoryVectorStore for vector similarity search
- Maintains document metadata for result enrichment

Example:
    >>> retriever = InMemoryRetriever(dim=128)
    >>> documents = [
    ...     ("doc1", "Machine learning is a subset of AI"),
    ...     ("doc2", "Deep learning uses neural networks")
    ... ]
    >>> retriever.index(documents)
    >>> results = retriever.search("artificial intelligence", top_k=2)

Note:
    This implementation is suitable for development and small datasets.
    For production use with large document collections, consider dedicated
    vector databases like Pinecone, Weaviate, or Chroma.
"""

from __future__ import annotations

from typing import Dict, Iterable, List, Tuple

from core.contracts.retrieval import <PERSON>Result, VectorRecord
from core.stores.in_memory_vector import InMemoryVectorStore
from plugins.embedders.simple_embedder import SimpleEmbedder


class InMemoryRetriever:
    """Document retriever with in-memory vector storage and embedding.

    Provides complete document indexing and retrieval functionality using
    in-memory storage and hash-based embeddings. Suitable for development,
    testing, and small-scale applications.

    The retriever maintains:
    - Document text storage for metadata enrichment
    - Vector embeddings for similarity search
    - Configurable embedding dimensions

    Args:
        dim: Embedding dimension for vector representations

    Example:
        >>> retriever = InMemoryRetriever(dim=64)
        >>> docs = [("id1", "AI text"), ("id2", "ML text")]
        >>> retriever.index(docs)
        >>> results = retriever.search("artificial intelligence", top_k=1)
        >>> print(results[0].id)  # "id1"

    Note:
        Memory usage scales with document count and embedding dimension.
        Consider external vector databases for large document collections.
    """

    def __init__(self, dim: int = 8) -> None:
        """Initialize retriever with specified embedding dimension.

        Args:
            dim: Vector dimension for document embeddings
        """
        self.embedder = SimpleEmbedder(dim=dim)
        self.store = InMemoryVectorStore()
        self._docs: Dict[str, str] = {}

    def index(self, items: Iterable[Tuple[str, str]]) -> None:
        """Index documents for retrieval by generating embeddings.

        Processes a collection of documents by generating vector embeddings
        and storing them in the vector store for similarity search.

        Args:
            items: Iterable of (document_id, text) tuples to index

        Example:
            >>> retriever = InMemoryRetriever()
            >>> docs = [
            ...     ("doc1", "Machine learning algorithms"),
            ...     ("doc2", "Deep neural networks")
            ... ]
            >>> retriever.index(docs)

        Note:
            Existing documents with the same ID will be overwritten.
            Large document collections may consume significant memory.
        """
        ids: List[str] = []
        texts: List[str] = []
        for doc_id, text in items:
            self._docs[doc_id] = text
            ids.append(doc_id)
            texts.append(text)
        vectors = self.embedder.embed(texts)
        records = [VectorRecord(id=i, vector=v, metadata={"text": t}) for i, v, t in zip(ids, vectors, texts)]
        self.store.add(records)

    def search(self, query_text: str, top_k: int = 5) -> List[SearchResult]:
        """Search for documents similar to the query text.

        Finds the most similar documents to the query using vector similarity
        search on the indexed document embeddings.

        Args:
            query_text: Text query to search for
            top_k: Maximum number of results to return

        Returns:
            List of SearchResult objects ordered by similarity score (descending)

        Example:
            >>> retriever = InMemoryRetriever()
            >>> retriever.index([("doc1", "AI and machine learning")])
            >>> results = retriever.search("artificial intelligence", top_k=1)
            >>> print(results[0].id)  # "doc1"
            >>> print(results[0].score)  # similarity score

        Note:
            Returns empty list if no documents are indexed.
            Scores are cosine similarity values (higher = more similar).
        """
        qv = self.embedder.embed([query_text])[0]
        return self.store.search(qv, top_k=top_k)
