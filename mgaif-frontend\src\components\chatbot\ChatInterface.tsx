import React, { useState, useRef, useEffect } from 'react';
import { 
  Card, 
  Input, 
  Button, 
  Space, 
  Typography, 
  Avatar, 
  Divider,
  Tag,
  Tooltip,
  Spin
} from 'antd';
import { 
  SendOutlined, 
  RobotOutlined, 
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useChatbotStore } from '../../stores/chatbotStore';
import type { Message, Conversation } from '../../types/chatbot';

const { TextArea } = Input;

interface ChatInterfaceProps {
  chatbotId: string;
  height?: number;
  showHeader?: boolean;
  onMessageSent?: (message: string) => void;
}

export const ChatInterface: React.FC<ChatInterfaceProps> = ({
  chatbotId,
  height = 500,
  showHeader = true,
  onMessageSent,
}) => {
  const { 
    currentChatbot, 
    sendMessage, 
    createConversation,
    isLoading 
  } = useChatbotStore();

  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [currentConversation, setCurrentConversation] = useState<Conversation | null>(null);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Initialize conversation
    const initConversation = async () => {
      if (chatbotId && !currentConversation) {
        const conversation = await createConversation(chatbotId);
        setCurrentConversation(conversation);
        
        // Add greeting message
        if (currentChatbot?.personality.greeting) {
          const greetingMessage: Message = {
            id: 'greeting',
            conversationId: conversation.id,
            type: 'bot',
            content: currentChatbot.personality.greeting,
            timestamp: new Date().toISOString(),
            metadata: {},
          };
          setMessages([greetingMessage]);
        }
      }
    };

    initConversation();
  }, [chatbotId, currentConversation, createConversation, currentChatbot]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim() || !currentConversation || isTyping) return;

    const userMessage: Message = {
      id: `user-${Date.now()}`,
      conversationId: currentConversation.id,
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date().toISOString(),
      metadata: {},
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsTyping(true);
    onMessageSent?.(userMessage.content);

    try {
      // Simulate typing delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const botResponse = await sendMessage(
        chatbotId, 
        currentConversation.id, 
        userMessage.content
      );
      
      setMessages(prev => [...prev, botResponse]);
    } catch (error) {
      const errorMessage: Message = {
        id: `error-${Date.now()}`,
        conversationId: currentConversation.id,
        type: 'bot',
        content: currentChatbot?.personality.fallbackMessage || "I'm sorry, something went wrong. Please try again.",
        timestamp: new Date().toISOString(),
        metadata: { error: true },
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTime = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const renderMessage = (message: Message) => {
    const isBot = message.type === 'bot';
    const isError = message.metadata.error;

    return (
      <div
        key={message.id}
        style={{
          display: 'flex',
          justifyContent: isBot ? 'flex-start' : 'flex-end',
          marginBottom: '16px',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: isBot ? 'row' : 'row-reverse',
            alignItems: 'flex-start',
            maxWidth: '80%',
          }}
        >
          <Avatar
            size="small"
            icon={isBot ? <RobotOutlined /> : <UserOutlined />}
            style={{
              backgroundColor: isBot ? '#1890ff' : '#52c41a',
              margin: isBot ? '0 8px 0 0' : '0 0 0 8px',
            }}
          />
          
          <div>
            <div
              style={{
                background: isBot ? '#f0f0f0' : '#1890ff',
                color: isBot ? '#000' : '#fff',
                padding: '8px 12px',
                borderRadius: '12px',
                borderTopLeftRadius: isBot ? '4px' : '12px',
                borderTopRightRadius: isBot ? '12px' : '4px',
                wordBreak: 'break-word',
                border: isError ? '1px solid #ff4d4f' : 'none',
              }}
            >
              <Typography.Text
                style={{ 
                  color: isBot ? '#000' : '#fff',
                  fontSize: '14px',
                }}
              >
                {message.content}
              </Typography.Text>
            </div>
            
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: isBot ? 'flex-start' : 'flex-end',
                marginTop: '4px',
                gap: '8px',
              }}
            >
              <Typography.Text
                type="secondary"
                style={{ fontSize: '11px' }}
              >
                {formatTime(message.timestamp)}
              </Typography.Text>
              
              {message.metadata.confidence && (
                <Tooltip title={`Confidence: ${(message.metadata.confidence * 100).toFixed(0)}%`}>
                  <Tag 
                    size="small" 
                    color={message.metadata.confidence > 0.8 ? 'green' : 'orange'}
                  >
                    {(message.metadata.confidence * 100).toFixed(0)}%
                  </Tag>
                </Tooltip>
              )}
              
              {message.metadata.processingTime && (
                <Tooltip title={`Response time: ${message.metadata.processingTime}ms`}>
                  <ClockCircleOutlined style={{ fontSize: '10px', color: '#999' }} />
                </Tooltip>
              )}
              
              {!isBot && (
                <CheckCircleOutlined style={{ fontSize: '10px', color: '#52c41a' }} />
              )}
            </div>
            
            {message.metadata.sources && message.metadata.sources.length > 0 && (
              <div style={{ marginTop: '4px' }}>
                <Typography.Text type="secondary" style={{ fontSize: '10px' }}>
                  Sources: {message.metadata.sources.join(', ')}
                </Typography.Text>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <Card
      title={showHeader && currentChatbot ? (
        <Space>
          <Avatar icon={<RobotOutlined />} />
          <div>
            <Typography.Text strong>{currentChatbot.name}</Typography.Text>
            <br />
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              {currentChatbot.description}
            </Typography.Text>
          </div>
        </Space>
      ) : null}
      style={{ height }}
      bodyStyle={{ 
        padding: 0, 
        height: showHeader ? height - 57 : height,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {/* Messages Area */}
      <div
        style={{
          flex: 1,
          padding: '16px',
          overflowY: 'auto',
          backgroundColor: '#fafafa',
        }}
      >
        {messages.map(renderMessage)}
        
        {isTyping && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
            <Avatar
              size="small"
              icon={<RobotOutlined />}
              style={{ backgroundColor: '#1890ff', marginRight: '8px' }}
            />
            <div
              style={{
                background: '#f0f0f0',
                padding: '8px 12px',
                borderRadius: '12px',
                borderTopLeftRadius: '4px',
              }}
            >
              <Spin size="small" />
              <Typography.Text style={{ marginLeft: '8px', fontSize: '14px' }}>
                Typing...
              </Typography.Text>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <Divider style={{ margin: 0 }} />

      {/* Input Area */}
      <div style={{ padding: '16px' }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            autoSize={{ minRows: 1, maxRows: 3 }}
            disabled={isTyping}
            style={{ resize: 'none' }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            loading={isTyping}
          />
        </Space.Compact>
        
        <Typography.Text type="secondary" style={{ fontSize: '11px', marginTop: '4px', display: 'block' }}>
          Press Enter to send, Shift+Enter for new line
        </Typography.Text>
      </div>
    </Card>
  );
};
