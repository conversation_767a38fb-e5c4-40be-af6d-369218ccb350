import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { MainLayout } from './components/layout/MainLayout';
import { Dashboard } from './pages/Dashboard';
import { Workflows } from './pages/Workflows';
import { Chatbots } from './pages/Chatbots';
import { Agents } from './pages/Agents';
import { Templates } from './pages/Templates';
import { Settings } from './pages/Settings';
import { RAGStores } from './pages/RAGStores';
import { Models } from './pages/Models';
import { APIBuilder } from './pages/APIBuilder';
import { MCPIntegrations } from './pages/MCPIntegrations';

function App() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 8,
        },
      }}
    >
      <Router>
        <MainLayout>
          <Routes>
            <Route path="/" element={<Dashboard />} />
            <Route path="/workflows" element={<Workflows />} />
            <Route path="/chatbots" element={<Chatbots />} />
            <Route path="/agents" element={<Agents />} />
            <Route path="/models" element={<Models />} />
            <Route path="/api-builder" element={<APIBuilder />} />
            <Route path="/mcp-integrations" element={<MCPIntegrations />} />
            <Route path="/rag-stores" element={<RAGStores />} />
            <Route path="/templates" element={<Templates />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </MainLayout>
      </Router>
    </ConfigProvider>
  );
}

export default App;
