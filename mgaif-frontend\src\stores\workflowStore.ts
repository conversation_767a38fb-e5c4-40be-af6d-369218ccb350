import { create } from 'zustand';
import { addEdge, applyNodeChanges, applyEdgeChanges, Connection } from 'reactflow';
import type { 
  Workflow, 
  WorkflowNode, 
  WorkflowEdge, 
  WorkflowExecution,
  NodeTemplate,
  WorkflowBuilderState 
} from '../types/workflow';
import { v4 as uuidv4 } from 'uuid';

interface WorkflowStore extends WorkflowBuilderState {
  // Workflow CRUD
  createWorkflow: (name: string, description: string) => void;
  loadWorkflow: (workflow: Workflow) => void;
  saveWorkflow: () => Promise<void>;
  updateWorkflowSettings: (settings: Partial<Workflow['settings']>) => void;
  
  // Node Management
  addNode: (template: NodeTemplate, position: { x: number; y: number }) => void;
  updateNode: (nodeId: string, data: Partial<WorkflowNode['data']>) => void;
  deleteNode: (nodeId: string) => void;
  selectNode: (node: WorkflowNode | null) => void;
  
  // Edge Management
  onConnect: (connection: Connection) => void;
  deleteEdge: (edgeId: string) => void;
  selectEdge: (edge: WorkflowEdge | null) => void;
  
  // React Flow Events
  onNodesChange: (changes: any[]) => void;
  onEdgesChange: (changes: any[]) => void;
  
  // Execution
  executeWorkflow: (input: Record<string, any>) => Promise<void>;
  stopExecution: () => void;
  clearExecutionResult: () => void;
  
  // Templates
  loadNodeTemplates: () => void;
  
  // Validation
  validateWorkflow: () => { isValid: boolean; errors: string[] };
  validateConnection: (connection: Connection) => boolean;
  
  // UI State
  setZoom: (zoom: number) => void;
  setViewport: (viewport: { x: number; y: number }) => void;
  
  // Reset
  resetWorkflow: () => void;
}

const defaultWorkflowSettings = {
  timeout: 300,
  retryAttempts: 3,
  parallelExecution: false,
  errorHandling: 'stop' as const,
  logging: true,
};

const mockNodeTemplates: NodeTemplate[] = [
  {
    id: 'input-text',
    type: 'input',
    name: 'Text Input',
    description: 'Accept text input from user',
    icon: '📝',
    category: 'input',
    defaultData: {
      label: 'Text Input',
      config: {
        inputType: 'text',
        required: true,
        placeholder: 'Enter text...',
      },
    },
    configSchema: {
      properties: {
        label: { type: 'string', label: 'Label', required: true },
        placeholder: { type: 'string', label: 'Placeholder' },
        required: { type: 'boolean', label: 'Required' },
      },
    },
  },
  {
    id: 'llm-openai',
    type: 'llm',
    name: 'OpenAI LLM',
    description: 'Process text with OpenAI models',
    icon: '🤖',
    category: 'processing',
    defaultData: {
      label: 'OpenAI LLM',
      config: {
        model: 'gpt-3.5-turbo',
        temperature: 0.7,
        maxTokens: 1000,
        systemPrompt: 'You are a helpful assistant.',
        userPrompt: '{{input}}',
      },
    },
    configSchema: {
      properties: {
        model: {
          type: 'select',
          label: 'Model',
          required: true,
          options: [
            { label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo' },
            { label: 'GPT-4', value: 'gpt-4' },
            { label: 'GPT-4 Turbo', value: 'gpt-4-turbo' },
          ],
        },
        temperature: {
          type: 'number',
          label: 'Temperature',
          validation: { min: 0, max: 2 },
        },
        maxTokens: {
          type: 'number',
          label: 'Max Tokens',
          validation: { min: 1, max: 4000 },
        },
        systemPrompt: { type: 'textarea', label: 'System Prompt' },
        userPrompt: { type: 'textarea', label: 'User Prompt', required: true },
      },
    },
  },
  {
    id: 'tool-websearch',
    type: 'tool',
    name: 'Web Search',
    description: 'Search the web for information',
    icon: '🔍',
    category: 'tools',
    defaultData: {
      label: 'Web Search',
      config: {
        toolType: 'web-search',
        query: '{{input}}',
        maxResults: 5,
      },
    },
    configSchema: {
      properties: {
        query: { type: 'string', label: 'Search Query', required: true },
        maxResults: {
          type: 'number',
          label: 'Max Results',
          validation: { min: 1, max: 20 },
        },
      },
    },
  },
  {
    id: 'condition-text',
    type: 'condition',
    name: 'Text Condition',
    description: 'Branch based on text conditions',
    icon: '🔀',
    category: 'logic',
    defaultData: {
      label: 'Text Condition',
      config: {
        condition: '{{input}}',
        operator: 'contains',
        value: '',
      },
    },
    configSchema: {
      properties: {
        condition: { type: 'string', label: 'Condition', required: true },
        operator: {
          type: 'select',
          label: 'Operator',
          required: true,
          options: [
            { label: 'Equals', value: 'equals' },
            { label: 'Contains', value: 'contains' },
            { label: 'Greater than', value: 'greater' },
            { label: 'Less than', value: 'less' },
          ],
        },
        value: { type: 'string', label: 'Value', required: true },
      },
    },
  },
  {
    id: 'output-text',
    type: 'output',
    name: 'Text Output',
    description: 'Display text output to user',
    icon: '📤',
    category: 'output',
    defaultData: {
      label: 'Text Output',
      config: {
        outputType: 'text',
        format: '{{input}}',
      },
    },
    configSchema: {
      properties: {
        format: { type: 'textarea', label: 'Output Format', required: true },
      },
    },
  },
];

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  // Initial state
  workflow: null,
  selectedNode: null,
  selectedEdge: null,
  isExecuting: false,
  executionResult: null,
  nodeTemplates: mockNodeTemplates,
  zoom: 1,
  viewport: { x: 0, y: 0 },

  // Workflow CRUD
  createWorkflow: (name: string, description: string) => {
    const newWorkflow: Workflow = {
      id: uuidv4(),
      name,
      description,
      nodes: [],
      edges: [],
      variables: [],
      settings: defaultWorkflowSettings,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      version: 1,
      status: 'draft',
    };
    
    set({ workflow: newWorkflow, selectedNode: null, selectedEdge: null });
  },

  loadWorkflow: (workflow: Workflow) => {
    set({ 
      workflow, 
      selectedNode: null, 
      selectedEdge: null,
      executionResult: null 
    });
  },

  saveWorkflow: async () => {
    const { workflow } = get();
    if (!workflow) return;

    // TODO: Implement API call to save workflow
    const updatedWorkflow = {
      ...workflow,
      updatedAt: new Date().toISOString(),
    };
    
    set({ workflow: updatedWorkflow });
  },

  updateWorkflowSettings: (settings) => {
    const { workflow } = get();
    if (!workflow) return;

    set({
      workflow: {
        ...workflow,
        settings: { ...workflow.settings, ...settings },
        updatedAt: new Date().toISOString(),
      },
    });
  },

  // Node Management
  addNode: (template: NodeTemplate, position: { x: number; y: number }) => {
    const { workflow } = get();
    if (!workflow) return;

    const newNode: WorkflowNode = {
      id: uuidv4(),
      type: template.type,
      position,
      data: {
        ...template.defaultData,
        label: template.name,
        config: { ...template.defaultData.config },
      },
    };

    set({
      workflow: {
        ...workflow,
        nodes: [...workflow.nodes, newNode],
        updatedAt: new Date().toISOString(),
      },
    });
  },

  updateNode: (nodeId: string, data: Partial<WorkflowNode['data']>) => {
    const { workflow } = get();
    if (!workflow) return;

    const updatedNodes = workflow.nodes.map((node) =>
      node.id === nodeId
        ? { ...node, data: { ...node.data, ...data } }
        : node
    );

    set({
      workflow: {
        ...workflow,
        nodes: updatedNodes,
        updatedAt: new Date().toISOString(),
      },
    });
  },

  deleteNode: (nodeId: string) => {
    const { workflow } = get();
    if (!workflow) return;

    const updatedNodes = workflow.nodes.filter((node) => node.id !== nodeId);
    const updatedEdges = workflow.edges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId
    );

    set({
      workflow: {
        ...workflow,
        nodes: updatedNodes,
        edges: updatedEdges,
        updatedAt: new Date().toISOString(),
      },
      selectedNode: null,
    });
  },

  selectNode: (node: WorkflowNode | null) => {
    set({ selectedNode: node, selectedEdge: null });
  },

  // Edge Management
  onConnect: (connection: Connection) => {
    const { workflow, validateConnection } = get();
    if (!workflow || !validateConnection(connection)) return;

    const newEdge: WorkflowEdge = {
      id: uuidv4(),
      ...connection,
      animated: true,
    };

    set({
      workflow: {
        ...workflow,
        edges: addEdge(newEdge, workflow.edges),
        updatedAt: new Date().toISOString(),
      },
    });
  },

  deleteEdge: (edgeId: string) => {
    const { workflow } = get();
    if (!workflow) return;

    const updatedEdges = workflow.edges.filter((edge) => edge.id !== edgeId);

    set({
      workflow: {
        ...workflow,
        edges: updatedEdges,
        updatedAt: new Date().toISOString(),
      },
      selectedEdge: null,
    });
  },

  selectEdge: (edge: WorkflowEdge | null) => {
    set({ selectedEdge: edge, selectedNode: null });
  },

  // React Flow Events
  onNodesChange: (changes) => {
    const { workflow } = get();
    if (!workflow) return;

    set({
      workflow: {
        ...workflow,
        nodes: applyNodeChanges(changes, workflow.nodes),
        updatedAt: new Date().toISOString(),
      },
    });
  },

  onEdgesChange: (changes) => {
    const { workflow } = get();
    if (!workflow) return;

    set({
      workflow: {
        ...workflow,
        edges: applyEdgeChanges(changes, workflow.edges),
        updatedAt: new Date().toISOString(),
      },
    });
  },

  // Execution
  executeWorkflow: async (input: Record<string, any>) => {
    const { workflow } = get();
    if (!workflow) return;

    set({ isExecuting: true, executionResult: null });

    try {
      // TODO: Implement actual workflow execution
      await new Promise((resolve) => setTimeout(resolve, 2000));

      const mockResult: WorkflowExecution = {
        id: uuidv4(),
        workflowId: workflow.id,
        status: 'completed',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        duration: 2000,
        input,
        output: { result: 'Mock execution completed successfully' },
        logs: [
          {
            id: uuidv4(),
            timestamp: new Date().toISOString(),
            nodeId: workflow.nodes[0]?.id || '',
            level: 'info',
            message: 'Workflow execution started',
          },
        ],
      };

      set({ executionResult: mockResult, isExecuting: false });
    } catch (error) {
      const errorResult: WorkflowExecution = {
        id: uuidv4(),
        workflowId: workflow.id,
        status: 'failed',
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        input,
        error: error instanceof Error ? error.message : 'Unknown error',
        logs: [],
      };

      set({ executionResult: errorResult, isExecuting: false });
    }
  },

  stopExecution: () => {
    set({ isExecuting: false });
  },

  clearExecutionResult: () => {
    set({ executionResult: null });
  },

  // Templates
  loadNodeTemplates: () => {
    // Templates are already loaded in initial state
    // This could fetch from API in the future
  },

  // Validation
  validateWorkflow: () => {
    const { workflow } = get();
    if (!workflow) return { isValid: false, errors: ['No workflow loaded'] };

    const errors: string[] = [];

    if (workflow.nodes.length === 0) {
      errors.push('Workflow must have at least one node');
    }

    const inputNodes = workflow.nodes.filter((node) => node.type === 'input');
    const outputNodes = workflow.nodes.filter((node) => node.type === 'output');

    if (inputNodes.length === 0) {
      errors.push('Workflow must have at least one input node');
    }

    if (outputNodes.length === 0) {
      errors.push('Workflow must have at least one output node');
    }

    return { isValid: errors.length === 0, errors };
  },

  validateConnection: (connection: Connection) => {
    const { workflow } = get();
    if (!workflow) return false;

    // Prevent self-connections
    if (connection.source === connection.target) return false;

    // Check if connection already exists
    const existingConnection = workflow.edges.find(
      (edge) =>
        edge.source === connection.source && edge.target === connection.target
    );

    return !existingConnection;
  },

  // UI State
  setZoom: (zoom: number) => {
    set({ zoom });
  },

  setViewport: (viewport: { x: number; y: number }) => {
    set({ viewport });
  },

  // Reset
  resetWorkflow: () => {
    set({
      workflow: null,
      selectedNode: null,
      selectedEdge: null,
      isExecuting: false,
      executionResult: null,
      zoom: 1,
      viewport: { x: 0, y: 0 },
    });
  },
}));
