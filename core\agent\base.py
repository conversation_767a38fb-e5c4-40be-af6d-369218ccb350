"""Base agent classes and interfaces."""

from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class AgentStatus(Enum):
    """Agent execution status."""
    IDLE = "idle"
    PLANNING = "planning"
    EXECUTING = "executing"
    REFLECTING = "reflecting"
    ERROR = "error"
    COMPLETED = "completed"


@dataclass
class AgentResult:
    """Result of agent execution."""
    success: bool
    result: Any
    reasoning: str
    steps_taken: List[str]
    execution_time: float
    tokens_used: int
    cost_estimate: float
    metadata: Dict[str, Any]
    timestamp: datetime


@dataclass
class ExecutionContext:
    """Context for agent execution."""
    goal: str
    constraints: List[str]
    available_tools: List[str]
    max_steps: int
    timeout_seconds: float
    user_id: str
    session_id: str
    metadata: Dict[str, Any]


class AgentError(Exception):
    """Base exception for agent-related errors."""
    
    def __init__(self, message: str, agent_name: str = None, context: Dict[str, Any] = None):
        super().__init__(message)
        self.agent_name = agent_name
        self.context = context or {}


class PlanningError(AgentError):
    """Raised when agent planning fails."""
    pass


class ExecutionError(AgentError):
    """Raised when agent execution fails."""
    pass


class Agent(ABC):
    """Abstract base class for autonomous AI agents.
    
    Provides the core interface for agents that can plan, execute,
    and reflect on tasks using available tools and memory systems.
    
    The agent lifecycle:
    1. Planning: Generate a plan to achieve the goal
    2. Execution: Execute the plan step by step
    3. Reflection: Analyze results and learn from experience
    
    Example:
        >>> class MyAgent(Agent):
        ...     async def plan(self, goal: str, context: ExecutionContext) -> Plan:
        ...         # Generate plan to achieve goal
        ...         return Plan(steps=[...])
        ...     
        ...     async def execute_step(self, step: PlanStep, context: ExecutionContext) -> Any:
        ...         # Execute individual plan step
        ...         return result
    """
    
    def __init__(self, name: str, description: str = ""):
        """Initialize agent with name and description.
        
        Args:
            name: Unique agent name
            description: Human-readable description of agent capabilities
        """
        self.name = name
        self.description = description
        self.status = AgentStatus.IDLE
        self.execution_history: List[AgentResult] = []
        self._execution_lock = asyncio.Lock()
    
    @abstractmethod
    async def plan(self, goal: str, context: ExecutionContext) -> 'Plan':
        """Generate a plan to achieve the given goal.
        
        Args:
            goal: The objective to achieve
            context: Execution context with constraints and available resources
            
        Returns:
            Plan with steps to achieve the goal
            
        Raises:
            PlanningError: If planning fails
        """
        pass
    
    @abstractmethod
    async def execute_step(self, step: 'PlanStep', context: ExecutionContext) -> Any:
        """Execute a single step of the plan.
        
        Args:
            step: The plan step to execute
            context: Execution context
            
        Returns:
            Result of step execution
            
        Raises:
            ExecutionError: If step execution fails
        """
        pass
    
    async def execute(self, goal: str, context: ExecutionContext = None) -> AgentResult:
        """Execute a goal with full planning and execution cycle.
        
        Args:
            goal: The objective to achieve
            context: Optional execution context
            
        Returns:
            AgentResult with execution details and results
        """
        if context is None:
            context = ExecutionContext(
                goal=goal,
                constraints=[],
                available_tools=[],
                max_steps=10,
                timeout_seconds=300.0,
                user_id="unknown",
                session_id="unknown",
                metadata={}
            )
        
        async with self._execution_lock:
            start_time = datetime.now()
            steps_taken = []
            total_tokens = 0
            total_cost = 0.0
            
            try:
                # Planning phase
                self.status = AgentStatus.PLANNING
                logger.info(f"Agent {self.name} planning for goal: {goal}")
                
                plan = await self.plan(goal, context)
                steps_taken.append(f"Generated plan with {len(plan.steps)} steps")
                
                # Execution phase
                self.status = AgentStatus.EXECUTING
                logger.info(f"Agent {self.name} executing plan")
                
                results = []
                for i, step in enumerate(plan.steps):
                    if i >= context.max_steps:
                        logger.warning(f"Agent {self.name} reached max steps limit")
                        break
                    
                    step_start = datetime.now()
                    step_result = await self.execute_step(step, context)
                    step_duration = (datetime.now() - step_start).total_seconds()
                    
                    results.append(step_result)
                    steps_taken.append(f"Step {i+1}: {step.description} -> {step_result}")
                    
                    # Check timeout
                    elapsed = (datetime.now() - start_time).total_seconds()
                    if elapsed > context.timeout_seconds:
                        logger.warning(f"Agent {self.name} execution timeout")
                        break
                
                # Reflection phase
                self.status = AgentStatus.REFLECTING
                reflection = await self.reflect(goal, plan, results, context)
                steps_taken.append(f"Reflection: {reflection}")
                
                # Create result
                execution_time = (datetime.now() - start_time).total_seconds()
                
                result = AgentResult(
                    success=True,
                    result=results[-1] if results else None,
                    reasoning=plan.reasoning,
                    steps_taken=steps_taken,
                    execution_time=execution_time,
                    tokens_used=total_tokens,
                    cost_estimate=total_cost,
                    metadata={
                        "plan_steps": len(plan.steps),
                        "executed_steps": len(results),
                        "agent_name": self.name
                    },
                    timestamp=start_time
                )
                
                self.status = AgentStatus.COMPLETED
                self.execution_history.append(result)
                
                logger.info(f"Agent {self.name} completed goal in {execution_time:.2f}s")
                return result
                
            except Exception as e:
                self.status = AgentStatus.ERROR
                execution_time = (datetime.now() - start_time).total_seconds()
                
                error_result = AgentResult(
                    success=False,
                    result=None,
                    reasoning=f"Execution failed: {e}",
                    steps_taken=steps_taken,
                    execution_time=execution_time,
                    tokens_used=total_tokens,
                    cost_estimate=total_cost,
                    metadata={
                        "error": str(e),
                        "agent_name": self.name
                    },
                    timestamp=start_time
                )
                
                self.execution_history.append(error_result)
                logger.error(f"Agent {self.name} execution failed: {e}")
                
                return error_result
    
    async def reflect(self, goal: str, plan: 'Plan', results: List[Any], 
                     context: ExecutionContext) -> str:
        """Reflect on execution results and generate insights.
        
        Override to provide custom reflection logic for learning
        and improvement.
        
        Args:
            goal: The original goal
            plan: The executed plan
            results: Results from each step
            context: Execution context
            
        Returns:
            Reflection text with insights and learnings
        """
        success_rate = len([r for r in results if r is not None]) / len(plan.steps)
        return f"Executed {len(results)}/{len(plan.steps)} steps successfully ({success_rate:.1%})"
    
    def get_status(self) -> AgentStatus:
        """Get current agent status."""
        return self.status
    
    def get_execution_history(self) -> List[AgentResult]:
        """Get history of agent executions."""
        return self.execution_history.copy()
    
    def get_metrics(self) -> Dict[str, float]:
        """Get agent performance metrics."""
        if not self.execution_history:
            return {}
        
        successful_executions = [r for r in self.execution_history if r.success]
        
        return {
            "total_executions": len(self.execution_history),
            "successful_executions": len(successful_executions),
            "success_rate": len(successful_executions) / len(self.execution_history),
            "avg_execution_time": sum(r.execution_time for r in self.execution_history) / len(self.execution_history),
            "total_tokens_used": sum(r.tokens_used for r in self.execution_history),
            "total_cost": sum(r.cost_estimate for r in self.execution_history),
        }
