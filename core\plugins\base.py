"""Base plugin interfaces and data structures."""

from __future__ import annotations

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Any, Dict, List, Optional
from datetime import datetime


class PluginStatus(Enum):
    """Plugin lifecycle status."""
    UNLOADED = "unloaded"
    LOADING = "loading"
    LOADED = "loaded"
    INITIALIZING = "initializing"
    ACTIVE = "active"
    ERROR = "error"
    SHUTTING_DOWN = "shutting_down"
    SHUTDOWN = "shutdown"


class HealthStatus(Enum):
    """Plugin health status."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class PluginInfo:
    """Plugin metadata and information."""
    name: str
    version: str
    plugin_type: str
    description: str
    author: str
    dependencies: List[str]
    config_schema: Dict[str, Any]
    entry_point: str
    created_at: datetime
    status: PluginStatus = PluginStatus.UNLOADED
    health: HealthStatus = HealthStatus.UNKNOWN


@dataclass
class HealthCheckResult:
    """Result of a plugin health check."""
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: datetime
    response_time_ms: float


class Plugin(ABC):
    """Abstract base class for all M-GAIF plugins.
    
    Provides lifecycle management, health checking, and configuration
    for pluggable components in the M-GAIF framework.
    
    Lifecycle:
        1. __init__() - Plugin construction
        2. initialize() - Async initialization with config
        3. health_check() - Periodic health monitoring
        4. shutdown() - Graceful cleanup
    
    Example:
        >>> class MyTokenizer(Plugin):
        ...     async def initialize(self, config: Dict[str, Any]) -> None:
        ...         self.model = load_model(config["model_path"])
        ...     
        ...     async def health_check(self) -> HealthCheckResult:
        ...         return HealthCheckResult(
        ...             status=HealthStatus.HEALTHY,
        ...             message="Tokenizer operational",
        ...             details={"model_loaded": True},
        ...             timestamp=datetime.now(),
        ...             response_time_ms=1.2
        ...         )
    """
    
    def __init__(self, name: str, version: str = "1.0.0"):
        """Initialize plugin with basic metadata.
        
        Args:
            name: Unique plugin name
            version: Plugin version string
        """
        self.name = name
        self.version = version
        self.status = PluginStatus.UNLOADED
        self.config: Dict[str, Any] = {}
        self._initialization_lock = asyncio.Lock()
        self._shutdown_lock = asyncio.Lock()
    
    @abstractmethod
    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with configuration.
        
        Called once after plugin loading to set up resources,
        connections, and prepare for operation.
        
        Args:
            config: Plugin-specific configuration dictionary
            
        Raises:
            PluginInitializationError: If initialization fails
        """
        pass
    
    @abstractmethod
    async def health_check(self) -> HealthCheckResult:
        """Perform health check and return status.
        
        Called periodically to monitor plugin health and performance.
        Should be lightweight and non-blocking.
        
        Returns:
            HealthCheckResult with current health status
        """
        pass
    
    async def shutdown(self) -> None:
        """Gracefully shutdown the plugin.
        
        Called before plugin unloading to clean up resources,
        close connections, and save state if needed.
        
        Default implementation sets status to SHUTDOWN.
        Override for custom cleanup logic.
        """
        async with self._shutdown_lock:
            if self.status == PluginStatus.SHUTTING_DOWN:
                return
            
            self.status = PluginStatus.SHUTTING_DOWN
            # Perform any cleanup here
            self.status = PluginStatus.SHUTDOWN
    
    def get_info(self) -> PluginInfo:
        """Get plugin metadata and current status.
        
        Returns:
            PluginInfo with current plugin state
        """
        return PluginInfo(
            name=self.name,
            version=self.version,
            plugin_type=self.__class__.__name__,
            description=self.__doc__ or "No description available",
            author="Unknown",
            dependencies=[],
            config_schema={},
            entry_point=f"{self.__class__.__module__}:{self.__class__.__name__}",
            created_at=datetime.now(),
            status=self.status
        )
    
    def get_metrics(self) -> Dict[str, float]:
        """Get plugin-specific metrics.
        
        Override to provide custom metrics for monitoring
        and performance tracking.
        
        Returns:
            Dictionary of metric names to values
        """
        return {
            "status_code": float(hash(self.status.value) % 1000),
            "uptime_seconds": 0.0,
        }
    
    async def configure(self, config: Dict[str, Any]) -> None:
        """Update plugin configuration.
        
        Called to update plugin configuration at runtime.
        Default implementation stores config and reinitializes.
        
        Args:
            config: New configuration dictionary
        """
        async with self._initialization_lock:
            self.config = config
            if self.status == PluginStatus.ACTIVE:
                await self.initialize(config)


class PluginError(Exception):
    """Base exception for plugin-related errors."""
    
    def __init__(self, message: str, plugin_name: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.plugin_name = plugin_name
        self.details = details or {}


class PluginInitializationError(PluginError):
    """Raised when plugin initialization fails."""
    pass


class PluginLoadError(PluginError):
    """Raised when plugin loading fails."""
    pass


class PluginNotFoundError(PluginError):
    """Raised when requested plugin is not found."""
    pass
