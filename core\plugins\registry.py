"""Plugin registry for dynamic plugin management."""

from __future__ import annotations

import asyncio
import importlib
import inspect
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Type, Union

from .base import Plugin, PluginInfo, PluginStatus, PluginError, PluginLoadError, PluginNotFoundError
from .factory import PluginFactory

logger = logging.getLogger(__name__)


class PluginRegistry:
    """Central registry for plugin discovery, loading, and management.
    
    Provides dynamic plugin loading, hot-swapping, and lifecycle management
    for M-GAIF components. Supports both instance-based and factory-based
    plugin registration.
    
    Features:
    - Auto-discovery from plugin directories
    - Dynamic loading and unloading
    - Hot-swapping with graceful transitions
    - Health monitoring and metrics collection
    - Configuration management
    
    Example:
        >>> registry = PluginRegistry()
        >>> await registry.discover_plugins(Path("plugins/"))
        >>> tokenizer = await registry.get_plugin("tokenizer", "simple")
        >>> await registry.hot_swap("tokenizer", "simple", "huggingface", config)
    """
    
    def __init__(self):
        """Initialize empty plugin registry."""
        self._plugins: Dict[str, Dict[str, Plugin]] = {}  # type -> name -> plugin
        self._factories: Dict[str, Dict[str, PluginFactory]] = {}  # type -> name -> factory
        self._plugin_info: Dict[str, Dict[str, PluginInfo]] = {}  # type -> name -> info
        self._locks: Dict[str, asyncio.Lock] = {}  # type -> lock
        self._health_tasks: Dict[str, asyncio.Task] = {}  # plugin_key -> health_task
    
    def register_plugin(self, plugin_type: str, name: str, plugin: Plugin) -> None:
        """Register a plugin instance.
        
        Args:
            plugin_type: Type of plugin (e.g., "tokenizer", "embedder")
            name: Unique name within the plugin type
            plugin: Plugin instance to register
            
        Raises:
            ValueError: If plugin is already registered
        """
        if plugin_type not in self._plugins:
            self._plugins[plugin_type] = {}
            self._plugin_info[plugin_type] = {}
            self._locks[plugin_type] = asyncio.Lock()
        
        if name in self._plugins[plugin_type]:
            raise ValueError(f"Plugin {plugin_type}:{name} already registered")
        
        self._plugins[plugin_type][name] = plugin
        self._plugin_info[plugin_type][name] = plugin.get_info()
        
        logger.info(f"Registered plugin {plugin_type}:{name}")
    
    def register_factory(self, plugin_type: str, name: str, factory: PluginFactory) -> None:
        """Register a plugin factory for dynamic creation.
        
        Args:
            plugin_type: Type of plugin the factory creates
            name: Unique name within the plugin type
            factory: Factory instance for creating plugins
        """
        if plugin_type not in self._factories:
            self._factories[plugin_type] = {}
        
        self._factories[plugin_type][name] = factory
        logger.info(f"Registered factory {plugin_type}:{name}")
    
    async def get_plugin(self, plugin_type: str, name: str) -> Plugin:
        """Get a plugin instance, creating if needed.
        
        Args:
            plugin_type: Type of plugin to retrieve
            name: Name of plugin within the type
            
        Returns:
            Plugin instance
            
        Raises:
            PluginNotFoundError: If plugin not found and no factory available
        """
        # Check if plugin instance exists
        if (plugin_type in self._plugins and 
            name in self._plugins[plugin_type]):
            return self._plugins[plugin_type][name]
        
        # Try to create from factory
        if (plugin_type in self._factories and 
            name in self._factories[plugin_type]):
            factory = self._factories[plugin_type][name]
            plugin = await factory.create_plugin({})
            self.register_plugin(plugin_type, name, plugin)
            return plugin
        
        raise PluginNotFoundError(f"Plugin {plugin_type}:{name} not found")
    
    async def create_plugin(self, plugin_type: str, name: str, config: Dict[str, Any]) -> Plugin:
        """Create a new plugin instance from factory.
        
        Args:
            plugin_type: Type of plugin to create
            name: Name of plugin factory to use
            config: Configuration for plugin creation
            
        Returns:
            New plugin instance
            
        Raises:
            PluginNotFoundError: If factory not found
        """
        if (plugin_type not in self._factories or 
            name not in self._factories[plugin_type]):
            raise PluginNotFoundError(f"Factory {plugin_type}:{name} not found")
        
        factory = self._factories[plugin_type][name]
        plugin = await factory.create_plugin(config)
        
        # Register with unique name if not already registered
        instance_name = f"{name}_{id(plugin)}"
        self.register_plugin(plugin_type, instance_name, plugin)
        
        return plugin
    
    async def hot_swap(self, plugin_type: str, old_name: str, new_name: str, 
                      config: Dict[str, Any] = None) -> None:
        """Hot-swap a plugin with graceful transition.
        
        Args:
            plugin_type: Type of plugin to swap
            old_name: Name of current plugin
            new_name: Name of new plugin to swap in
            config: Configuration for new plugin
            
        Raises:
            PluginNotFoundError: If old or new plugin not found
        """
        async with self._locks.get(plugin_type, asyncio.Lock()):
            # Get current plugin
            if (plugin_type not in self._plugins or 
                old_name not in self._plugins[plugin_type]):
                raise PluginNotFoundError(f"Current plugin {plugin_type}:{old_name} not found")
            
            old_plugin = self._plugins[plugin_type][old_name]
            
            # Create or get new plugin
            if new_name in self._plugins[plugin_type]:
                new_plugin = self._plugins[plugin_type][new_name]
            else:
                new_plugin = await self.create_plugin(plugin_type, new_name, config or {})
            
            # Initialize new plugin if needed
            if new_plugin.status != PluginStatus.ACTIVE:
                await new_plugin.initialize(config or {})
                new_plugin.status = PluginStatus.ACTIVE
            
            # Graceful shutdown of old plugin
            await old_plugin.shutdown()
            
            # Atomic swap
            self._plugins[plugin_type][old_name] = new_plugin
            self._plugin_info[plugin_type][old_name] = new_plugin.get_info()
            
            logger.info(f"Hot-swapped {plugin_type}:{old_name} -> {new_name}")
    
    def list_plugins(self, plugin_type: str = None) -> Dict[str, List[str]]:
        """List all registered plugins.
        
        Args:
            plugin_type: Optional filter by plugin type
            
        Returns:
            Dictionary mapping plugin types to lists of plugin names
        """
        if plugin_type:
            return {plugin_type: list(self._plugins.get(plugin_type, {}).keys())}
        
        return {ptype: list(plugins.keys()) 
                for ptype, plugins in self._plugins.items()}
    
    def get_plugin_info(self, plugin_type: str, name: str) -> PluginInfo:
        """Get plugin metadata and status.
        
        Args:
            plugin_type: Type of plugin
            name: Name of plugin
            
        Returns:
            PluginInfo with current status
            
        Raises:
            PluginNotFoundError: If plugin not found
        """
        if (plugin_type not in self._plugin_info or 
            name not in self._plugin_info[plugin_type]):
            raise PluginNotFoundError(f"Plugin {plugin_type}:{name} not found")
        
        return self._plugin_info[plugin_type][name]
    
    async def discover_plugins(self, plugin_dir: Path) -> List[PluginInfo]:
        """Auto-discover plugins from directory.
        
        Scans the plugin directory for Python modules and attempts
        to load plugin classes that inherit from Plugin.
        
        Args:
            plugin_dir: Directory to scan for plugins
            
        Returns:
            List of discovered plugin information
        """
        discovered = []
        
        if not plugin_dir.exists():
            logger.warning(f"Plugin directory {plugin_dir} does not exist")
            return discovered
        
        # Scan for Python files
        for py_file in plugin_dir.rglob("*.py"):
            if py_file.name.startswith("__"):
                continue
            
            try:
                # Convert path to module name
                relative_path = py_file.relative_to(plugin_dir.parent)
                module_name = str(relative_path.with_suffix("")).replace("/", ".").replace("\\", ".")
                
                # Import module
                module = importlib.import_module(module_name)
                
                # Find Plugin classes
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (issubclass(obj, Plugin) and 
                        obj != Plugin and 
                        not inspect.isabstract(obj)):
                        
                        # Create plugin info
                        plugin_info = PluginInfo(
                            name=name.lower(),
                            version="1.0.0",
                            plugin_type=self._infer_plugin_type(module_name),
                            description=obj.__doc__ or "Auto-discovered plugin",
                            author="Unknown",
                            dependencies=[],
                            config_schema={},
                            entry_point=f"{module_name}:{name}",
                            created_at=datetime.now()
                        )
                        
                        discovered.append(plugin_info)
                        logger.info(f"Discovered plugin: {plugin_info.plugin_type}:{plugin_info.name}")
                        
            except Exception as e:
                logger.warning(f"Failed to load plugin from {py_file}: {e}")
        
        return discovered
    
    def _infer_plugin_type(self, module_name: str) -> str:
        """Infer plugin type from module name."""
        if "tokenizer" in module_name:
            return "tokenizer"
        elif "embedder" in module_name:
            return "embedder"
        elif "retriever" in module_name:
            return "retriever"
        elif "adapter" in module_name:
            return "adapter"
        else:
            return "unknown"
