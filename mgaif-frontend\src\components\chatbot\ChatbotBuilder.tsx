import React, { useState, useEffect } from 'react';
import { 
  Steps, 
  Button, 
  Space, 
  Typography, 
  Card, 
  Form, 
  message,
  Progress 
} from 'antd';
import { 
  LeftOutlined, 
  RightOutlined, 
  CheckOutlined,
  RocketOutlined 
} from '@ant-design/icons';
import { useChatbotStore } from '../../stores/chatbotStore';
import { BasicInfoStep } from './steps/BasicInfoStep';
import { PersonalityStep } from './steps/PersonalityStep';
import { KnowledgeBaseStep } from './steps/KnowledgeBaseStep';
import type { BuilderStep, ChatbotFormData } from '../../types/chatbot';

const { Step } = Steps;

const builderSteps = [
  {
    key: 'basic-info' as BuilderStep,
    title: 'Basic Info',
    description: 'Name and description',
    icon: '📝',
  },
  {
    key: 'personality' as BuilderStep,
    title: 'Personality',
    description: 'Tone and style',
    icon: '🎭',
  },
  {
    key: 'knowledge-base' as BuilderStep,
    title: 'Knowledge Base',
    description: 'Upload documents',
    icon: '📚',
  },
  {
    key: 'llm-settings' as BuilderStep,
    title: 'AI Settings',
    description: 'Model configuration',
    icon: '🤖',
  },
  {
    key: 'conversation-settings' as BuilderStep,
    title: 'Conversation',
    description: 'Chat behavior',
    icon: '💬',
  },
  {
    key: 'testing' as BuilderStep,
    title: 'Testing',
    description: 'Test your bot',
    icon: '🧪',
  },
  {
    key: 'deployment' as BuilderStep,
    title: 'Deploy',
    description: 'Go live',
    icon: '🚀',
  },
];

interface ChatbotBuilderProps {
  chatbotId?: string;
  onComplete?: (chatbotId: string) => void;
}

export const ChatbotBuilder: React.FC<ChatbotBuilderProps> = ({
  chatbotId,
  onComplete,
}) => {
  const {
    currentChatbot,
    currentStep,
    isLoading,
    error,
    createChatbot,
    updateChatbot,
    loadChatbot,
    setCurrentStep,
    nextStep,
    previousStep,
    clearError,
  } = useChatbotStore();

  const [form] = Form.useForm();
  const [formData, setFormData] = useState<Partial<ChatbotFormData>>({});

  const currentStepIndex = builderSteps.findIndex(step => step.key === currentStep);
  const isFirstStep = currentStepIndex === 0;
  const isLastStep = currentStepIndex === builderSteps.length - 1;

  useEffect(() => {
    if (chatbotId && !currentChatbot) {
      loadChatbot(chatbotId);
    }
  }, [chatbotId, currentChatbot, loadChatbot]);

  useEffect(() => {
    if (currentChatbot) {
      const initialData = {
        name: currentChatbot.name,
        description: currentChatbot.description,
        personality: currentChatbot.personality,
        llmSettings: currentChatbot.llmSettings,
        conversationSettings: currentChatbot.conversationSettings,
      };
      setFormData(initialData);
      form.setFieldsValue(initialData);
    }
  }, [currentChatbot, form]);

  const handleNext = async () => {
    try {
      // Validate current step
      if (currentStep === 'basic-info' || currentStep === 'personality') {
        await form.validateFields();
      }

      // Save progress
      if (currentChatbot) {
        await updateChatbot(currentChatbot.id, formData);
      } else if (currentStep === 'basic-info') {
        // Create new chatbot on first step completion
        const newChatbot = await createChatbot(formData as ChatbotFormData);
        message.success('Chatbot created successfully!');
      }

      nextStep();
      clearError();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  const handlePrevious = () => {
    previousStep();
    clearError();
  };

  const handleStepClick = (stepIndex: number) => {
    if (stepIndex <= currentStepIndex) {
      setCurrentStep(builderSteps[stepIndex].key);
    }
  };

  const handleFormChange = (changedValues: any, allValues: any) => {
    setFormData(prev => ({ ...prev, ...changedValues }));
  };

  const handleComplete = () => {
    if (currentChatbot) {
      message.success('Chatbot setup completed!');
      onComplete?.(currentChatbot.id);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'basic-info':
        return (
          <BasicInfoStep
            form={form}
            initialValues={formData}
            onValuesChange={handleFormChange}
          />
        );
      
      case 'personality':
        return (
          <PersonalityStep
            form={form}
            initialValues={formData.personality}
            onValuesChange={(values) => handleFormChange({ personality: values }, formData)}
          />
        );
      
      case 'knowledge-base':
        return (
          <KnowledgeBaseStep chatbotId={currentChatbot?.id} />
        );
      
      case 'llm-settings':
        return (
          <Card title="AI Model Settings" style={{ maxWidth: 600, margin: '0 auto' }}>
            <Typography.Paragraph type="secondary">
              Configure the AI model settings for your chatbot.
            </Typography.Paragraph>
            <Typography.Text type="secondary">
              This step is coming soon. Default settings will be used for now.
            </Typography.Text>
          </Card>
        );
      
      case 'conversation-settings':
        return (
          <Card title="Conversation Settings" style={{ maxWidth: 600, margin: '0 auto' }}>
            <Typography.Paragraph type="secondary">
              Configure how conversations are handled.
            </Typography.Paragraph>
            <Typography.Text type="secondary">
              This step is coming soon. Default settings will be used for now.
            </Typography.Text>
          </Card>
        );
      
      case 'testing':
        return (
          <Card title="Test Your Chatbot" style={{ maxWidth: 600, margin: '0 auto' }}>
            <Typography.Paragraph type="secondary">
              Test your chatbot before deploying it.
            </Typography.Paragraph>
            <Typography.Text type="secondary">
              Testing interface is coming soon.
            </Typography.Text>
          </Card>
        );
      
      case 'deployment':
        return (
          <Card title="Deploy Your Chatbot" style={{ maxWidth: 600, margin: '0 auto' }}>
            <div style={{ textAlign: 'center', padding: '40px 20px' }}>
              <RocketOutlined style={{ fontSize: '64px', color: '#1890ff', marginBottom: '24px' }} />
              <Typography.Title level={3}>Ready to Deploy!</Typography.Title>
              <Typography.Paragraph>
                Your chatbot is configured and ready to go live. 
                Click the button below to complete the setup.
              </Typography.Paragraph>
              <Button 
                type="primary" 
                size="large" 
                icon={<CheckOutlined />}
                onClick={handleComplete}
              >
                Complete Setup
              </Button>
            </div>
          </Card>
        );
      
      default:
        return null;
    }
  };

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '32px', textAlign: 'center' }}>
        <Typography.Title level={2}>
          {currentChatbot ? `Edit ${currentChatbot.name}` : 'Create New Chatbot'}
        </Typography.Title>
        <Typography.Paragraph type="secondary">
          Follow the steps below to configure your AI chatbot
        </Typography.Paragraph>
      </div>

      {/* Progress */}
      <Card style={{ marginBottom: '24px' }}>
        <div style={{ marginBottom: '16px' }}>
          <Typography.Text strong>Progress: </Typography.Text>
          <Typography.Text>
            Step {currentStepIndex + 1} of {builderSteps.length}
          </Typography.Text>
        </div>
        <Progress 
          percent={((currentStepIndex + 1) / builderSteps.length) * 100} 
          strokeColor="#1890ff"
          showInfo={false}
        />
      </Card>

      {/* Steps */}
      <Card style={{ marginBottom: '24px' }}>
        <Steps 
          current={currentStepIndex} 
          onChange={handleStepClick}
          type="navigation"
          size="small"
        >
          {builderSteps.map((step, index) => (
            <Step
              key={step.key}
              title={
                <Space>
                  <span>{step.icon}</span>
                  <span>{step.title}</span>
                </Space>
              }
              description={step.description}
              disabled={index > currentStepIndex}
            />
          ))}
        </Steps>
      </Card>

      {/* Step Content */}
      <div style={{ marginBottom: '24px' }}>
        {renderStepContent()}
      </div>

      {/* Navigation */}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Button
            icon={<LeftOutlined />}
            onClick={handlePrevious}
            disabled={isFirstStep}
          >
            Previous
          </Button>

          <Typography.Text type="secondary">
            {builderSteps[currentStepIndex]?.title}
          </Typography.Text>

          {!isLastStep ? (
            <Button
              type="primary"
              icon={<RightOutlined />}
              iconPosition="end"
              onClick={handleNext}
              loading={isLoading}
            >
              Next
            </Button>
          ) : (
            <Button
              type="primary"
              icon={<CheckOutlined />}
              onClick={handleComplete}
              loading={isLoading}
            >
              Complete
            </Button>
          )}
        </div>
      </Card>

      {/* Error Display */}
      {error && (
        <Card style={{ marginTop: '16px', borderColor: '#ff4d4f' }}>
          <Typography.Text type="danger">{error}</Typography.Text>
        </Card>
      )}
    </div>
  );
};
