services:
  edge:
    build: ..
    image: mgaif/edge:latest
    environment:
      - LOG_LEVEL=info
      - MGAIF_EDGE_ADAPTER=echo
      - OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
    ports:
      - "8000:8000"
    depends_on:
      - otel-collector
    networks:
      - observability

  otel-collector:
    image: otel/opentelemetry-collector-contrib:0.108.0
    command: ["--config=/etc/otel-collector.yaml"]
    volumes:
      - ./otel-collector.yaml:/etc/otel-collector.yaml:ro
    ports:
      - "4317:4317"
      - "4318:4318"
    networks:
      - observability

  prometheus:
    image: prom/prometheus:v2.54.1
    volumes:
      - ./prometheus:/etc/prometheus:ro
    ports:
      - "9090:9090"
    networks:
      - observability

networks:
  observability: {}
