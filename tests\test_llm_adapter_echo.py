import asyncio

import pytest

from core.adapters.openai_adapter import Echo<PERSON>dapter
from tests.realistic_data import get_chat_messages
from core.contracts.llm import ChatCompletionRequest, ChatMessage


@pytest.mark.asyncio
async def test_echo_adapter_chat_and_stream(async_bench):
    adapter = EchoAdapter()

    # Non-streaming with realistic message
    realistic_messages = get_chat_messages("tech_support_001")
    realistic_content = realistic_messages[0]["content"]
    req = ChatCompletionRequest(messages=[ChatMessage(role="user", content=realistic_content)])
    resp = await adapter.chat(req)
    assert resp.choices[0].message.content == realistic_content
    # Validate usage fields
    assert resp.usage is not None
    assert resp.usage.total_tokens == resp.usage.prompt_tokens + resp.usage.completion_tokens
    assert resp.usage.prompt_tokens >= 1
    assert resp.usage.completion_tokens >= 1

    # Streaming with realistic message
    realistic_stream_messages = get_chat_messages("learning_ai_002")
    stream_content = realistic_stream_messages[0]["content"]
    req_s = ChatCompletionRequest(messages=[ChatMessage(role="user", content=stream_content)], stream=True)

    async def _collect():
        parts = []
        async for chunk in adapter.chat_stream(req_s):
            for ch in chunk.choices:
                if ch.delta.content:
                    parts.append(ch.delta.content)
        return "".join(parts).strip()

    streamed = await _collect()
    assert streamed == stream_content

    # Bench chat latency (async)
    metrics = await async_bench(lambda: adapter.chat(req), iterations=50)
    assert metrics["p95_ms"] < 5.0
    # Rough tokens/sec calculation using last response usage
    total_tokens_per_call = resp.usage.total_tokens
    tokens_per_sec = metrics["qps"] * total_tokens_per_call
    assert tokens_per_sec > 10  # sanity lower bound
