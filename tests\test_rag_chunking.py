"""Tests for RAG chunking strategies.

This module tests the various document chunking strategies including
FixedSizeChunker, SemanticChunker, and RecursiveChunker.
"""

import pytest
from core.rag.base import Document, DocumentType
from core.rag.chunking import (
    ChunkingStrategy, FixedSizeChunker, SemanticChunker, RecursiveChunker
)


class TestFixedSizeChunker:
    """Test FixedSizeChunker implementation."""
    
    def test_fixed_size_chunker_creation(self):
        """Test basic chunker creation."""
        chunker = FixedSizeChunker(chunk_size=100, overlap=20)
        
        assert chunker.chunk_size == 100
        assert chunker.overlap == 20
        assert chunker.min_chunk_size == 50  # Default
    
    def test_fixed_size_chunker_simple_text(self):
        """Test chunking simple text."""
        chunker = FixedSizeChunker(chunk_size=50, overlap=10)
        document = Document(
            content="This is a simple test document that should be split into multiple chunks based on the fixed size configuration.",
            title="Test Document"
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) > 1
        for chunk in chunks:
            assert len(chunk.content) <= 50
            assert chunk.document_id == document.id
            assert chunk.document_title == "Test Document"
    
    def test_fixed_size_chunker_with_overlap(self):
        """Test chunking with overlap."""
        chunker = FixedSizeChunker(chunk_size=30, overlap=10)
        document = Document(
            content="This is a test document for checking overlap functionality in chunking."
        )
        
        chunks = chunker.chunk_document(document)
        
        # Verify overlap exists between consecutive chunks
        if len(chunks) > 1:
            # Check that there's some overlap between chunks
            chunk1_end = chunks[0].content[-10:]  # Last 10 chars of first chunk
            chunk2_start = chunks[1].content[:10]  # First 10 chars of second chunk
            
            # There should be some common content (allowing for word boundaries)
            assert len(chunks) >= 2
    
    def test_fixed_size_chunker_short_document(self):
        """Test chunking document shorter than chunk size."""
        chunker = FixedSizeChunker(chunk_size=100, overlap=20)
        document = Document(content="Short document.")
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) == 1
        assert chunks[0].content == "Short document."
        assert chunks[0].start_char == 0
        assert chunks[0].end_char == 15
    
    def test_fixed_size_chunker_empty_document(self):
        """Test chunking empty document."""
        chunker = FixedSizeChunker(chunk_size=100, overlap=20)
        document = Document(content="")
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) == 0
    
    def test_fixed_size_chunker_word_boundaries(self):
        """Test that chunker respects word boundaries."""
        chunker = FixedSizeChunker(chunk_size=20, overlap=5)
        document = Document(content="This is a test document with multiple words that should be split properly.")
        
        chunks = chunker.chunk_document(document)
        
        # Verify no chunks end in the middle of a word (except possibly the last one)
        for i, chunk in enumerate(chunks[:-1]):  # All but last chunk
            # Should not end with a partial word (no letter followed by space at start of next chunk)
            if i + 1 < len(chunks):
                next_chunk = chunks[i + 1]
                # This is a heuristic check - in practice, word boundary detection is more complex
                assert len(chunk.content.strip()) > 0


class TestSemanticChunker:
    """Test SemanticChunker implementation."""
    
    def test_semantic_chunker_creation(self):
        """Test basic semantic chunker creation."""
        chunker = SemanticChunker(chunk_size=200, min_chunk_size=50)
        
        assert chunker.chunk_size == 200
        assert chunker.min_chunk_size == 50
        assert chunker.overlap == 0  # Default
    
    def test_semantic_chunker_sentence_boundaries(self):
        """Test chunking at sentence boundaries."""
        chunker = SemanticChunker(chunk_size=100, min_chunk_size=20)
        document = Document(
            content="This is the first sentence. This is the second sentence. This is the third sentence. This is the fourth sentence."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) > 1
        
        # Verify chunks end at sentence boundaries when possible
        for chunk in chunks:
            content = chunk.content.strip()
            if content and not content.endswith('.'):
                # If it doesn't end with a period, it might be the last chunk or split due to size
                pass
            assert len(content) > 0
    
    def test_semantic_chunker_paragraph_boundaries(self):
        """Test chunking at paragraph boundaries."""
        chunker = SemanticChunker(chunk_size=150, min_chunk_size=30)
        document = Document(
            content="This is the first paragraph. It has multiple sentences.\n\nThis is the second paragraph. It also has content.\n\nThis is the third paragraph with more text."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) >= 1
        
        # Verify chunks preserve paragraph structure when possible
        for chunk in chunks:
            assert len(chunk.content.strip()) > 0
    
    def test_semantic_chunker_section_headers(self):
        """Test chunking with section headers."""
        chunker = SemanticChunker(chunk_size=200, min_chunk_size=40)
        document = Document(
            content="# Introduction\nThis is the introduction section with some content.\n\n## Background\nThis is the background section with more detailed information.\n\n## Methods\nThis section describes the methods used."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) >= 1
        
        # Verify chunks respect section boundaries
        for chunk in chunks:
            assert len(chunk.content.strip()) > 0
    
    def test_semantic_chunker_fallback_to_fixed(self):
        """Test fallback to fixed-size chunking when semantic boundaries are too large."""
        chunker = SemanticChunker(chunk_size=50, min_chunk_size=10)
        document = Document(
            content="This is a very long sentence that exceeds the chunk size limit and should be split even though it breaks semantic boundaries because we need to respect the maximum chunk size constraint."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) > 1
        
        # Verify no chunk exceeds the maximum size
        for chunk in chunks:
            assert len(chunk.content) <= chunker.chunk_size


class TestRecursiveChunker:
    """Test RecursiveChunker implementation."""
    
    def test_recursive_chunker_creation(self):
        """Test basic recursive chunker creation."""
        chunker = RecursiveChunker(
            chunk_size=200,
            overlap=20,
            separators=["\n\n", ". ", "\n", " "]
        )
        
        assert chunker.chunk_size == 200
        assert chunker.overlap == 20
        assert chunker.separators == ["\n\n", ". ", "\n", " "]
    
    def test_recursive_chunker_default_separators(self):
        """Test recursive chunker with default separators."""
        chunker = RecursiveChunker(chunk_size=100)
        
        # Should have default separators
        assert len(chunker.separators) > 0
        assert "\n\n" in chunker.separators  # Paragraph separator
        assert ". " in chunker.separators    # Sentence separator
    
    def test_recursive_chunker_hierarchy(self):
        """Test chunking with separator hierarchy."""
        chunker = RecursiveChunker(
            chunk_size=80,
            separators=["\n\n", ". ", " "]
        )
        document = Document(
            content="First paragraph with content.\n\nSecond paragraph with more content. It has multiple sentences. Each sentence provides information.\n\nThird paragraph conclusion."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) >= 1
        
        # Verify chunks respect the hierarchy
        for chunk in chunks:
            assert len(chunk.content) <= chunker.chunk_size
            assert len(chunk.content.strip()) > 0
    
    def test_recursive_chunker_section_headers(self):
        """Test recursive chunking with section headers."""
        chunker = RecursiveChunker(
            chunk_size=150,
            separators=["##", "\n\n", ". ", " "]
        )
        document = Document(
            content="## Introduction\nThis is the introduction with some content.\n\n## Methods\nThis section describes methods. It has detailed information. Multiple sentences explain the approach.\n\n## Results\nResults are presented here."
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) >= 1
        
        # Verify section structure is preserved when possible
        for chunk in chunks:
            assert len(chunk.content.strip()) > 0
    
    def test_recursive_chunker_fallback_chain(self):
        """Test fallback through separator chain."""
        chunker = RecursiveChunker(
            chunk_size=30,
            separators=["\n\n", ". ", " "]
        )
        document = Document(
            content="This_is_a_very_long_word_without_spaces_that_should_force_character_level_splitting_when_no_other_separators_work"
        )
        
        chunks = chunker.chunk_document(document)
        
        assert len(chunks) > 1
        
        # Should eventually split at character level
        for chunk in chunks:
            assert len(chunk.content) <= chunker.chunk_size
    
    def test_recursive_chunker_with_overlap(self):
        """Test recursive chunking with overlap."""
        chunker = RecursiveChunker(
            chunk_size=50,
            overlap=10,
            separators=[". ", " "]
        )
        document = Document(
            content="First sentence here. Second sentence follows. Third sentence continues. Fourth sentence ends the sequence."
        )
        
        chunks = chunker.chunk_document(document)
        
        if len(chunks) > 1:
            # Verify overlap exists
            assert len(chunks) >= 2
            # Basic check that chunks have reasonable content
            for chunk in chunks:
                assert len(chunk.content.strip()) > 0


class TestChunkingIntegration:
    """Test integration scenarios for chunking strategies."""
    
    @pytest.mark.parametrize("chunker_class,kwargs", [
        (FixedSizeChunker, {"chunk_size": 100, "overlap": 20}),
        (SemanticChunker, {"chunk_size": 100, "min_chunk_size": 20}),
        (RecursiveChunker, {"chunk_size": 100, "overlap": 10})
    ])
    def test_chunker_consistency(self, chunker_class, kwargs):
        """Test that all chunkers produce consistent results."""
        chunker = chunker_class(**kwargs)
        document = Document(
            content="This is a test document. It has multiple sentences. Each sentence provides different information. The document should be chunked consistently.",
            title="Consistency Test"
        )
        
        chunks = chunker.chunk_document(document)
        
        # Basic consistency checks
        assert len(chunks) > 0
        
        # Verify chunk properties
        for i, chunk in enumerate(chunks):
            assert chunk.document_id == document.id
            assert chunk.document_title == "Consistency Test"
            assert len(chunk.content) > 0
            assert chunk.start_char >= 0
            assert chunk.end_char > chunk.start_char
            
            # Verify chunk ordering
            if i > 0:
                assert chunk.start_char >= chunks[i-1].start_char
    
    def test_chunking_preserves_content(self):
        """Test that chunking preserves all document content."""
        chunker = FixedSizeChunker(chunk_size=50, overlap=0)
        original_content = "This is a comprehensive test document that will be split into multiple chunks to verify that no content is lost during the chunking process."
        document = Document(content=original_content)
        
        chunks = chunker.chunk_document(document)
        
        # Reconstruct content from chunks
        reconstructed = ""
        for chunk in chunks:
            reconstructed += chunk.content
        
        # Should preserve all content (allowing for minor whitespace differences)
        assert len(reconstructed) >= len(original_content) * 0.9  # Allow some tolerance
    
    def test_chunking_metadata_preservation(self):
        """Test that chunking preserves document metadata."""
        chunker = SemanticChunker(chunk_size=100)
        document = Document(
            content="This document has metadata. It should be preserved in chunks. The metadata provides important context.",
            title="Metadata Test",
            metadata={"author": "Test Author", "category": "test", "priority": "high"}
        )
        
        chunks = chunker.chunk_document(document)
        
        for chunk in chunks:
            assert chunk.document_id == document.id
            assert chunk.document_title == "Metadata Test"
            # Chunk metadata should include document metadata
            assert "document_metadata" in chunk.metadata
            assert chunk.metadata["document_metadata"]["author"] == "Test Author"
            assert chunk.metadata["document_metadata"]["category"] == "test"


@pytest.mark.asyncio
async def test_chunking_performance():
    """Test chunking performance with larger documents."""
    # Create a larger document
    content = "This is a performance test sentence. " * 1000  # ~37,000 characters
    document = Document(content=content, title="Performance Test")
    
    chunkers = [
        FixedSizeChunker(chunk_size=500, overlap=50),
        SemanticChunker(chunk_size=500, min_chunk_size=100),
        RecursiveChunker(chunk_size=500, overlap=50)
    ]
    
    for chunker in chunkers:
        chunks = chunker.chunk_document(document)
        
        # Verify reasonable performance
        assert len(chunks) > 0
        assert len(chunks) < 200  # Should not create excessive chunks
        
        # Verify chunk sizes are reasonable
        for chunk in chunks:
            assert len(chunk.content) <= chunker.chunk_size
            assert len(chunk.content) > 0
