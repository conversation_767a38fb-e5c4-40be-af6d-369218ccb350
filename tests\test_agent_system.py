"""Tests for the agent system."""

import pytest
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any
from unittest.mock import AsyncMock, MagicMock

from core.agent.base import Agent, AgentResult, ExecutionContext
from core.agent.memory import EpisodicMemory, SemanticMemory, MemoryItem
from core.agent.planning import <PERSON><PERSON><PERSON><PERSON>, ReActPlanner, Plan, PlanStep, PlanStatus
from core.agent.tools import <PERSON><PERSON>, ToolResult, ToolOrchestrator, EchoTool, CalculatorTool
from core.agent.coordination import AgentCoordinator, AgentTask, CoordinationStrategy, MultiAgentResult
from core.adapters.openai_adapter import EchoAdapter


class MockTool(Tool):
    """Mock tool for testing."""
    
    def __init__(self, name: str = "mock_tool", should_fail: bool = False):
        super().__init__(name, f"Mock tool: {name}")
        self.should_fail = should_fail
        self.call_count = 0
    
    async def execute(self, **kwargs) -> ToolResult:
        """Execute mock tool."""
        self.call_count += 1
        
        if self.should_fail:
            return ToolResult(
                success=False,
                result=None,
                error_message="Mock tool failure",
                execution_time=0.1,
                metadata={"call_count": self.call_count},
                timestamp=datetime.now()
            )
        
        return ToolResult(
            success=True,
            result=f"Mock result from {self.name}: {kwargs}",
            error_message=None,
            execution_time=0.1,
            metadata={"call_count": self.call_count},
            timestamp=datetime.now()
        )


class MockAgent(Agent):
    """Mock agent for testing."""
    
    def __init__(self, name: str = "mock_agent", should_fail: bool = False):
        super().__init__()
        self.name = name
        self.should_fail = should_fail
        self.execution_count = 0
    
    async def execute(self, goal: str, context: ExecutionContext) -> AgentResult:
        """Execute mock agent."""
        self.execution_count += 1
        
        if self.should_fail:
            return AgentResult(
                success=False,
                result=None,
                reasoning="Mock agent failure",
                steps_taken=[f"Failed step for goal: {goal}"],
                execution_time=0.5,
                tokens_used=0,
                cost_estimate=0.0,
                metadata={"execution_count": self.execution_count},
                timestamp=datetime.now()
            )
        
        return AgentResult(
            success=True,
            result=f"Mock result for goal: {goal}",
            reasoning=f"Mock reasoning for {goal}",
            steps_taken=[f"Step 1: Analyzed goal", f"Step 2: Generated result"],
            execution_time=0.5,
            tokens_used=100,
            cost_estimate=0.001,
            metadata={"execution_count": self.execution_count},
            timestamp=datetime.now()
        )

    async def plan(self, goal: str, context: ExecutionContext) -> 'Plan':
        """Generate a mock plan."""
        from core.agent.planning import Plan, PlanStep

        steps = [
            PlanStep(
                id="step1",
                description=f"Mock step 1 for {goal}",
                action="mock_action",
                parameters={"goal": goal},
                dependencies=[],
                estimated_duration=1.0
            ),
            PlanStep(
                id="step2",
                description=f"Mock step 2 for {goal}",
                action="mock_action",
                parameters={"goal": goal},
                dependencies=["step1"],
                estimated_duration=1.0
            )
        ]

        return Plan(
            id=f"plan_{goal}",
            goal=goal,
            steps=steps,
            estimated_duration=2.0,
            success_criteria=[f"Goal {goal} achieved"]
        )

    async def execute_step(self, step: 'PlanStep', context: ExecutionContext) -> Any:
        """Execute a mock step."""
        if self.should_fail:
            raise Exception(f"Mock execution failure for step {step.id}")

        return f"Mock result for step {step.id}"


class TestEpisodicMemory:
    """Test episodic memory functionality."""
    
    @pytest.fixture
    def memory(self):
        """Create episodic memory for testing."""
        return EpisodicMemory(max_memories=100)
    
    def test_store_memory(self, memory):
        """Test storing memories."""
        memory.store_sync("test_event", "This is a test memory", importance=0.8)

        assert len(memory.memories) == 1
        memory_item = memory.memories[0]
        assert memory_item.content == "This is a test memory"
        assert memory_item.importance == 0.8
        assert memory_item.memory_type == "episodic"  # Fixed: should be "episodic" not "test_event"
    
    def test_retrieve_memories(self, memory):
        """Test retrieving memories."""
        # Store multiple memories
        memory.store_sync("event1", "First memory", importance=0.9)
        memory.store_sync("event2", "Second memory", importance=0.7)
        memory.store_sync("event1", "Third memory", importance=0.8)

        # Retrieve by semantic similarity
        event1_memories = memory.retrieve_sync("event1")
        assert len(event1_memories) >= 1  # Should find at least one relevant memory

        # Retrieve by importance threshold
        important_memories = memory.retrieve_by_importance(0.8)
        assert len(important_memories) == 2
    
    def test_memory_decay(self, memory):
        """Test memory importance decay."""
        # Store memory with high importance
        memory.store_sync("test", "Decaying memory", importance=1.0)
        original_importance = memory.memories[0].importance

        # Apply decay
        memory.decay_memories(decay_rate=0.1)

        # Check importance decreased
        assert memory.memories[0].importance < original_importance
    
    def test_memory_consolidation(self, memory):
        """Test memory consolidation."""
        # Fill memory beyond capacity
        for i in range(150):
            memory.store_sync(f"event_{i}", f"Memory {i}", importance=0.5 + (i % 10) * 0.05)

        # Consolidate
        memory.consolidate()

        # Check memory count is within limit
        assert len(memory.memories) <= memory.max_memories
        
        # Check most important memories are retained
        importances = [m.importance for m in memory.memories]
        assert max(importances) > 0.9  # High importance memories retained


class TestSemanticMemory:
    """Test semantic memory functionality."""
    
    @pytest.fixture
    def memory(self):
        """Create semantic memory for testing."""
        return SemanticMemory(embedding_dim=8)
    
    def test_store_fact(self, memory):
        """Test storing facts."""
        memory.store_fact("AI", "Artificial Intelligence is machine intelligence")
        
        assert "AI" in memory.facts
        assert len(memory.embeddings) == 1
    
    def test_retrieve_fact(self, memory):
        """Test retrieving facts."""
        memory.store_fact("AI", "Artificial Intelligence is machine intelligence")
        memory.store_fact("ML", "Machine Learning is a subset of AI")
        
        # Retrieve specific fact
        ai_fact = memory.retrieve_fact("AI")
        assert ai_fact == "Artificial Intelligence is machine intelligence"
        
        # Retrieve similar facts
        similar = memory.retrieve_similar("artificial intelligence", top_k=2)
        assert len(similar) <= 2
    
    def test_update_fact(self, memory):
        """Test updating facts."""
        memory.store_fact("AI", "Old definition")
        memory.update_fact("AI", "New definition")
        
        assert memory.retrieve_fact("AI") == "New definition"
    
    def test_remove_fact(self, memory):
        """Test removing facts."""
        memory.store_fact("AI", "Artificial Intelligence")
        memory.remove_fact("AI")
        
        assert "AI" not in memory.facts
        assert len(memory.embeddings) == 0


class TestPlanning:
    """Test planning algorithms."""
    
    @pytest.fixture
    def llm_adapter(self):
        """Create LLM adapter for testing."""
        return EchoAdapter()
    
    @pytest.mark.asyncio
    async def test_cot_planner(self, llm_adapter):
        """Test Chain of Thought planner."""
        planner = CoTPlanner(llm_adapter)
        
        plan = await planner.create_plan(
            goal="Test goal",
            available_tools=["search", "calculate"],
            constraints=["max_steps: 5"]
        )
        
        assert isinstance(plan, Plan)
        assert plan.goal == "Test goal"
        assert len(plan.steps) > 0
        assert plan.confidence > 0
    
    @pytest.mark.asyncio
    async def test_react_planner(self, llm_adapter):
        """Test ReAct planner."""
        planner = ReActPlanner(llm_adapter)
        
        plan = await planner.create_plan(
            goal="Research and analyze",
            available_tools=["search", "analyze"],
            constraints=["timeout: 300"]
        )
        
        assert isinstance(plan, Plan)
        assert plan.goal == "Research and analyze"
        assert len(plan.steps) > 0
        assert "ReActPlanner" in plan.metadata["planner"]
    
    def test_plan_validation(self, llm_adapter):
        """Test plan validation."""
        planner = CoTPlanner(llm_adapter)
        
        # Create plan with circular dependency
        steps = [
            PlanStep("step1", "First step", "action1", {}, ["step2"], "output1", 60, 3),
            PlanStep("step2", "Second step", "action2", {}, ["step1"], "output2", 60, 3)
        ]
        
        plan = Plan("test_plan", "Test goal", "Test reasoning", steps, 120, 0.8, {})
        
        issues = planner.validate_plan(plan)
        assert len(issues) > 0  # Should detect circular dependency


class TestToolOrchestrator:
    """Test tool orchestration."""
    
    @pytest.fixture
    def orchestrator(self):
        """Create tool orchestrator for testing."""
        return ToolOrchestrator()
    
    @pytest.mark.asyncio
    async def test_tool_registration(self, orchestrator):
        """Test tool registration."""
        tool = MockTool("test_tool")
        orchestrator.register_tool(tool)
        
        assert "test_tool" in orchestrator.get_available_tools()
        schemas = orchestrator.get_tool_schemas()
        assert len(schemas) == 1
    
    @pytest.mark.asyncio
    async def test_tool_execution(self, orchestrator):
        """Test tool execution."""
        tool = MockTool("test_tool")
        orchestrator.register_tool(tool)
        
        result = await orchestrator.execute_tool("test_tool", param1="value1")
        
        assert result.success
        assert "Mock result" in result.result
        assert tool.call_count == 1
    
    @pytest.mark.asyncio
    async def test_parallel_tool_execution(self, orchestrator):
        """Test parallel tool execution."""
        tool1 = MockTool("tool1")
        tool2 = MockTool("tool2")
        
        orchestrator.register_tool(tool1)
        orchestrator.register_tool(tool2)
        
        tool_calls = [
            {"name": "tool1", "parameters": {"param": "value1"}},
            {"name": "tool2", "parameters": {"param": "value2"}}
        ]
        
        results = await orchestrator.execute_tools_parallel(tool_calls)
        
        assert len(results) == 2
        assert all(result.success for result in results)
        assert tool1.call_count == 1
        assert tool2.call_count == 1
    
    @pytest.mark.asyncio
    async def test_tool_failure_handling(self, orchestrator):
        """Test tool failure handling."""
        failing_tool = MockTool("failing_tool", should_fail=True)
        orchestrator.register_tool(failing_tool)
        
        result = await orchestrator.execute_tool("failing_tool")
        
        assert not result.success
        assert "Mock tool failure" in result.error_message
    
    def test_tool_selection(self, orchestrator):
        """Test tool selection."""
        tool1 = MockTool("search_tool")
        tool2 = MockTool("calc_tool")
        
        orchestrator.register_tool(tool1)
        orchestrator.register_tool(tool2)
        
        # Simple selection (returns first available)
        selected = orchestrator.select_tool("Find information")
        assert selected in ["search_tool", "calc_tool"]


class TestAgentCoordination:
    """Test multi-agent coordination."""
    
    @pytest.fixture
    def coordinator(self):
        """Create agent coordinator for testing."""
        return AgentCoordinator()
    
    @pytest.mark.asyncio
    async def test_agent_registration(self, coordinator):
        """Test agent registration."""
        agent = MockAgent("test_agent")
        coordinator.register_agent("test_agent", agent)
        
        assert "test_agent" in coordinator.get_available_agents()
    
    @pytest.mark.asyncio
    async def test_sequential_coordination(self, coordinator):
        """Test sequential coordination strategy."""
        agent1 = MockAgent("agent1")
        agent2 = MockAgent("agent2")
        
        coordinator.register_agent("agent1", agent1)
        coordinator.register_agent("agent2", agent2)
        
        context = ExecutionContext(
            constraints=["max_steps: 5"],
            available_tools=["search"],
            metadata={}
        )
        
        tasks = [
            AgentTask("agent1", "First task", context, []),
            AgentTask("agent2", "Second task", context, ["agent1"])
        ]
        
        result = await coordinator.execute_tasks(tasks, CoordinationStrategy.SEQUENTIAL)
        
        assert isinstance(result, MultiAgentResult)
        assert result.success
        assert len(result.results) == 2
        assert agent1.execution_count == 1
        assert agent2.execution_count == 1
    
    @pytest.mark.asyncio
    async def test_parallel_coordination(self, coordinator):
        """Test parallel coordination strategy."""
        agent1 = MockAgent("agent1")
        agent2 = MockAgent("agent2")
        
        coordinator.register_agent("agent1", agent1)
        coordinator.register_agent("agent2", agent2)
        
        context = ExecutionContext(
            constraints=["max_steps: 5"],
            available_tools=["search"],
            metadata={}
        )
        
        tasks = [
            AgentTask("agent1", "Task 1", context, []),
            AgentTask("agent2", "Task 2", context, [])
        ]
        
        result = await coordinator.execute_tasks(tasks, CoordinationStrategy.PARALLEL)
        
        assert result.success
        assert len(result.results) == 2
    
    @pytest.mark.asyncio
    async def test_coordination_with_failure(self, coordinator):
        """Test coordination with agent failure."""
        good_agent = MockAgent("good_agent")
        bad_agent = MockAgent("bad_agent", should_fail=True)
        
        coordinator.register_agent("good_agent", good_agent)
        coordinator.register_agent("bad_agent", bad_agent)
        
        context = ExecutionContext(
            constraints=["max_steps: 5"],
            available_tools=["search"],
            metadata={}
        )
        
        tasks = [
            AgentTask("good_agent", "Good task", context, []),
            AgentTask("bad_agent", "Bad task", context, [])
        ]
        
        result = await coordinator.execute_tasks(tasks, CoordinationStrategy.PARALLEL)
        
        assert not result.success  # Overall failure due to one agent failing
        assert "good_agent" in result.results
        assert "bad_agent" in result.results
        assert result.results["good_agent"].success
        assert not result.results["bad_agent"].success


@pytest.mark.asyncio
async def test_agent_system_integration():
    """Test complete agent system integration."""
    # Create components
    orchestrator = ToolOrchestrator()
    coordinator = AgentCoordinator()
    
    # Register tools
    echo_tool = EchoTool()
    calc_tool = CalculatorTool()
    orchestrator.register_tool(echo_tool)
    orchestrator.register_tool(calc_tool)
    
    # Create agents
    agent1 = MockAgent("research_agent")
    agent2 = MockAgent("analysis_agent")
    
    coordinator.register_agent("research_agent", agent1)
    coordinator.register_agent("analysis_agent", agent2)
    
    # Create execution context
    context = ExecutionContext(
        constraints=["max_steps: 10", "timeout: 300"],
        available_tools=["echo", "calculator"],
        metadata={"session_id": "test_session"}
    )
    
    # Create coordinated tasks
    tasks = [
        AgentTask("research_agent", "Research the topic", context, []),
        AgentTask("analysis_agent", "Analyze findings", context, ["research_agent"])
    ]
    
    # Execute coordination
    result = await coordinator.execute_tasks(tasks, CoordinationStrategy.SEQUENTIAL)
    
    # Verify results
    assert result.success
    assert len(result.results) == 2
    assert result.coordination_strategy == CoordinationStrategy.SEQUENTIAL
    
    # Check individual agent results
    research_result = result.results["research_agent"]
    analysis_result = result.results["analysis_agent"]
    
    assert research_result.success
    assert analysis_result.success
    assert "Research the topic" in research_result.result
    assert "Analyze findings" in analysis_result.result
    
    # Test tool execution
    echo_result = await orchestrator.execute_tool("echo", text="Hello World")
    assert echo_result.success
    assert "Hello World" in echo_result.result
    
    calc_result = await orchestrator.execute_tool("calculator", expression="2 + 2")
    assert calc_result.success
    assert calc_result.result == 4
