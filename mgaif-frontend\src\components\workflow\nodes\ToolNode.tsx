import React from 'react';
import { NodeProps } from 'reactflow';
import { BaseNode } from './BaseNode';
import type { ToolNodeData } from '../../../types/workflow';

const toolIcons = {
  'web-search': '🔍',
  'calculator': '🧮',
  'file-reader': '📄',
  'api-call': '🌐',
  'database-query': '🗄️',
};

export const ToolNode: React.FC<NodeProps> = (props) => {
  const data = props.data as ToolNodeData;
  const icon = toolIcons[data.config.toolType] || '🔧';

  return (
    <BaseNode
      {...props}
      data={data}
      icon={icon}
      color="#13c2c2"
    />
  );
};
