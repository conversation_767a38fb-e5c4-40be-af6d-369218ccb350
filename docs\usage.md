# Usage

## Installation

```powershell
pip install -r requirements.txt
```

## Running the Edge API

```powershell
uvicorn core.edge.api:app --reload
```
- OpenAI-compatible endpoint: `POST /v1/chat/completions`
- Select adapter via env:
  - `MGAIF_EDGE_ADAPTER=echo` (default)
  - `MGAIF_EDGE_ADAPTER=ollama`

## Running the MCP-like API

```powershell
uvicorn core.mcp.api:app --reload
```

- Select LLM adapter:
  ```powershell
  $env:MGAIF_MCP_ADAPTER = "echo"  # or "ollama"
  uvicorn core.mcp.api:app --reload
  ```

## Workflows

- Run example RAG workflow:
```powershell
python run.py configs/workflows/rag_example.yaml
```

- RAG + LLM (echo):
```powershell
python run.py configs/workflows/rag_llm_echo.yaml
```

- RAG + LLM (ollama):
```powershell
$env:OLLAMA_MODEL = "llama3"
python run.py configs/workflows/rag_llm_ollama.yaml
```

## Benchmarks

- Run tests to generate `.benchmarks.json`:
```powershell
pytest -q
```
- Override output path:
```powershell
$env:MGAIF_BENCH_OUT = "bench-local.json"
pytest -q
```

## Observability

- Metrics endpoint (Edge API): `GET /metrics` (Prometheus format).
- Tracing (optional):
  ```powershell
  $env:OTEL_EXPORTER_OTLP_ENDPOINT = "http://localhost:4318"
  uvicorn core.edge.api:app --reload
  ```

## Security Guardrails

- Enable conservative prompt-injection blocking for Edge API:
  ```powershell
  $env:MGAIF_SECURITY_STRICT = "true"
  uvicorn core.edge.api:app --reload
  ```

## Docker Compose

Run Edge + OTEL Collector + Prometheus locally:

```powershell
docker compose -f deploy/docker-compose.yml up --build
```

- Edge API: http://localhost:8000
- Metrics: http://localhost:8000/metrics
- Prometheus: http://localhost:9090
