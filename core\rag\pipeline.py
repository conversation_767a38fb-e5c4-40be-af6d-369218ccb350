"""RAG pipeline orchestration.

This module provides the main RAG pipeline that orchestrates document processing,
retrieval, re-ranking, and grounding validation into a cohesive system.

The pipeline provides:
- End-to-end RAG workflow orchestration
- Configurable components and strategies
- Performance monitoring and metrics
- Error handling and fallback strategies
- Batch processing capabilities

Key Components:
- RAGPipeline: Main orchestration class
- RAGConfig: Configuration management
- RAGMetrics: Performance and quality metrics

Example:
    >>> from core.rag import RAGPipeline, RAGConfig
    >>> config = RAGConfig(
    ...     chunking_strategy="semantic",
    ...     retrieval_strategy="hybrid",
    ...     reranking_enabled=True,
    ...     grounding_validation=True
    ... )
    >>> pipeline = RAGPipeline(config)
    >>> 
    >>> # Index documents
    >>> await pipeline.index_documents(documents)
    >>> 
    >>> # Query pipeline
    >>> result = await pipeline.query("What is artificial intelligence?")
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime
import time

from .base import Document, Chunk, RAGResult, RAGError
from .chunking import ChunkingStrategy, FixedSizeChunker, SemanticChunker, RecursiveChunker
from .retrieval import Retriever, VectorRetriever, KeywordRetriever, HybridRetriever, RetrievalResult
from .reranking import Reranker, BM25Reranker, CrossEncoderReranker, LLMReranker, EnsembleReranker
from .grounding import GroundingValidator, ComprehensiveGroundingValidator, GroundingResult

logger = logging.getLogger(__name__)


@dataclass
class RAGConfig:
    """Configuration for RAG pipeline."""
    
    # Chunking configuration
    chunking_strategy: str = "recursive"  # fixed, semantic, recursive
    chunk_size: int = 512
    chunk_overlap: int = 50
    
    # Retrieval configuration
    retrieval_strategy: str = "hybrid"  # vector, keyword, hybrid
    top_k_retrieval: int = 20
    retrieval_score_threshold: float = 0.0
    
    # Vector retrieval specific
    embedding_model: str = "text-embedding-ada-002"
    similarity_metric: str = "cosine"
    
    # Keyword retrieval specific
    bm25_k1: float = 1.2
    bm25_b: float = 0.75
    
    # Hybrid retrieval specific
    vector_weight: float = 0.7
    keyword_weight: float = 0.3
    
    # Re-ranking configuration
    reranking_enabled: bool = True
    reranking_strategy: str = "bm25"  # bm25, cross_encoder, llm, ensemble
    top_k_reranking: int = 10
    
    # Cross-encoder specific
    cross_encoder_model: str = "cross-encoder/ms-marco-MiniLM-L-6-v2"
    cross_encoder_batch_size: int = 32
    
    # Grounding validation configuration
    grounding_validation: bool = True
    grounding_confidence_threshold: float = 0.7
    hallucination_threshold: float = 0.5
    
    # Performance configuration
    batch_size: int = 32
    max_concurrent_requests: int = 10
    timeout_seconds: float = 30.0
    
    # Caching configuration
    enable_caching: bool = True
    cache_ttl_seconds: int = 3600


@dataclass
class RAGMetrics:
    """Metrics for RAG pipeline performance and quality."""
    
    # Performance metrics
    total_queries: int = 0
    avg_query_time: float = 0.0
    avg_retrieval_time: float = 0.0
    avg_reranking_time: float = 0.0
    avg_grounding_time: float = 0.0
    
    # Quality metrics
    avg_retrieval_score: float = 0.0
    avg_grounding_confidence: float = 0.0
    avg_hallucination_score: float = 0.0
    
    # System metrics
    cache_hit_rate: float = 0.0
    error_rate: float = 0.0
    
    # Detailed timing
    timing_history: List[Dict[str, float]] = field(default_factory=list)
    
    def update_query_metrics(self, query_time: float, retrieval_time: float, 
                           reranking_time: float, grounding_time: float,
                           retrieval_score: float, grounding_confidence: float,
                           hallucination_score: float):
        """Update metrics with new query results."""
        self.total_queries += 1
        
        # Update averages using running average
        alpha = 1.0 / self.total_queries
        self.avg_query_time = (1 - alpha) * self.avg_query_time + alpha * query_time
        self.avg_retrieval_time = (1 - alpha) * self.avg_retrieval_time + alpha * retrieval_time
        self.avg_reranking_time = (1 - alpha) * self.avg_reranking_time + alpha * reranking_time
        self.avg_grounding_time = (1 - alpha) * self.avg_grounding_time + alpha * grounding_time
        
        self.avg_retrieval_score = (1 - alpha) * self.avg_retrieval_score + alpha * retrieval_score
        self.avg_grounding_confidence = (1 - alpha) * self.avg_grounding_confidence + alpha * grounding_confidence
        self.avg_hallucination_score = (1 - alpha) * self.avg_hallucination_score + alpha * hallucination_score
        
        # Store detailed timing (keep last 100 entries)
        timing_entry = {
            "timestamp": datetime.now().isoformat(),
            "query_time": query_time,
            "retrieval_time": retrieval_time,
            "reranking_time": reranking_time,
            "grounding_time": grounding_time,
            "retrieval_score": retrieval_score,
            "grounding_confidence": grounding_confidence,
            "hallucination_score": hallucination_score
        }
        self.timing_history.append(timing_entry)
        if len(self.timing_history) > 100:
            self.timing_history.pop(0)


class RAGPipeline:
    """Main RAG pipeline orchestrating all components."""
    
    def __init__(self, config: RAGConfig):
        self.config = config
        self.metrics = RAGMetrics()
        
        # Initialize components
        self.chunker = self._create_chunker()
        self.retriever = self._create_retriever()
        self.reranker = self._create_reranker() if config.reranking_enabled else None
        self.grounding_validator = self._create_grounding_validator() if config.grounding_validation else None
        
        # State
        self.indexed_documents: List[Document] = []
        self.is_indexed = False
        
        # Caching
        self.query_cache: Dict[str, RAGResult] = {}
        
        logger.info(f"RAG pipeline initialized with config: {config}")
    
    def _create_chunker(self) -> ChunkingStrategy:
        """Create chunker based on configuration."""
        if self.config.chunking_strategy == "fixed":
            return FixedSizeChunker(
                chunk_size=self.config.chunk_size,
                overlap=self.config.chunk_overlap
            )
        elif self.config.chunking_strategy == "semantic":
            return SemanticChunker(
                chunk_size=self.config.chunk_size,
                overlap=self.config.chunk_overlap
            )
        elif self.config.chunking_strategy == "recursive":
            return RecursiveChunker(
                chunk_size=self.config.chunk_size,
                overlap=self.config.chunk_overlap
            )
        else:
            raise RAGError(f"Unknown chunking strategy: {self.config.chunking_strategy}")
    
    def _create_retriever(self) -> Retriever:
        """Create retriever based on configuration."""
        if self.config.retrieval_strategy == "vector":
            return VectorRetriever(
                embedding_model=self.config.embedding_model,
                similarity_metric=self.config.similarity_metric,
                top_k=self.config.top_k_retrieval,
                score_threshold=self.config.retrieval_score_threshold
            )
        elif self.config.retrieval_strategy == "keyword":
            return KeywordRetriever(
                k1=self.config.bm25_k1,
                b=self.config.bm25_b,
                top_k=self.config.top_k_retrieval,
                score_threshold=self.config.retrieval_score_threshold
            )
        elif self.config.retrieval_strategy == "hybrid":
            return HybridRetriever(
                vector_weight=self.config.vector_weight,
                keyword_weight=self.config.keyword_weight,
                vector_config={
                    "embedding_model": self.config.embedding_model,
                    "similarity_metric": self.config.similarity_metric
                },
                keyword_config={
                    "k1": self.config.bm25_k1,
                    "b": self.config.bm25_b
                },
                top_k=self.config.top_k_retrieval,
                score_threshold=self.config.retrieval_score_threshold
            )
        else:
            raise RAGError(f"Unknown retrieval strategy: {self.config.retrieval_strategy}")
    
    def _create_reranker(self) -> Optional[Reranker]:
        """Create reranker based on configuration."""
        if self.config.reranking_strategy == "bm25":
            return BM25Reranker(
                k1=self.config.bm25_k1,
                b=self.config.bm25_b,
                top_k=self.config.top_k_reranking
            )
        elif self.config.reranking_strategy == "cross_encoder":
            return CrossEncoderReranker(
                model_name=self.config.cross_encoder_model,
                batch_size=self.config.cross_encoder_batch_size,
                top_k=self.config.top_k_reranking
            )
        elif self.config.reranking_strategy == "llm":
            return LLMReranker(top_k=self.config.top_k_reranking)
        elif self.config.reranking_strategy == "ensemble":
            # Create ensemble of multiple rerankers
            rerankers = [
                (BM25Reranker(top_k=self.config.top_k_reranking), 0.3),
                (CrossEncoderReranker(top_k=self.config.top_k_reranking), 0.7)
            ]
            return EnsembleReranker(rerankers, top_k=self.config.top_k_reranking)
        else:
            raise RAGError(f"Unknown reranking strategy: {self.config.reranking_strategy}")
    
    def _create_grounding_validator(self) -> Optional[GroundingValidator]:
        """Create grounding validator based on configuration."""
        return ComprehensiveGroundingValidator(
            confidence_threshold=self.config.grounding_confidence_threshold
        )
    
    async def index_documents(self, documents: List[Document]) -> None:
        """Index documents for retrieval."""
        logger.info(f"Indexing {len(documents)} documents")
        start_time = time.time()
        
        try:
            # Store documents
            self.indexed_documents = documents
            
            # Chunk documents
            chunked_documents = []
            for doc in documents:
                chunks = await self.chunker.chunk_document(doc)
                doc.chunks = chunks
                chunked_documents.append(doc)
            
            # Index in retriever
            await self.retriever.index_documents(chunked_documents)
            
            self.is_indexed = True
            index_time = time.time() - start_time
            
            logger.info(f"Indexing completed in {index_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Document indexing failed: {e}")
            raise RAGError(f"Indexing failed: {e}")
    
    def _get_cache_key(self, query: str, top_k: int) -> str:
        """Generate cache key for query."""
        import hashlib
        key_data = f"{query}:{top_k}:{hash(str(self.config))}"
        return hashlib.md5(key_data.encode()).hexdigest()
    
    async def query(self, query: str, top_k: Optional[int] = None, 
                   include_grounding: bool = True) -> RAGResult:
        """Execute RAG query pipeline."""
        if not self.is_indexed:
            raise RAGError("Pipeline not indexed. Call index_documents() first.")
        
        top_k = top_k or self.config.top_k_reranking
        start_time = time.time()
        
        # Check cache
        cache_key = self._get_cache_key(query, top_k)
        if self.config.enable_caching and cache_key in self.query_cache:
            cached_result = self.query_cache[cache_key]
            # Check if cache is still valid
            cache_age = (datetime.now() - cached_result.timestamp).total_seconds()
            if cache_age < self.config.cache_ttl_seconds:
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached_result
        
        try:
            # Step 1: Retrieval
            retrieval_start = time.time()
            retrieval_results = await self.retriever.retrieve(query, self.config.top_k_retrieval)
            retrieval_time = time.time() - retrieval_start
            
            if not retrieval_results:
                logger.warning(f"No results retrieved for query: {query}")
                return RAGResult(
                    query=query,
                    chunks=[],
                    scores=[],
                    metadata={"error": "no_results", "retrieval_time": retrieval_time}
                )
            
            # Step 2: Re-ranking (optional)
            reranking_time = 0.0
            if self.reranker:
                reranking_start = time.time()
                retrieval_results = await self.reranker.rerank(query, retrieval_results, top_k)
                reranking_time = time.time() - reranking_start
            else:
                # Just take top-k if no re-ranking
                retrieval_results = retrieval_results[:top_k]
            
            # Step 3: Grounding validation (optional)
            grounding_time = 0.0
            grounding_result = None
            if self.grounding_validator and include_grounding:
                grounding_start = time.time()
                # For grounding, we need a response - this would typically come from LLM generation
                # For now, we'll skip grounding validation in the pipeline
                # grounding_result = await self.grounding_validator.validate_grounding(
                #     query, retrieval_results, response
                # )
                grounding_time = time.time() - grounding_start
            
            # Create result
            chunks = [result.chunk for result in retrieval_results]
            scores = [result.score for result in retrieval_results]
            
            total_time = time.time() - start_time
            
            result = RAGResult(
                query=query,
                chunks=chunks,
                scores=scores,
                metadata={
                    "retrieval_method": self.config.retrieval_strategy,
                    "reranking_method": self.config.reranking_strategy if self.reranker else None,
                    "total_time": total_time,
                    "retrieval_time": retrieval_time,
                    "reranking_time": reranking_time,
                    "grounding_time": grounding_time,
                    "num_retrieved": len(retrieval_results),
                    "grounding_result": grounding_result
                }
            )
            
            # Update metrics
            avg_score = sum(scores) / len(scores) if scores else 0.0
            grounding_confidence = grounding_result.confidence if grounding_result else 0.0
            hallucination_score = grounding_result.hallucination_score if grounding_result else 0.0
            
            self.metrics.update_query_metrics(
                total_time, retrieval_time, reranking_time, grounding_time,
                avg_score, grounding_confidence, hallucination_score
            )
            
            # Cache result
            if self.config.enable_caching:
                self.query_cache[cache_key] = result
                # Limit cache size
                if len(self.query_cache) > 1000:
                    # Remove oldest entries
                    oldest_keys = list(self.query_cache.keys())[:100]
                    for key in oldest_keys:
                        del self.query_cache[key]
            
            logger.debug(f"Query completed in {total_time:.3f}s: {query[:50]}...")
            return result
            
        except Exception as e:
            logger.error(f"Query pipeline failed: {e}")
            self.metrics.error_rate = (self.metrics.error_rate * self.metrics.total_queries + 1) / (self.metrics.total_queries + 1)
            raise RAGError(f"Query failed: {e}")
    
    async def batch_query(self, queries: List[str], top_k: Optional[int] = None) -> List[RAGResult]:
        """Execute multiple queries in batch."""
        semaphore = asyncio.Semaphore(self.config.max_concurrent_requests)
        
        async def query_with_semaphore(query: str) -> RAGResult:
            async with semaphore:
                return await self.query(query, top_k)
        
        tasks = [query_with_semaphore(query) for query in queries]
        return await asyncio.gather(*tasks)
    
    def get_metrics(self) -> RAGMetrics:
        """Get pipeline performance metrics."""
        return self.metrics
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive pipeline statistics."""
        return {
            "config": self.config.__dict__,
            "metrics": self.metrics.__dict__,
            "indexed_documents": len(self.indexed_documents),
            "is_indexed": self.is_indexed,
            "cache_size": len(self.query_cache),
            "retriever_stats": self.retriever.get_stats() if hasattr(self.retriever, 'get_stats') else {},
        }
    
    def clear_cache(self) -> None:
        """Clear query cache."""
        self.query_cache.clear()
        logger.info("Query cache cleared")
    
    async def validate_response_grounding(self, query: str, response: str, 
                                        context: Optional[List[RetrievalResult]] = None) -> GroundingResult:
        """Validate grounding of a generated response."""
        if not self.grounding_validator:
            raise RAGError("Grounding validation not enabled")
        
        if context is None:
            # Retrieve context for the query
            rag_result = await self.query(query, include_grounding=False)
            context = [
                RetrievalResult(chunk=chunk, score=score, retrieval_method="pipeline")
                for chunk, score in zip(rag_result.chunks, rag_result.scores)
            ]
        
        return await self.grounding_validator.validate_grounding(query, context, response)
