"""Agent system for M-GAIF framework.

This module provides the core agent infrastructure for autonomous AI agents
with planning, reasoning, memory, and tool orchestration capabilities.

The agent system supports:
- Multi-step planning and reasoning
- Episodic and semantic memory
- Tool orchestration and execution
- Multi-agent coordination
- Reflection and learning

Key Components:
- Agent: Base agent interface with planning and execution
- Memory: Persistent memory systems for agents
- Planner: Planning algorithms (CoT, ToT, ReAct)
- ToolOrchestrator: Tool selection and execution
- AgentCoordinator: Multi-agent coordination

Example:
    >>> from core.agent import Agent, ReActPlanner, EpisodicMemory
    >>> agent = Agent(
    ...     planner=ReActPlanner(),
    ...     memory=EpisodicMemory(),
    ...     tools=["search", "calculator"]
    ... )
    >>> result = await agent.execute("What is the weather in Paris?")
"""

from .base import Agent, AgentResult, ExecutionContext, AgentStatus
from .memory import MemoryItem, EpisodicMemory, SemanticMemory
from .planning import Planner, Plan, PlanStep, PlanStatus, CoTP<PERSON>ner, ReAct<PERSON>lan<PERSON>
from .tools import Too<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolOrchestrator, EchoTool, CalculatorTool
from .coordination import AgentCoordinator, MultiAgentResult, AgentTask, CoordinationStrategy

__all__ = [
    # Base classes
    "Agent",
    "AgentResult",
    "ExecutionContext",
    "AgentStatus",

    # Memory system
    "MemoryItem",
    "EpisodicMemory",
    "SemanticMemory",

    # Planning system
    "Planner",
    "Plan",
    "PlanStep",
    "PlanStatus",
    "CoTPlanner",
    "ReActPlanner",

    # Tool system
    "Tool",
    "ToolResult",
    "ToolOrchestrator",
    "EchoTool",
    "CalculatorTool",

    # Multi-agent coordination
    "AgentCoordinator",
    "MultiAgentResult",
    "AgentTask",
    "CoordinationStrategy",
]