"""Base classes and interfaces for LLM adapters.

This module provides the foundational interfaces and error handling for LLM
adapters within the M-GAIF framework. Adapters bridge the gap between the
framework's standardized contracts and specific LLM provider APIs.

The base adapter interface defines:
- Standardized chat completion methods
- Streaming and non-streaming support
- Error handling and exceptions
- Provider-agnostic abstractions

Adapter implementations should:
- Inherit from BaseModelAdapter
- Implement both chat and chat_stream methods
- Handle provider-specific authentication
- Convert between provider formats and framework contracts
- Provide appropriate error handling

Example:
    >>> class CustomAdapter(BaseModelAdapter):
    ...     name = "custom"
    ...
    ...     async def chat(self, request):
    ...         # Implementation here
    ...         pass
    ...
    ...     async def chat_stream(self, request):
    ...         # Streaming implementation here
    ...         yield chunk

Note:
    All adapters should be stateless and thread-safe for concurrent use.
"""

from __future__ import annotations

import abc
from typing import AsyncIterator, Optional

from ..contracts.llm import (
    ChatCompletionChunk,
    ChatCompletionRequest,
    ChatCompletionResponse,
)


class ModelAdapterError(RuntimeError):
    """Raised when an adapter encounters a recoverable model/API error."""


class BaseModelAdapter(abc.ABC):
    """Base interface for model adapters.

    Concrete adapters should implement `chat` and `chat_stream`.
    """

    name: str = "base"

    @abc.abstractmethod
    async def chat(self, request: ChatCompletionRequest) -> ChatCompletionResponse:
        """Generate a non-streaming chat completion response.

        Processes a chat completion request and returns a complete response
        with all generated content in a single response object.

        Args:
            request: Chat completion request with messages and parameters

        Returns:
            Complete chat completion response with generated content

        Raises:
            ModelAdapterError: If the request fails or model is unavailable

        Example:
            >>> adapter = EchoAdapter()
            >>> request = ChatCompletionRequest(
            ...     messages=[ChatMessage(role="user", content="Hello!")]
            ... )
            >>> response = await adapter.chat(request)
            >>> print(response.choices[0].message.content)
        """
        raise NotImplementedError

    @abc.abstractmethod
    async def chat_stream(
        self, request: ChatCompletionRequest
    ) -> AsyncIterator[ChatCompletionChunk]:
        """Generate a streaming chat completion response.

        Processes a chat completion request and yields response chunks
        as they are generated, enabling real-time streaming interfaces.

        Args:
            request: Chat completion request with messages and parameters

        Yields:
            ChatCompletionChunk: Individual chunks of the response as generated

        Raises:
            ModelAdapterError: If the request fails or model is unavailable

        Example:
            >>> adapter = EchoAdapter()
            >>> request = ChatCompletionRequest(
            ...     messages=[ChatMessage(role="user", content="Hello!")],
            ...     stream=True
            ... )
            >>> async for chunk in adapter.chat_stream(request):
            ...     print(chunk.choices[0].delta.content, end="")
        """
        if False:
            yield  # pragma: no cover
        raise NotImplementedError
