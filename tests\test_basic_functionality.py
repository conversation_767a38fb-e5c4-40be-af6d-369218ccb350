"""Basic functionality tests to verify the test environment works."""

import pytest
import asyncio
from datetime import datetime


def test_basic_math():
    """Test basic math operations."""
    assert 2 + 2 == 4
    assert 10 - 5 == 5
    assert 3 * 4 == 12
    assert 8 / 2 == 4


def test_string_operations():
    """Test string operations."""
    text = "Hello, World!"
    assert len(text) == 13
    assert text.lower() == "hello, world!"
    assert text.upper() == "HELLO, WORLD!"
    assert "World" in text


def test_list_operations():
    """Test list operations."""
    items = [1, 2, 3, 4, 5]
    assert len(items) == 5
    assert items[0] == 1
    assert items[-1] == 5
    assert sum(items) == 15


def test_dict_operations():
    """Test dictionary operations."""
    data = {"name": "test", "value": 42}
    assert data["name"] == "test"
    assert data["value"] == 42
    assert len(data) == 2
    assert "name" in data


@pytest.mark.asyncio
async def test_async_function():
    """Test async function execution."""
    async def async_add(a, b):
        await asyncio.sleep(0.01)  # Simulate async work
        return a + b
    
    result = await async_add(3, 4)
    assert result == 7


def test_datetime_operations():
    """Test datetime operations."""
    now = datetime.now()
    assert isinstance(now, datetime)
    assert now.year >= 2024


class TestClass:
    """Test class-based testing."""
    
    def test_class_method(self):
        """Test method in class."""
        assert True
    
    def test_class_setup(self):
        """Test class setup."""
        self.value = 42
        assert self.value == 42


@pytest.fixture
def sample_data():
    """Sample data fixture."""
    return {"test": "data", "numbers": [1, 2, 3]}


def test_fixture_usage(sample_data):
    """Test fixture usage."""
    assert sample_data["test"] == "data"
    assert len(sample_data["numbers"]) == 3
    assert sum(sample_data["numbers"]) == 6


def test_exception_handling():
    """Test exception handling."""
    with pytest.raises(ZeroDivisionError):
        result = 10 / 0
    
    with pytest.raises(KeyError):
        data = {}
        value = data["missing_key"]


@pytest.mark.parametrize("input,expected", [
    (1, 2),
    (2, 4),
    (3, 6),
    (4, 8),
])
def test_parametrized(input, expected):
    """Test parametrized testing."""
    assert input * 2 == expected


def test_boolean_operations():
    """Test boolean operations."""
    assert True is True
    assert False is False
    assert not False
    assert True and True
    assert True or False
    assert not (False and True)


def test_type_checking():
    """Test type checking."""
    assert isinstance(42, int)
    assert isinstance("hello", str)
    assert isinstance([1, 2, 3], list)
    assert isinstance({"key": "value"}, dict)
    assert isinstance(True, bool)


if __name__ == "__main__":
    pytest.main([__file__])
