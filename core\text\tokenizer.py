"""Text tokenization interfaces and contracts.

This module defines the abstract tokenizer interface used throughout the M-GAIF
framework. Tokenizers convert text into sequences of integer tokens and back,
enabling consistent text processing across different components.

The tokenizer interface supports:
- Bidirectional text-to-token conversion (encode/decode)
- Token counting for length estimation
- Deterministic and thread-safe implementations
- Integration with various tokenization strategies

Implementations should be:
- Deterministic: Same input always produces same output
- Thread-safe: Safe for concurrent use
- Reversible: decode(encode(text)) should preserve meaning
- Efficient: Optimized for batch processing

Example:
    >>> from plugins.tokenizers.simple_tokenizer import SimpleTokenizer
    >>> tokenizer = SimpleTokenizer()
    >>> tokens = tokenizer.encode("Hello, world!")
    >>> text = tokenizer.decode(tokens)
    >>> count = tokenizer.token_count("Hello, world!")

Note:
    Token IDs are framework-specific and may not be compatible between
    different tokenizer implementations.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import List


class Tokenizer(ABC):
    """Abstract tokenizer interface used across the framework.

    Implementations should be deterministic and thread-safe.
    """

    @abstractmethod
    def encode(self, text: str) -> List[int]:
        """Convert text into a list of token IDs.

        Transforms input text into a sequence of integer token identifiers
        that can be processed by language models or other NLP components.

        Args:
            text: Input text to tokenize

        Returns:
            List of integer token IDs representing the input text

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> tokens = tokenizer.encode("Hello, world!")
            >>> print(tokens)  # [1, 2, 3]

        Note:
            Token IDs are specific to each tokenizer implementation and
            should not be mixed between different tokenizers.
        """
        raise NotImplementedError

    @abstractmethod
    def decode(self, tokens: List[int]) -> str:
        """Convert a list of token IDs back to text.

        Reconstructs the original text from a sequence of token IDs,
        performing the inverse operation of encode().

        Args:
            tokens: List of integer token IDs to decode

        Returns:
            Reconstructed text string

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> text = tokenizer.decode([1, 2, 3])
            >>> print(text)  # "Hello, world!"

        Note:
            The decoded text may not be identical to the original due to
            tokenization artifacts, but should preserve semantic meaning.
        """
        raise NotImplementedError

    def token_count(self, text: str) -> int:
        """Count the number of tokens in text without full tokenization.

        Provides an efficient way to estimate token count for length
        validation and cost estimation without full encode/decode.

        Args:
            text: Input text to count tokens for

        Returns:
            Number of tokens the text would produce when encoded

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> count = tokenizer.token_count("Hello, world!")
            >>> print(count)  # 3

        Note:
            Default implementation uses len(encode(text)). Override for
            better performance if tokenization is expensive.
        """
        return len(self.encode(text))
