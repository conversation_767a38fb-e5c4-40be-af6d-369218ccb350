# API Reference

This section documents the most important public modules and classes. Generated using Sphinx autodoc.

## Workflow

```{eval-rst}
.. automodule:: core.workflow.engine
   :members:
   :undoc-members:
   :show-inheritance:
```

## Adapters

```{eval-rst}
.. automodule:: core.adapters.openai_adapter
   :members:
   :undoc-members:
   :show-inheritance:

.. automodule:: core.adapters.ollama_adapter
   :members:
   :undoc-members:
   :show-inheritance:
```

## MCP-like API

```{eval-rst}
.. automodule:: core.mcp.api
   :members:
   :undoc-members:
   :show-inheritance:
```

## Edge API

```{eval-rst}
.. automodule:: core.edge.api
   :members:
   :undoc-members:
   :show-inheritance:
```

## Plugins

```{eval-rst}
.. automodule:: plugins.embedders.simple_embedder
   :members:
   :undoc-members:

.. automodule:: plugins.retrievers.in_memory_retriever
   :members:
   :undoc-members:

.. automodule:: plugins.tokenizers.simple_tokenizer
   :members:
   :undoc-members:
```
