"""OpenAI-compatible Edge API for M-GAIF.

This module implements an OpenAI-compatible REST API that provides chat completion
endpoints with streaming support. The Edge API is designed for direct integration
with applications expecting OpenAI's chat completion format.

Features:
- OpenAI-compatible chat completions endpoint
- Streaming and non-streaming responses
- Prometheus metrics collection
- OpenTelemetry distributed tracing
- Security middleware integration
- Health and readiness checks

API Endpoints:
- POST /v1/chat/completions: Chat completions (OpenAI-compatible)
- GET /health: Health check endpoint
- GET /metrics: Prometheus metrics
- GET /docs: Interactive API documentation

Streaming Support:
- Server-sent events (SSE) for real-time responses
- Chunked response format compatible with OpenAI
- Proper connection handling and cleanup

Example Usage:
    >>> import httpx
    >>> response = httpx.post(
    ...     "http://localhost:8000/v1/chat/completions",
    ...     json={
    ...         "messages": [{"role": "user", "content": "Hello!"}],
    ...         "stream": False
    ...     }
    ... )

Configuration:
- MGAIF_EDGE_ADAPTER: LLM adapter selection (echo/ollama)
- OTEL_EXPORTER_OTLP_ENDPOINT: OpenTelemetry collector endpoint
- MGAIF_SECURITY_STRICT: Enable strict security mode

Note:
    The Edge API provides a familiar interface for developers already
    using OpenAI's API, enabling easy migration to M-GAIF.
"""

from __future__ import annotations

import asyncio
import json
import re
import logging
import os
import time
import uuid
from typing import AsyncIterator, Optional

from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse, StreamingResponse

from ..contracts.llm import ChatCompletionRequest
from ..adapters.openai_adapter import EchoAdapter
from ..adapters.ollama_adapter import OllamaAdapter
from ..security.middleware import create_security_middleware, SecurityMiddleware, ToolAccessMiddleware

# Prometheus metrics
from prometheus_client import Counter, Histogram, CONTENT_TYPE_LATEST, generate_latest

# Optional OpenTelemetry tracing (best-effort init)
try:  # pragma: no cover - optional
    from opentelemetry import trace
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.exporter.otlp.proto.http.trace_exporter import OTLPSpanExporter

    _otel_available = True
except Exception:  # pragma: no cover - optional
    _otel_available = False


# Basic JSON logging
logger = logging.getLogger("mgaif.edge")
if not logger.handlers:
    handler = logging.StreamHandler()
    formatter = logging.Formatter(fmt="%(message)s")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
logger.setLevel(os.getenv("LOG_LEVEL", "INFO").upper())


def _jsonlog(event: str, **fields: object) -> None:
    try:
        logger.info(json.dumps({"event": event, **fields}))
    except Exception:
        logger.info({"event": event, **fields})


app = FastAPI(title="M-GAIF Edge API")

# Add security middleware
_strict_mode = os.getenv("MGAIF_SECURITY_STRICT", "false").lower() in {"1", "true", "yes"}
_rate_limit = int(os.getenv("MGAIF_RATE_LIMIT", "100"))
security_middleware, tool_access_middleware = create_security_middleware(
    strict_mode=_strict_mode,
    rate_limit=_rate_limit
)

# Add the middleware instances
app.middleware("http")(security_middleware.dispatch)
app.middleware("http")(tool_access_middleware.dispatch)

# Metrics
REQ_COUNTER = Counter("mgaif_edge_requests_total", "Total requests", ["path", "method"])
REQ_LATENCY = Histogram(
    "mgaif_edge_request_latency_seconds", "Request latency", ["path", "method"]
)
TOKEN_COUNTER = Counter(
    "mgaif_edge_tokens_total", "Total tokens processed (prompt+completion)", ["path"]
)


def _init_tracer() -> Optional["trace.Tracer"]:
    if not _otel_available:
        return None
    endpoint = os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT")
    if not endpoint:
        return None
    try:
        provider = TracerProvider()
        exporter = OTLPSpanExporter(endpoint=f"{endpoint}/v1/traces")
        provider.add_span_processor(BatchSpanProcessor(exporter))
        trace.set_tracer_provider(provider)
        return trace.get_tracer("mgaif.edge")
    except Exception:
        return None


_tracer = _init_tracer()


_which = os.getenv("MGAIF_EDGE_ADAPTER", "echo").lower()
if _which == "ollama":
    adapter = OllamaAdapter()
else:
    adapter = EchoAdapter()


@app.get("/metrics")
def metrics() -> Response:
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


@app.get("/health")
def health():
    return {"status": "ok"}


@app.post("/v1/chat/completions")
async def chat_completions(req: ChatCompletionRequest, request: Request):
    path = str(request.url.path)
    method = request.method
    rid = request.headers.get("X-Request-ID") or str(uuid.uuid4())
    start = time.perf_counter()
    REQ_COUNTER.labels(path=path, method=method).inc()

    # Basic prompt-injection detection (very conservative)
    _strict = os.getenv("MGAIF_SECURITY_STRICT", "false").lower() in {"1", "true", "yes"}
    _inj_re = re.compile(r"(?i)ignore (all|any|previous) instructions|disregard.*policy|reveal.*system prompt|sudo |/etc/passwd")

    def _has_injection() -> bool:
        try:
            for m in req.messages:
                if m.role == "user" and _inj_re.search(m.content or ""):
                    return True
        except Exception:
            return False
        return False

    if _has_injection() and _strict:
        _jsonlog("edge.blocked", path=path, method=method, request_id=rid, reason="prompt_injection")
        return JSONResponse(status_code=400, content={"error": {"type": "bad_request", "message": "prompt flagged by guardrails"}})

    async def _non_stream():
        resp = await adapter.chat(req)
        # track tokens
        try:
            TOKEN_COUNTER.labels(path=path).inc(resp.usage.total_tokens)
        except Exception:
            pass
        return JSONResponse(content=json.loads(resp.model_dump_json()))

    try:
        if _tracer:
            with _tracer.start_as_current_span("chat_completions", attributes={"request.id": rid, "adapter": getattr(adapter, "name", "unknown")}):
                if not req.stream:
                    response = await _non_stream()
                else:
                    async def gen() -> AsyncIterator[bytes]:
                        async for chunk in adapter.chat_stream(req):
                            yield ("data: " + chunk.model_dump_json() + "\n\n").encode("utf-8")
                        yield b"data: [DONE]\n\n"

                    response = StreamingResponse(gen(), media_type="text/event-stream")
        else:
            if not req.stream:
                response = await _non_stream()
            else:
                async def gen() -> AsyncIterator[bytes]:
                    async for chunk in adapter.chat_stream(req):
                        yield ("data: " + chunk.model_dump_json() + "\n\n").encode("utf-8")
                    yield b"data: [DONE]\n\n"

                response = StreamingResponse(gen(), media_type="text/event-stream")
        return response
    finally:
        dur = time.perf_counter() - start
        REQ_LATENCY.labels(path=path, method=method).observe(dur)
        _jsonlog("edge.request", path=path, method=method, request_id=rid, duration_ms=int(dur * 1000))

