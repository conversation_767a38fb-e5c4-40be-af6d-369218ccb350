"""Tool system for agent orchestration."""

from __future__ import annotations

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Callable

logger = logging.getLogger(__name__)


class ToolStatus(Enum):
    """Tool execution status."""
    AVAILABLE = "available"
    BUSY = "busy"
    ERROR = "error"
    UNAVAILABLE = "unavailable"


@dataclass
class ToolResult:
    """Result of tool execution."""
    success: bool
    result: Any
    error_message: Optional[str]
    execution_time: float
    metadata: Dict[str, Any]
    timestamp: datetime


class Tool(ABC):
    """Abstract base class for agent tools.
    
    Tools provide specific capabilities that agents can use
    to interact with external systems and perform actions.
    
    Example:
        >>> class SearchTool(Tool):
        ...     async def execute(self, query: str) -> ToolResult:
        ...         results = await search_api.search(query)
        ...         return ToolResult(
        ...             success=True,
        ...             result=results,
        ...             error_message=None,
        ...             execution_time=1.2,
        ...             metadata={"query": query},
        ...             timestamp=datetime.now()
        ...         )
    """
    
    def __init__(self, name: str, description: str):
        """Initialize tool with name and description.
        
        Args:
            name: Unique tool name
            description: Human-readable description of tool capabilities
        """
        self.name = name
        self.description = description
        self.status = ToolStatus.AVAILABLE
        self._execution_count = 0
        self._total_execution_time = 0.0
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters.
        
        Args:
            **kwargs: Tool-specific parameters
            
        Returns:
            ToolResult with execution outcome
        """
        pass
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool parameter schema.
        
        Returns:
            JSON schema describing tool parameters
        """
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
    
    def get_metrics(self) -> Dict[str, float]:
        """Get tool performance metrics."""
        avg_time = (self._total_execution_time / self._execution_count 
                   if self._execution_count > 0 else 0.0)
        
        return {
            "execution_count": float(self._execution_count),
            "avg_execution_time": avg_time,
            "total_execution_time": self._total_execution_time,
            "status_code": float(hash(self.status.value) % 1000)
        }


class ToolOrchestrator:
    """Orchestrates tool selection and execution for agents.
    
    Manages a registry of available tools and provides intelligent
    tool selection based on agent goals and context.
    
    Features:
    - Tool registry and discovery
    - Intelligent tool selection
    - Parallel tool execution
    - Error handling and retries
    - Performance monitoring
    
    Example:
        >>> orchestrator = ToolOrchestrator()
        >>> orchestrator.register_tool(SearchTool())
        >>> orchestrator.register_tool(CalculatorTool())
        >>> 
        >>> result = await orchestrator.execute_tool(
        ...     "search", query="AI safety research"
        ... )
    """
    
    def __init__(self):
        """Initialize tool orchestrator."""
        self._tools: Dict[str, Tool] = {}
        self._tool_selectors: List[Callable[[str, Dict[str, Any]], str]] = []
        self._execution_locks: Dict[str, asyncio.Lock] = {}
    
    def register_tool(self, tool: Tool) -> None:
        """Register a tool for use by agents.
        
        Args:
            tool: Tool instance to register
        """
        self._tools[tool.name] = tool
        self._execution_locks[tool.name] = asyncio.Lock()
        logger.info(f"Registered tool: {tool.name}")
    
    def unregister_tool(self, tool_name: str) -> bool:
        """Unregister a tool.
        
        Args:
            tool_name: Name of tool to unregister
            
        Returns:
            True if tool was unregistered
        """
        if tool_name in self._tools:
            del self._tools[tool_name]
            del self._execution_locks[tool_name]
            logger.info(f"Unregistered tool: {tool_name}")
            return True
        return False
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return [name for name, tool in self._tools.items() 
                if tool.status == ToolStatus.AVAILABLE]
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get schemas for all available tools."""
        return [tool.get_schema() for tool in self._tools.values()
                if tool.status == ToolStatus.AVAILABLE]
    
    async def execute_tool(self, tool_name: str, **kwargs) -> ToolResult:
        """Execute a specific tool with parameters.
        
        Args:
            tool_name: Name of tool to execute
            **kwargs: Tool-specific parameters
            
        Returns:
            ToolResult with execution outcome
        """
        if tool_name not in self._tools:
            return ToolResult(
                success=False,
                result=None,
                error_message=f"Tool '{tool_name}' not found",
                execution_time=0.0,
                metadata={"available_tools": list(self._tools.keys())},
                timestamp=datetime.now()
            )
        
        tool = self._tools[tool_name]
        
        if tool.status != ToolStatus.AVAILABLE:
            return ToolResult(
                success=False,
                result=None,
                error_message=f"Tool '{tool_name}' is {tool.status.value}",
                execution_time=0.0,
                metadata={"tool_status": tool.status.value},
                timestamp=datetime.now()
            )
        
        # Execute tool with lock to prevent concurrent execution
        async with self._execution_locks[tool_name]:
            tool.status = ToolStatus.BUSY
            start_time = datetime.now()
            
            try:
                result = await tool.execute(**kwargs)
                
                # Update tool metrics
                execution_time = (datetime.now() - start_time).total_seconds()
                tool._execution_count += 1
                tool._total_execution_time += execution_time
                
                tool.status = ToolStatus.AVAILABLE
                return result
                
            except Exception as e:
                execution_time = (datetime.now() - start_time).total_seconds()
                tool.status = ToolStatus.ERROR
                
                logger.error(f"Tool {tool_name} execution failed: {e}")
                
                return ToolResult(
                    success=False,
                    result=None,
                    error_message=str(e),
                    execution_time=execution_time,
                    metadata={"exception_type": type(e).__name__},
                    timestamp=start_time
                )
    
    async def execute_tools_parallel(self, tool_calls: List[Dict[str, Any]]) -> List[ToolResult]:
        """Execute multiple tools in parallel.
        
        Args:
            tool_calls: List of tool call dictionaries with 'name' and 'parameters'
            
        Returns:
            List of ToolResult objects in same order as input
        """
        tasks = []
        
        for call in tool_calls:
            tool_name = call["name"]
            parameters = call.get("parameters", {})
            
            task = asyncio.create_task(
                self.execute_tool(tool_name, **parameters)
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Convert exceptions to ToolResult objects
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append(ToolResult(
                    success=False,
                    result=None,
                    error_message=str(result),
                    execution_time=0.0,
                    metadata={"tool_call": tool_calls[i]},
                    timestamp=datetime.now()
                ))
            else:
                processed_results.append(result)
        
        return processed_results
    
    def select_tool(self, goal: str, context: Dict[str, Any] = None) -> Optional[str]:
        """Select the best tool for a given goal.
        
        Args:
            goal: Goal or task description
            context: Additional context for tool selection
            
        Returns:
            Name of selected tool or None if no suitable tool
        """
        context = context or {}
        
        # Use registered tool selectors
        for selector in self._tool_selectors:
            try:
                selected_tool = selector(goal, context)
                if selected_tool and selected_tool in self._tools:
                    return selected_tool
            except Exception as e:
                logger.warning(f"Tool selector failed: {e}")
        
        # Default selection: return first available tool
        available_tools = self.get_available_tools()
        return available_tools[0] if available_tools else None
    
    def add_tool_selector(self, selector: Callable[[str, Dict[str, Any]], str]) -> None:
        """Add a custom tool selector function.
        
        Args:
            selector: Function that takes (goal, context) and returns tool name
        """
        self._tool_selectors.append(selector)
    
    def get_tool_metrics(self) -> Dict[str, Dict[str, float]]:
        """Get performance metrics for all tools."""
        return {name: tool.get_metrics() for name, tool in self._tools.items()}


# Example tool implementations

class EchoTool(Tool):
    """Simple echo tool for testing."""
    
    def __init__(self):
        super().__init__(
            name="echo",
            description="Echoes back the input text"
        )
    
    async def execute(self, text: str = "") -> ToolResult:
        """Echo the input text."""
        start_time = datetime.now()
        
        # Simulate some processing time
        await asyncio.sleep(0.1)
        
        execution_time = (datetime.now() - start_time).total_seconds()
        
        return ToolResult(
            success=True,
            result=f"Echo: {text}",
            error_message=None,
            execution_time=execution_time,
            metadata={"input_length": len(text)},
            timestamp=start_time
        )
    
    def get_schema(self) -> Dict[str, Any]:
        """Get echo tool schema."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "text": {
                        "type": "string",
                        "description": "Text to echo back"
                    }
                },
                "required": ["text"]
            }
        }


class CalculatorTool(Tool):
    """Simple calculator tool."""
    
    def __init__(self):
        super().__init__(
            name="calculator",
            description="Performs basic arithmetic calculations"
        )
    
    async def execute(self, expression: str) -> ToolResult:
        """Evaluate mathematical expression."""
        start_time = datetime.now()
        
        try:
            # Simple evaluation - in production, use safer evaluation
            result = eval(expression)
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ToolResult(
                success=True,
                result=result,
                error_message=None,
                execution_time=execution_time,
                metadata={"expression": expression},
                timestamp=start_time
            )
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return ToolResult(
                success=False,
                result=None,
                error_message=f"Calculation error: {e}",
                execution_time=execution_time,
                metadata={"expression": expression},
                timestamp=start_time
            )
    
    def get_schema(self) -> Dict[str, Any]:
        """Get calculator tool schema."""
        return {
            "name": self.name,
            "description": self.description,
            "parameters": {
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate"
                    }
                },
                "required": ["expression"]
            }
        }
