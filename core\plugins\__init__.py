"""Plugin system for M-GAIF framework.

This module provides the core plugin infrastructure for dynamic component
loading, hot-swapping, and lifecycle management.
"""

from .base import Plugin, PluginInfo, PluginStatus, HealthStatus, HealthCheckResult
from .registry import PluginRegistry
from .factory import ReflectionPluginFactory, ConfigurablePluginFactory
from .manager import ComponentManager

__all__ = [
    "Plugin",
    "PluginInfo",
    "PluginStatus",
    "HealthStatus",
    "HealthCheckResult",
    "PluginRegistry",
    "ReflectionPluginFactory",
    "ConfigurablePluginFactory",
    "ComponentManager",
]
