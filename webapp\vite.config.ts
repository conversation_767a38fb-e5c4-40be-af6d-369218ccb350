import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '')
  const API_BASE = env.VITE_API_BASE || 'http://127.0.0.1:8000'
  return {
    plugins: [react()],
    server: {
      port: 5173,
      proxy: {
        '/mcp': {
          target: API_BASE,
          changeOrigin: true,
        },
        '/v1': {
          target: API_BASE,
          changeOrigin: true,
        },
      },
    },
  }
})
