from plugins.retrievers.in_memory_retriever import In<PERSON><PERSON>ory<PERSON><PERSON>riever
from tests.realistic_data import get_document_corpus


def test_retriever_index_and_search(benchmarker):
    r = InMemoryRetriever(dim=8)
    # Use realistic document corpus
    docs = get_document_corpus(4)
    corpus = [(doc["id"], doc["content"]) for doc in docs]
    r.index(corpus)

    def _search():
        return r.search("machine learning applications", top_k=2)

    res = _search()
    assert len(res) == 2
    # Should return relevant AI/ML documents
    assert any(hit.id in {doc["id"] for doc in docs} for hit in res)

    metrics = benchmarker(_search, iterations=100)
    assert metrics["p95_ms"] < 3.0
    assert metrics["qps"] > 200.0
