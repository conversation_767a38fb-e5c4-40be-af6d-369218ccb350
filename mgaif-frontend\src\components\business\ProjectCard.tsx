import React from 'react';
import { <PERSON>, Tag, Button, Space, Typography, Tooltip } from 'antd';
import {
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  ApiOutlined,
  MessageOutlined,
  RobotOutlined,
} from '@ant-design/icons';
import type { Project } from '../../types';

interface ProjectCardProps {
  project: Project;
  onEdit: (project: Project) => void;
  onDelete: (project: Project) => void;
  onDeploy: (project: Project) => void;
}

const statusColors = {
  draft: 'default',
  active: 'processing',
  archived: 'error',
} as const;

const typeIcons = {
  workflow: <ApiOutlined />,
  chatbot: <MessageOutlined />,
  agent: <RobotOutlined />,
} as const;

const typeColors = {
  workflow: 'blue',
  chatbot: 'green',
  agent: 'purple',
} as const;

export const ProjectCard: React.FC<ProjectCardProps> = ({
  project,
  onEdit,
  onDelete,
  onDeploy,
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  return (
    <Card
      hoverable
      style={{ height: '100%' }}
      actions={[
        <Tooltip title="Edit Project">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => onEdit(project)}
          />
        </Tooltip>,
        <Tooltip title="Deploy Project">
          <Button
            type="text"
            icon={<PlayCircleOutlined />}
            onClick={() => onDeploy(project)}
          />
        </Tooltip>,
        <Tooltip title="Delete Project">
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => onDelete(project)}
          />
        </Tooltip>,
      ]}
    >
      <Card.Meta
        title={
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Typography.Text strong ellipsis style={{ maxWidth: '150px' }}>
              {project.name}
            </Typography.Text>
            <Tag color={statusColors[project.status]}>
              {project.status.toUpperCase()}
            </Tag>
          </Space>
        }
        description={
          <div>
            <Typography.Paragraph
              ellipsis={{ rows: 2 }}
              style={{ marginBottom: '12px', minHeight: '40px' }}
            >
              {project.description}
            </Typography.Paragraph>
            
            <Space style={{ width: '100%', justifyContent: 'space-between' }}>
              <Tag color={typeColors[project.type]} icon={typeIcons[project.type]}>
                {project.type.charAt(0).toUpperCase() + project.type.slice(1)}
              </Tag>
              <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                {formatDate(project.updatedAt)}
              </Typography.Text>
            </Space>
          </div>
        }
      />
    </Card>
  );
};
