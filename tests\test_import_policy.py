"""Test import policy and circular dependency detection."""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Set


def test_no_circular_imports():
    """Test that there are no circular imports in the codebase."""
    
    def get_imports(file_path: Path) -> List[str]:
        """Extract import statements from a Python file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    if node.module:
                        imports.append(node.module)
            
            return imports
        except (SyntaxError, UnicodeDecodeError):
            return []
    
    def build_dependency_graph() -> Dict[str, Set[str]]:
        """Build a dependency graph of all Python modules."""
        graph = {}
        project_root = Path(__file__).parent.parent
        
        # Find all Python files in core/ and plugins/
        for pattern in ["core/**/*.py", "plugins/**/*.py"]:
            for py_file in project_root.glob(pattern):
                if py_file.name == "__init__.py":
                    continue
                
                # Convert file path to module name
                relative_path = py_file.relative_to(project_root)
                module_name = str(relative_path.with_suffix('')).replace(os.sep, '.')
                
                # Get imports for this module
                imports = get_imports(py_file)
                
                # Filter to only include project modules
                project_imports = set()
                for imp in imports:
                    if imp.startswith('core.') or imp.startswith('plugins.'):
                        project_imports.add(imp)
                
                graph[module_name] = project_imports
        
        return graph
    
    def detect_cycles(graph: Dict[str, Set[str]]) -> List[List[str]]:
        """Detect cycles in the dependency graph using DFS."""
        visited = set()
        rec_stack = set()
        cycles = []
        
        def dfs(node: str, path: List[str]) -> bool:
            if node in rec_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                cycles.append(cycle)
                return True
            
            if node in visited:
                return False
            
            visited.add(node)
            rec_stack.add(node)
            
            for neighbor in graph.get(node, set()):
                if neighbor in graph:  # Only follow edges to nodes we know about
                    dfs(neighbor, path + [node])
            
            rec_stack.remove(node)
            return False
        
        for node in graph:
            if node not in visited:
                dfs(node, [])
        
        return cycles
    
    # Build dependency graph and check for cycles
    graph = build_dependency_graph()
    cycles = detect_cycles(graph)
    
    if cycles:
        cycle_descriptions = []
        for cycle in cycles:
            cycle_descriptions.append(" -> ".join(cycle))
        
        assert False, f"Circular imports detected:\n" + "\n".join(cycle_descriptions)


def test_core_contracts_importable():
    """Test that core contracts can be imported without side effects."""
    
    # These should be importable without executing any side effects
    contract_modules = [
        "core.contracts.llm",
        "core.contracts.retrieval",
        "core.text.tokenizer",
        "core.text.embeddings",
        "core.adapters.base",
    ]
    
    for module_name in contract_modules:
        try:
            __import__(module_name)
        except ImportError as e:
            assert False, f"Failed to import {module_name}: {e}"


def test_plugins_can_import_contracts():
    """Test that plugin modules can import core contracts."""
    
    # Test that plugins can import contracts without circular dependencies
    plugin_contract_pairs = [
        ("plugins.tokenizers.simple_tokenizer", "core.text.tokenizer"),
        ("plugins.embedders.simple_embedder", "core.text.embeddings"),
        ("plugins.retrievers.in_memory_retriever", "core.contracts.retrieval"),
    ]
    
    for plugin_module, contract_module in plugin_contract_pairs:
        try:
            # Import contract first
            __import__(contract_module)
            # Then import plugin
            __import__(plugin_module)
        except ImportError as e:
            assert False, f"Failed to import {plugin_module} with {contract_module}: {e}"


def test_no_star_imports():
    """Test that there are no star imports (from module import *)."""
    
    def has_star_imports(file_path: Path) -> List[str]:
        """Check if a file has star imports."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            star_imports = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ImportFrom):
                    for alias in node.names:
                        if alias.name == '*':
                            star_imports.append(f"from {node.module} import *")
            
            return star_imports
        except (SyntaxError, UnicodeDecodeError):
            return []
    
    project_root = Path(__file__).parent.parent
    star_import_files = []
    
    # Check all Python files
    for pattern in ["core/**/*.py", "plugins/**/*.py", "tests/**/*.py"]:
        for py_file in project_root.glob(pattern):
            star_imports = has_star_imports(py_file)
            if star_imports:
                star_import_files.append((str(py_file), star_imports))
    
    if star_import_files:
        error_msg = "Star imports found (these can cause namespace pollution):\n"
        for file_path, imports in star_import_files:
            error_msg += f"  {file_path}:\n"
            for imp in imports:
                error_msg += f"    {imp}\n"
        
        assert False, error_msg


def test_module_structure():
    """Test that the module structure follows the expected pattern."""
    
    project_root = Path(__file__).parent.parent
    
    # Check that required directories exist
    required_dirs = [
        "core",
        "core/contracts",
        "core/adapters", 
        "core/workflow",
        "core/edge",
        "core/mcp",
        "core/security",
        "plugins",
        "plugins/tokenizers",
        "plugins/embedders",
        "plugins/retrievers",
        "tests",
    ]
    
    for dir_path in required_dirs:
        full_path = project_root / dir_path
        assert full_path.exists(), f"Required directory {dir_path} does not exist"
        
        # Check that __init__.py exists in Python packages
        init_file = full_path / "__init__.py"
        assert init_file.exists(), f"Missing __init__.py in {dir_path}"


def test_no_print_statements():
    """Test that there are no print statements in production code."""
    
    def has_print_statements(file_path: Path) -> List[int]:
        """Check if a file has print statements."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print_lines = []
            for i, line in enumerate(lines, 1):
                # Skip comments and docstrings
                stripped = line.strip()
                if stripped.startswith('#') or stripped.startswith('"""') or stripped.startswith("'''"):
                    continue
                
                # Look for print statements (but allow in test files)
                if 'print(' in line and not file_path.name.startswith('test_'):
                    print_lines.append(i)
            
            return print_lines
        except (UnicodeDecodeError):
            return []
    
    project_root = Path(__file__).parent.parent
    print_files = []
    
    # Check core and plugins (but not tests)
    for pattern in ["core/**/*.py", "plugins/**/*.py"]:
        for py_file in project_root.glob(pattern):
            if py_file.name.startswith('test_'):
                continue
                
            print_lines = has_print_statements(py_file)
            if print_lines:
                print_files.append((str(py_file), print_lines))
    
    if print_files:
        error_msg = "Print statements found in production code (use logging instead):\n"
        for file_path, lines in print_files:
            error_msg += f"  {file_path}: lines {', '.join(map(str, lines))}\n"
        
        # This is a warning, not a hard failure for now
        print(f"WARNING: {error_msg}")
