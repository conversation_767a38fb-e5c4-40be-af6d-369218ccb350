"""OpenAI-compatible LLM adapters for testing and development.

This module provides LLM adapters that are compatible with OpenAI's chat completion
format. It includes a deterministic echo adapter for testing and development
purposes, ensuring predictable behavior in test environments.

The module contains:
- EchoAdapter: Deterministic testing adapter that echoes user input
- OpenAI-compatible response formatting
- Streaming and non-streaming support
- Proper token usage tracking

Key Features:
- Deterministic responses for testing
- Streaming word-by-word output
- OpenAI-compatible message formats
- Proper finish reason handling
- Token usage estimation

Example:
    >>> adapter = EchoAdapter()
    >>> request = ChatCompletionRequest(
    ...     messages=[ChatMessage(role="user", content="Hello!")]
    ... )
    >>> response = await adapter.chat(request)
    >>> print(response.choices[0].message.content)  # "Hello!"

Note:
    The EchoAdapter is designed for testing and should not be used in production.
    It provides deterministic responses by echoing the last user message.
"""

from __future__ import annotations

import asyncio
import time
import uuid
from typing import AsyncIterator

from .base import BaseModelAdapter
from ..contracts.llm import (
    ChatCompletionChunk,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatChoice,
    ChatChunkChoice,
    ChatMessage,
    DeltaMessage,
    Usage,
)


class EchoAdapter(BaseModelAdapter):
    """Deterministic stub adapter that echoes the last user message.

    This is suitable for development and test of API/streaming paths.
    """

    name = "echo"

    async def chat(self, request: ChatCompletionRequest) -> ChatCompletionResponse:
        last_user = next((m for m in reversed(request.messages) if m.role == "user"), None)
        content = last_user.content if last_user else ""
        message = ChatMessage(role="assistant", content=content)
        now = int(time.time())
        return ChatCompletionResponse(
            id=str(uuid.uuid4()),
            created=now,
            model=request.model,
            choices=[ChatChoice(index=0, message=message, finish_reason="stop")],
            usage=Usage(prompt_tokens=len(request.messages), completion_tokens=len(content), total_tokens=len(request.messages) + len(content)),
        )

    async def chat_stream(
        self, request: ChatCompletionRequest
    ) -> AsyncIterator[ChatCompletionChunk]:
        last_user = next((m for m in reversed(request.messages) if m.role == "user"), None)
        content = last_user.content if last_user else ""
        now = int(time.time())
        chunk_id = str(uuid.uuid4())
        # initial role chunk
        yield ChatCompletionChunk(
            id=chunk_id,
            created=now,
            model=request.model,
            choices=[ChatChunkChoice(index=0, delta=DeltaMessage(role="assistant"), finish_reason=None)],
        )
        # stream content by word
        for word in content.split():
            await asyncio.sleep(0)  # let loop breathe
            yield ChatCompletionChunk(
                id=chunk_id,
                created=now,
                model=request.model,
                choices=[ChatChunkChoice(index=0, delta=DeltaMessage(content=word + " "), finish_reason=None)],
            )
        # final finish chunk
        yield ChatCompletionChunk(
            id=chunk_id,
            created=now,
            model=request.model,
            choices=[ChatChunkChoice(index=0, delta=DeltaMessage(), finish_reason="stop")],
        )
