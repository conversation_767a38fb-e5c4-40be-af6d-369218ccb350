"""Security module for M-GAIF framework.

This module provides comprehensive security features including authentication,
authorization, threat detection, and audit logging.
"""

from .auth import (
    AuthManager, JWTAuthProvider, User, UserRole, Permission,
    AuthToken, AuthenticationError, AuthorizationError
)
from .threat_detection import (
    ThreatDetectionEngine, ThreatDetector, ThreatDetection,
    ThreatLevel, ThreatType, PromptInjectionDetector, PIIDetector
)
from .audit import AuditLogger, AuditEvent, AuditEventType, AuditLevel, CorrelationContext

__all__ = [
    # Authentication and authorization
    "AuthManager",
    "JWTAuthProvider",
    "User",
    "UserRole",
    "Permission",
    "AuthToken",
    "AuthenticationError",
    "AuthorizationError",

    # Threat detection
    "ThreatDetectionEngine",
    "ThreatDetector",
    "ThreatDetection",
    "ThreatLevel",
    "ThreatType",
    "PromptInjectionDetector",
    "PIIDetector",

    # Audit logging
    "AuditLogger",
    "AuditEvent",
    "AuditEventType",
    "AuditLevel",
    "CorrelationContext",
]
