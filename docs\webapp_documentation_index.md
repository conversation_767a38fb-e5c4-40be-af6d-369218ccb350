# M-GAIF Web Console Documentation

## 📚 Complete Documentation Suite

Welcome to the comprehensive documentation for the M-GAIF Web Console - a professional React + TypeScript interface for the Modular Generative AI Framework.

## 🚀 Getting Started

### For End Users

**[Quick Start Guide](webapp_quick_start.md)** ⭐ *Start here!*
- 5-minute setup guide
- Essential workflows walkthrough
- Common tasks and troubleshooting
- Perfect for first-time users

**[Complete User Guide](webapp_user_guide.md)**
- Comprehensive feature documentation
- Advanced usage patterns
- Production deployment guide
- Security and performance considerations

### For Developers

**[API Reference](webapp_api_reference.md)**
- TypeScript API client documentation
- Complete method signatures and examples
- Error handling and configuration
- Testing and performance optimization

**[Developer README](../webapp/README.md)**
- Technical setup and build instructions
- Project structure and architecture
- Development workflow and best practices

## 📖 Documentation Overview

### 1. Quick Start Guide
**File:** `webapp_quick_start.md`
**Audience:** New users, evaluators
**Content:**
- 5-minute setup process
- Essential workflow examples
- Key feature overview
- Basic troubleshooting

**When to use:** First time using the console, need quick demo

### 2. Complete User Guide
**File:** `webapp_user_guide.md`
**Audience:** Regular users, administrators
**Content:**
- Detailed feature documentation
- Advanced configuration options
- Production deployment strategies
- Security and performance guidance

**When to use:** Daily usage, production setup, advanced features

### 3. API Reference
**File:** `webapp_api_reference.md`
**Audience:** Frontend developers, integrators
**Content:**
- TypeScript API client methods
- Type definitions and interfaces
- Error handling patterns
- Testing and mocking strategies

**When to use:** Building integrations, debugging API issues, extending functionality

### 4. Developer README
**File:** `../webapp/README.md`
**Audience:** Frontend developers, contributors
**Content:**
- Project setup and dependencies
- Build and deployment processes
- Code structure and conventions
- Development best practices

**When to use:** Contributing code, setting up development environment

## 🎯 Use Case Navigation

### I want to...

**Try the console for the first time**
→ [Quick Start Guide](webapp_quick_start.md)

**Learn all features in detail**
→ [Complete User Guide](webapp_user_guide.md)

**Deploy to production**
→ [User Guide - Deployment Section](webapp_user_guide.md#deployment)

**Integrate with the API**
→ [API Reference](webapp_api_reference.md)

**Contribute to development**
→ [Developer README](../webapp/README.md)

**Troubleshoot issues**
→ [Quick Start - Troubleshooting](webapp_quick_start.md#troubleshooting)
→ [User Guide - Troubleshooting](webapp_user_guide.md#troubleshooting)

**Understand the architecture**
→ [User Guide - Architecture](webapp_user_guide.md#application-architecture)
→ [API Reference - Configuration](webapp_api_reference.md#configuration-management)

## 🔧 Feature Documentation Map

### Core Features

| Feature | Quick Start | User Guide | API Reference |
|---------|-------------|------------|---------------|
| **Dashboard** | ✅ Overview | ✅ Detailed | ➖ N/A |
| **Workflows** | ✅ First workflow | ✅ YAML guide | ✅ `workflowRun()` |
| **RAG Stores** | ✅ Index & search | ✅ Advanced usage | ✅ `retrieverIndex()`, `retrieverSearch()` |
| **LLMs** | ✅ Basic testing | ✅ Dual-mode setup | ✅ `mcpChat()`, `edgeChat()`, streaming |
| **Settings** | ✅ Basic config | ✅ Production setup | ✅ Configuration management |

### Advanced Topics

| Topic | Documentation Location |
|-------|----------------------|
| **Custom Workflows** | [User Guide - Advanced Usage](webapp_user_guide.md#advanced-usage) |
| **Production Deployment** | [User Guide - Deployment](webapp_user_guide.md#deployment) |
| **API Integration** | [API Reference](webapp_api_reference.md) |
| **Performance Optimization** | [User Guide - Performance](webapp_user_guide.md#performance-optimization) |
| **Security Configuration** | [User Guide - Security](webapp_user_guide.md#security-considerations) |
| **Development Setup** | [Developer README](../webapp/README.md) |

## 🛠️ Technical Reference

### Architecture Components

- **Frontend**: React 18 + TypeScript + Ant Design
- **Build Tool**: Vite with hot module replacement
- **API Client**: Axios with streaming support
- **Routing**: React Router for SPA navigation
- **State**: React hooks and local state

### API Endpoints

- **MCP API**: `/mcp/tools/*` - Tool-based interface
- **Edge API**: `/v1/chat/completions` - OpenAI-compatible

### Key Technologies

- **TypeScript**: Type-safe development
- **React Router**: Client-side routing
- **Ant Design**: Professional UI components
- **Axios**: HTTP client with interceptors
- **Vite**: Fast build tool and dev server

## 📋 Checklists

### New User Checklist

- [ ] Read [Quick Start Guide](webapp_quick_start.md)
- [ ] Start backend services
- [ ] Launch web console
- [ ] Try first RAG workflow
- [ ] Test document indexing and search
- [ ] Experiment with LLM interactions

### Developer Checklist

- [ ] Review [Developer README](../webapp/README.md)
- [ ] Set up development environment
- [ ] Study [API Reference](webapp_api_reference.md)
- [ ] Understand project structure
- [ ] Run tests and linting
- [ ] Review code quality guidelines

### Production Deployment Checklist

- [ ] Read [Deployment Guide](webapp_user_guide.md#deployment)
- [ ] Build optimized production bundle
- [ ] Configure web server (Nginx/Apache)
- [ ] Set up API proxying
- [ ] Configure CORS for backend
- [ ] Test all features in production
- [ ] Set up monitoring and logging

## 🆘 Support and Troubleshooting

### Common Issues

1. **Connection Problems**
   - Check backend is running
   - Verify API configuration in Settings
   - Review CORS settings

2. **Workflow Errors**
   - Validate YAML syntax
   - Check node ID uniqueness
   - Verify handler parameters

3. **Performance Issues**
   - Monitor browser console
   - Check network requests
   - Review bundle size

### Getting Help

1. **Check Documentation**: Start with relevant guide above
2. **Browser Console**: Look for JavaScript errors
3. **Network Tab**: Monitor API requests and responses
4. **Backend Logs**: Check server-side error messages

## 🔄 Documentation Updates

This documentation is actively maintained. For the latest updates:

1. Check the main repository for new releases
2. Review commit history for documentation changes
3. Submit issues for documentation improvements
4. Contribute updates via pull requests

---

**Last Updated:** 2025-01-18
**Version:** 1.0.0
**Maintainer:** M-GAIF Development Team

## 📞 Quick Links

- **[🚀 Quick Start](webapp_quick_start.md)** - Get started in 5 minutes
- **[📖 User Guide](webapp_user_guide.md)** - Complete feature documentation  
- **[🔧 API Reference](webapp_api_reference.md)** - Developer API documentation
- **[💻 Developer README](../webapp/README.md)** - Technical setup guide

*Choose your path and start exploring the M-GAIF Web Console!*
