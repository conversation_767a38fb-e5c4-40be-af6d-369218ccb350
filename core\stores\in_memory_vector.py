"""In-memory vector storage implementation.

This module provides a simple in-memory vector store for development and testing
purposes. It implements basic vector similarity search using cosine similarity
and maintains all vectors in memory for fast access.

Features:
- In-memory vector storage
- Cosine similarity search
- Batch vector operations
- Metadata preservation
- Simple add/search interface

Performance Characteristics:
- O(n) search complexity (linear scan)
- O(1) insertion complexity
- Memory usage scales with vector count
- No persistence between restarts

Example:
    >>> store = InMemoryVectorStore()
    >>> records = [
    ...     VectorRecord(id="doc1", vector=[0.1, 0.2], metadata={"title": "Doc 1"}),
    ...     VectorRecord(id="doc2", vector=[0.3, 0.4], metadata={"title": "Doc 2"})
    ... ]
    >>> store.add(records)
    >>> results = store.search([0.15, 0.25], top_k=1)

Note:
    This implementation is suitable for development and small datasets only.
    For production use, consider dedicated vector databases like Pinecone or Weaviate.
"""

from __future__ import annotations

from typing import List, Sequence, Tuple

from ..contracts.retrieval import SearchResult, VectorRecord


def _cosine(a: Sequence[float], b: Sequence[float]) -> float:
    num = sum(x * y for x, y in zip(a, b))
    da = sum(x * x for x in a) ** 0.5 or 1.0
    db = sum(y * y for y in b) ** 0.5 or 1.0
    return num / (da * db)


class InMemoryVectorStore:
    """Simple in-memory list-backed vector store for development/testing."""

    def __init__(self) -> None:
        self._records: List[VectorRecord] = []

    def add(self, records: List[VectorRecord]) -> None:
        self._records.extend(records)

    def search(self, query: Sequence[float], top_k: int = 5) -> List[SearchResult]:
        scored: List[Tuple[float, VectorRecord]] = [
            (_cosine(query, r.vector), r) for r in self._records
        ]
        scored.sort(key=lambda t: t[0], reverse=True)
        results: List[SearchResult] = []
        for score, rec in scored[:top_k]:
            results.append(
                SearchResult(id=rec.id, score=float(score), metadata=rec.metadata, record=rec)
            )
        return results
