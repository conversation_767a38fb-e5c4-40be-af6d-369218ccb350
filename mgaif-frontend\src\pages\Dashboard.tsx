import React, { useEffect } from 'react';
import {
  <PERSON>po<PERSON>,
  Row,
  Col,
  Button,
  Space,
  Spin,
  Empty,
  message,
  Card,
  Statistic,
} from 'antd';
import { PlusOutlined, RocketOutlined, MessageOutlined, RobotOutlined } from '@ant-design/icons';
import { useProjectStore } from '../stores/projectStore';
import { ProjectCard } from '../components/business/ProjectCard';

export const Dashboard: React.FC = () => {
  const { projects, isLoading, fetchProjects, createProject } = useProjectStore();

  useEffect(() => {
    // Load projects on mount
    fetchProjects().catch(() => {
      // Use mock data if API fails
      console.log('Using mock data for development');
    });
  }, [fetchProjects]);

  const handleCreateProject = async () => {
    try {
      await createProject({
        name: 'New Project',
        description: 'A new AI project',
        type: 'workflow',
        status: 'draft',
      });
      message.success('Project created successfully!');
    } catch (error) {
      message.error('Failed to create project');
    }
  };

  const handleEditProject = (project: any) => {
    message.info(`Edit project: ${project.name}`);
  };

  const handleDeleteProject = (project: any) => {
    message.info(`Delete project: ${project.name}`);
  };

  const handleDeployProject = (project: any) => {
    message.info(`Deploy project: ${project.name}`);
  };

  // Mock statistics
  const stats = {
    totalProjects: projects.length,
    activeProjects: projects.filter(p => p.status === 'active').length,
    chatbots: projects.filter(p => p.type === 'chatbot').length,
    agents: projects.filter(p => p.type === 'agent').length,
  };

  if (isLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <Typography.Paragraph style={{ marginTop: '16px' }}>
          Loading your projects...
        </Typography.Paragraph>
      </div>
    );
  }

  return (
    <div>
      {/* Header */}
      <div style={{ marginBottom: '32px' }}>
        <Space align="start" style={{ width: '100%', justifyContent: 'space-between' }}>
          <div>
            <Typography.Title level={2} style={{ margin: 0 }}>
              Welcome to M-GAIF
            </Typography.Title>
            <Typography.Paragraph type="secondary" style={{ marginBottom: 0 }}>
              Build AI applications without code using our visual tools
            </Typography.Paragraph>
          </div>
          <Button
            type="primary"
            size="large"
            icon={<PlusOutlined />}
            onClick={handleCreateProject}
          >
            Create Project
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '32px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Projects"
              value={stats.totalProjects}
              prefix={<RocketOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Projects"
              value={stats.activeProjects}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Chatbots"
              value={stats.chatbots}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="AI Agents"
              value={stats.agents}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* Projects Section */}
      <div style={{ marginBottom: '24px' }}>
        <Typography.Title level={3}>Recent Projects</Typography.Title>
      </div>

      {projects.length === 0 ? (
        <Empty
          description="No projects yet"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={handleCreateProject}>
            Create Your First Project
          </Button>
        </Empty>
      ) : (
        <Row gutter={[16, 16]}>
          {projects.slice(0, 8).map((project) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={project.id}>
              <ProjectCard
                project={project}
                onEdit={handleEditProject}
                onDelete={handleDeleteProject}
                onDeploy={handleDeployProject}
              />
            </Col>
          ))}
        </Row>
      )}

      {projects.length > 8 && (
        <div style={{ textAlign: 'center', marginTop: '24px' }}>
          <Button type="link">View All Projects</Button>
        </div>
      )}
    </div>
  );
};
