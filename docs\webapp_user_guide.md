# M-GAIF Web Console User Guide

## Overview

The M-GAIF Web Console is a professional React + TypeScript web application that provides a no-code interface for configuring and orchestrating the Modular Generative AI Framework. It offers a comprehensive dashboard for managing workflows, RAG stores, LLMs, agents, and execution runs.

## Getting Started

### Prerequisites

- **Backend Services**: Ensure the M-GAIF backend is running
  - MCP API: `uvicorn core.mcp.api:app --reload` (default: http://127.0.0.1:8000)
  - Edge API: `uvicorn core.edge.api:app --reload` (default: http://127.0.0.1:8000)
- **Node.js**: Version 18+ required for frontend development

### Starting the Web Console

1. **Development Mode**:
   ```bash
   cd webapp
   npm install
   npm run dev
   ```
   Access at: http://localhost:5173

2. **Production Build**:
   ```bash
   npm run build
   npm run preview
   ```

## Application Architecture

### Navigation Structure

The console features a responsive sidebar navigation with the following sections:

- **Dashboard**: Overview and quick access to main features
- **Workflows**: YAML workflow builder and executor
- **RAG Stores**: Document indexing and search interface
- **LLMs**: Language model testing and streaming interface
- **Agents**: Agent configuration (coming soon)
- **Runs**: Execution history and monitoring
- **Settings**: API configuration and preferences

### API Integration

The console communicates with the backend through two main APIs:

1. **MCP API** (`/mcp/tools/*`): Tool-based interface for specific operations
2. **Edge API** (`/v1/chat/completions`): OpenAI-compatible chat interface

## Feature Guide

### 1. Dashboard

**Purpose**: Central hub providing quick access to main features

**Features**:
- Welcome message and framework overview
- Quick navigation cards to Workflows, RAG Stores, and LLMs
- Responsive grid layout adapting to screen size

**Usage**:
- Click any card to navigate to the corresponding feature
- Use as a starting point for new users

### 2. Workflows

**Purpose**: Visual workflow builder and executor using YAML specifications

**Key Features**:
- **Template Library**: Pre-built workflow templates
  - RAG Example: Basic retrieval workflow
  - RAG + LLM (echo): Retrieval with language model generation
- **YAML Editor**: Live editing of workflow specifications
- **Execution Engine**: Run workflows and view results
- **State Visualization**: JSON output of workflow execution state

**Workflow Components**:
- **Nodes**: Individual processing steps (set, retrieve, call_llm, noop)
- **Edges**: Connections between nodes defining execution flow
- **Handlers**: Built-in processors for different operations

**Usage Example**:
1. Select a template from the Templates tab
2. Modify the YAML in the Editor section
3. Click "Run" to execute the workflow
4. View results in the Output state panel

**Supported Handlers**:
- `set`: Set state variables
- `retrieve`: Perform RAG document retrieval
- `call_llm`: Invoke language model
- `noop`: No operation (useful for workflow termination)

### 3. RAG Stores

**Purpose**: Document indexing and semantic search interface

**Key Features**:
- **Bulk Document Indexing**: Add multiple documents via text input
- **Real-time Search**: Query indexed documents with similarity scoring
- **Document Management**: View indexed documents with IDs
- **Configurable Results**: Adjust top-k parameter for search results

**Workflow**:
1. **Index Documents**:
   - Enter documents in the bulk text area (one per line)
   - Click "Index" to add to the vector store
   - View indexed documents in the list below

2. **Search Documents**:
   - Enter search query
   - Set top_k parameter (default: 3)
   - Click "Search" to find similar documents
   - View results with similarity scores

**Technical Details**:
- Uses SimpleEmbedder for deterministic hash-based embeddings
- InMemoryVectorStore for cosine similarity search
- Real-time indexing and search via MCP API

### 4. LLMs

**Purpose**: Language model testing and streaming interface

**Key Features**:
- **Dual Backend Support**:
  - Edge API: OpenAI-compatible interface
  - MCP API: Tool-based LLM interface
- **Streaming Support**: Real-time response streaming (Edge API only)
- **Model Configuration**: Specify model names for Edge API
- **Interactive Testing**: Send prompts and view responses

**Configuration Options**:
- **Backend Mode**: Choose between Edge or MCP API
- **Model**: Specify model name (for Edge API, e.g., "llama3")
- **Streaming**: Enable/disable real-time response streaming
- **Prompt**: Multi-line text input for user messages

**Usage**:
1. Select backend mode (Edge recommended for streaming)
2. Configure model name if using Edge API
3. Enter your prompt in the text area
4. Enable streaming for real-time responses
5. Click "Send" to get LLM response

### 5. Settings

**Purpose**: Configure API endpoints and application preferences

**Key Features**:
- **API Base Configuration**: Set custom backend URL
- **Local Storage**: Persistent settings across sessions
- **Proxy Support**: Automatic proxy detection for development

**Configuration**:
- **API Base URL**: Full URL to backend (e.g., http://127.0.0.1:8000)
- **Clear Option**: Reset to default proxy configuration
- **Auto-save**: Settings persist in browser local storage

**Common Configurations**:
- Development: Leave empty (uses Vite proxy)
- Local Backend: `http://127.0.0.1:8000`
- Remote Backend: `https://your-domain.com`

### 6. Agents (Coming Soon)

**Purpose**: Future agent configuration and management

**Planned Features**:
- Agent skill definition
- Tool and policy configuration
- Conversation starter templates
- Handoff workflow design

### 7. Runs

**Purpose**: Execution history and monitoring (placeholder)

**Current Features**:
- Mock run generation for UI testing
- Run status tracking
- Timestamp recording

**Future Features**:
- Real workflow execution history
- Performance metrics
- Error logging and debugging
- Execution replay

## Advanced Usage

### Custom Workflow Development

**YAML Structure**:
```yaml
name: workflow_name
nodes:
  - id: unique_node_id
    handler: handler_type
    params:
      key: value
edges:
  - from: source_node_id
    to: target_node_id
```

**Handler Parameters**:

1. **set**: Set state variables
   ```yaml
   params:
     variable_name: value
     another_var: "string value"
   ```

2. **retrieve**: RAG document retrieval
   ```yaml
   params:
     doc_state_key: docs        # State key containing documents
     query_key: query           # State key containing search query
     result_key: hits           # Output key for results
     result_mode: texts         # Return format (texts/ids)
     top_k: 3                   # Number of results
   ```

3. **call_llm**: Language model invocation
   ```yaml
   params:
     adapter: echo              # Adapter type (echo/ollama)
     input_key: prompt          # Input prompt state key
     context_key: context       # Optional context state key
     output_key: response       # Output state key
     model: llama3              # Model name (for ollama)
     max_tokens: 100            # Token limit
   ```

### API Integration

**Direct API Calls**:

The console uses the ApiClient class for backend communication:

```typescript
import { api } from './api/client'

// Workflow execution
const result = await api.workflowRun(spec, initialState)

// Document indexing
await api.retrieverIndex([{id: 'd1', text: 'content'}])

// Document search
const hits = await api.retrieverSearch('query', 3)

// LLM chat (MCP)
const response = await api.mcpChat(messages)

// LLM chat (Edge)
const response = await api.edgeChat(messages, 'llama3')

// Streaming chat
await api.edgeChatStream(messages, (delta) => {
  console.log(delta) // Handle streaming response
}, 'llama3')
```

## Troubleshooting

### Common Issues

1. **Backend Connection Failed**:
   - Verify backend services are running
   - Check API base URL in Settings
   - Ensure CORS is properly configured

2. **Workflow Execution Errors**:
   - Validate YAML syntax
   - Check node IDs are unique
   - Verify handler parameters
   - Ensure proper edge connections

3. **RAG Search Returns No Results**:
   - Verify documents are indexed
   - Check query spelling and relevance
   - Adjust top_k parameter

4. **LLM Streaming Not Working**:
   - Use Edge API mode for streaming
   - Verify model name is correct
   - Check network connectivity

### Debug Mode

Enable browser developer tools to:
- Monitor network requests
- View console errors
- Inspect API responses
- Debug workflow execution

### Performance Optimization

- Use appropriate top_k values for RAG search
- Limit workflow complexity for better performance
- Monitor browser memory usage for large document sets
- Use streaming for long LLM responses

## Security Considerations

- API endpoints should be secured in production
- Validate all user inputs before processing
- Use HTTPS for production deployments
- Implement proper authentication and authorization
- Sanitize workflow YAML to prevent injection attacks

## Browser Compatibility

**Supported Browsers**:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

**Required Features**:
- ES2020 support
- Fetch API
- Local Storage
- CSS Grid and Flexbox

## Deployment

### Production Build

```bash
cd webapp
npm run build
```

This creates an optimized build in `dist/` directory with:
- Minified JavaScript and CSS
- Tree-shaken dependencies
- Optimized assets
- Source maps for debugging

### Static Hosting Options

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/webapp/dist;
    index index.html;

    # Handle SPA routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API proxy to backend
    location /mcp/ {
        proxy_pass http://127.0.0.1:8000/mcp/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /v1/ {
        proxy_pass http://127.0.0.1:8000/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

**Apache Configuration:**
```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /path/to/webapp/dist

    # Handle SPA routing
    <Directory "/path/to/webapp/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # API proxy
    ProxyPass /mcp/ http://127.0.0.1:8000/mcp/
    ProxyPass /v1/ http://127.0.0.1:8000/v1/
</VirtualHost>
```

**Docker Deployment:**
```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### Environment Configuration

**Production Environment Variables:**
```bash
# Set at build time
VITE_API_BASE=https://api.your-domain.com
VITE_APP_TITLE="M-GAIF Console"
VITE_APP_VERSION="1.0.0"
```

**Runtime Configuration:**
- Use Settings page to configure API endpoints
- Settings persist in browser localStorage
- No server-side configuration required

### CDN Deployment

For global distribution, deploy to CDN services:

**Vercel:**
```bash
npm install -g vercel
vercel --prod
```

**Netlify:**
```bash
npm run build
# Upload dist/ folder to Netlify
```

**AWS S3 + CloudFront:**
```bash
aws s3 sync dist/ s3://your-bucket-name/
aws cloudfront create-invalidation --distribution-id YOUR_DIST_ID --paths "/*"
```

## Development

### Project Structure

```
webapp/
├── src/
│   ├── api/          # API client and types
│   ├── pages/        # Page components
│   ├── App.tsx       # Main application
│   └── main.tsx      # Entry point
├── package.json      # Dependencies
├── vite.config.ts    # Build configuration
└── nginx.conf        # Production server config
```

### Development Workflow

1. **Local Development:**
   ```bash
   npm run dev
   ```
   - Hot module replacement
   - Automatic proxy to backend
   - TypeScript type checking

2. **Type Checking:**
   ```bash
   npm run type-check
   ```

3. **Linting:**
   ```bash
   npm run lint
   ```

4. **Testing:**
   ```bash
   npm run test
   ```

### Adding New Features

1. Create new page component in `src/pages/`
2. Add route to `App.tsx` routes array
3. Update navigation menu in `App.tsx`
4. Implement API integration in `src/api/client.ts`
5. Add proper TypeScript types
6. Write tests for new functionality
7. Update documentation

### Code Quality

**TypeScript Configuration:**
- Strict mode enabled
- Path mapping for clean imports
- Type checking for all files

**ESLint Rules:**
- React hooks rules
- TypeScript recommended rules
- Import/export validation

**Prettier Formatting:**
- Consistent code style
- Automatic formatting on save

### Performance Optimization

**Bundle Analysis:**
```bash
npm run build
npm run analyze
```

**Optimization Techniques:**
- Route-based code splitting
- Tree shaking for unused code
- Asset optimization and compression
- Lazy loading for heavy components

### Building for Production

```bash
npm run build
```

Output includes:
- Minified and optimized JavaScript
- CSS extraction and minification
- Asset fingerprinting for caching
- Source maps for debugging
- Bundle analysis report
