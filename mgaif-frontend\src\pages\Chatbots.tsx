import React, { useState, useEffect } from 'react';
import {
  Typography,
  Button,
  Space,
  Card,
  Row,
  Col,
  Tag,
  Dropdown,
  Modal,
  message,
  Empty,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  CopyOutlined,
  MoreOutlined,
  RobotOutlined,
  MessageOutlined,
  UserOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import { useChatbotStore } from '../stores/chatbotStore';
import { ChatbotBuilder } from '../components/chatbot/ChatbotBuilder';
import { ChatInterface } from '../components/chatbot/ChatInterface';
import type { ChatbotConfig } from '../types/chatbot';

export const Chatbots: React.FC = () => {
  const {
    chatbots,
    currentChatbot,
    deleteChatbot,
    duplicateChatbot,
    setCurrentChatbot,
    resetBuilder
  } = useChatbotStore();

  const [showBuilder, setShowBuilder] = useState(false);
  const [showTestChat, setShowTestChat] = useState(false);
  const [selectedChatbot, setSelectedChatbot] = useState<ChatbotConfig | null>(null);

  useEffect(() => {
    // Reset builder when component mounts
    resetBuilder();
  }, [resetBuilder]);

  const handleCreateNew = () => {
    setSelectedChatbot(null);
    setCurrentChatbot(null);
    setShowBuilder(true);
  };

  const handleEdit = (chatbot: ChatbotConfig) => {
    setSelectedChatbot(chatbot);
    setCurrentChatbot(chatbot);
    setShowBuilder(true);
  };

  const handleTest = (chatbot: ChatbotConfig) => {
    setSelectedChatbot(chatbot);
    setCurrentChatbot(chatbot);
    setShowTestChat(true);
  };

  const handleDelete = async (chatbot: ChatbotConfig) => {
    Modal.confirm({
      title: 'Delete Chatbot',
      content: `Are you sure you want to delete "${chatbot.name}"? This action cannot be undone.`,
      okText: 'Delete',
      okType: 'danger',
      onOk: async () => {
        try {
          await deleteChatbot(chatbot.id);
          message.success('Chatbot deleted successfully');
        } catch (error) {
          message.error('Failed to delete chatbot');
        }
      },
    });
  };

  const handleDuplicate = async (chatbot: ChatbotConfig) => {
    try {
      await duplicateChatbot(chatbot.id);
      message.success('Chatbot duplicated successfully');
    } catch (error) {
      message.error('Failed to duplicate chatbot');
    }
  };

  const handleBuilderComplete = (chatbotId: string) => {
    setShowBuilder(false);
    message.success('Chatbot setup completed!');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'success';
      case 'draft': return 'default';
      case 'training': return 'processing';
      case 'inactive': return 'error';
      default: return 'default';
    }
  };

  const getMenuItems = (chatbot: ChatbotConfig) => [
    {
      key: 'edit',
      icon: <EditOutlined />,
      label: 'Edit',
      onClick: () => handleEdit(chatbot),
    },
    {
      key: 'test',
      icon: <PlayCircleOutlined />,
      label: 'Test',
      onClick: () => handleTest(chatbot),
    },
    {
      key: 'duplicate',
      icon: <CopyOutlined />,
      label: 'Duplicate',
      onClick: () => handleDuplicate(chatbot),
    },
    {
      type: 'divider',
    },
    {
      key: 'delete',
      icon: <DeleteOutlined />,
      label: 'Delete',
      danger: true,
      onClick: () => handleDelete(chatbot),
    },
  ];

  if (showBuilder) {
    return (
      <ChatbotBuilder
        chatbotId={selectedChatbot?.id}
        onComplete={handleBuilderComplete}
      />
    );
  }

  return (
    <div>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <Typography.Title level={2} style={{ margin: 0 }}>
              Chatbots
            </Typography.Title>
            <Typography.Paragraph type="secondary" style={{ marginBottom: 0 }}>
              Build intelligent chatbots that can answer questions based on your knowledge base
            </Typography.Paragraph>
          </div>
          <Button
            type="primary"
            size="large"
            icon={<PlusOutlined />}
            onClick={handleCreateNew}
          >
            Create Chatbot
          </Button>
        </div>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Chatbots"
              value={chatbots.length}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Active Chatbots"
              value={chatbots.filter(bot => bot.status === 'active').length}
              prefix={<MessageOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Draft Chatbots"
              value={chatbots.filter(bot => bot.status === 'draft').length}
              prefix={<EditOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="Total Conversations"
              value={1247}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Chatbot Grid */}
      {chatbots.length === 0 ? (
        <Card>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No chatbots yet"
          >
            <Button type="primary" onClick={handleCreateNew}>
              Create Your First Chatbot
            </Button>
          </Empty>
        </Card>
      ) : (
        <Row gutter={[16, 16]}>
          {chatbots.map((chatbot) => (
            <Col xs={24} sm={12} lg={8} xl={6} key={chatbot.id}>
              <Card
                hoverable
                actions={[
                  <Button
                    type="text"
                    icon={<EditOutlined />}
                    onClick={() => handleEdit(chatbot)}
                  >
                    Edit
                  </Button>,
                  <Button
                    type="text"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleTest(chatbot)}
                  >
                    Test
                  </Button>,
                  <Dropdown
                    menu={{ items: getMenuItems(chatbot) }}
                    trigger={['click']}
                  >
                    <Button type="text" icon={<MoreOutlined />} />
                  </Dropdown>,
                ]}
              >
                <Card.Meta
                  avatar={<RobotOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                  title={
                    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                      <Typography.Text strong ellipsis style={{ maxWidth: '120px' }}>
                        {chatbot.name}
                      </Typography.Text>
                      <Tag color={getStatusColor(chatbot.status)} size="small">
                        {chatbot.status.toUpperCase()}
                      </Tag>
                    </Space>
                  }
                  description={
                    <div>
                      <Typography.Paragraph
                        ellipsis={{ rows: 2 }}
                        style={{ marginBottom: '12px', minHeight: '40px' }}
                      >
                        {chatbot.description}
                      </Typography.Paragraph>

                      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                        <div>
                          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                            {chatbot.knowledgeBase.totalDocuments} docs
                          </Typography.Text>
                        </div>
                        <div style={{ display: 'flex', alignItems: 'center' }}>
                          <ClockCircleOutlined style={{ fontSize: '10px', marginRight: '4px' }} />
                          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                            {new Date(chatbot.updatedAt).toLocaleDateString()}
                          </Typography.Text>
                        </div>
                      </Space>
                    </div>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      )}

      {/* Test Chat Modal */}
      <Modal
        title={`Test ${selectedChatbot?.name}`}
        open={showTestChat}
        onCancel={() => setShowTestChat(false)}
        footer={null}
        width={600}
        destroyOnClose
      >
        {selectedChatbot && (
          <ChatInterface
            chatbotId={selectedChatbot.id}
            height={500}
            showHeader={false}
          />
        )}
      </Modal>
    </div>
  );
};
