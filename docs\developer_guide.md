# M-GAIF Developer Guide

## Project Structure

M-GAIF follows a modular architecture with clear separation of concerns:

### Core Systems
- `core/plugins/`: Dynamic plugin system with hot-swapping and health monitoring
- `core/agent/`: Agent framework with planning, memory, and coordination
- `core/security/`: Authentication, authorization, threat detection, audit logging
- `core/rag/`: Document processing, chunking, retrieval, and grounding validation
- `core/evaluation/`: Metrics, benchmarking, and performance analysis
- `core/workflow/`: Node-based workflow execution and state management
- `core/adapters/`: LLM and external service adapters
- `core/contracts/`: Interface definitions and data models
- `core/stores/`: Vector stores and data persistence
- `core/edge/`: OpenAI-compatible API endpoints
- `core/mcp/`: Tool and workflow HTTP interfaces

### Plugin Implementations
- `plugins/tokenizers/`: Text tokenization implementations
- `plugins/embedders/`: Text embedding implementations
- `plugins/retrievers/`: Document retrieval implementations

### Configuration and Testing
- `configs/workflows/`: YAML workflow specifications
- `tests/`: Unit tests, integration tests, and performance benchmarks
- `docs/`: Comprehensive documentation and guides

## Development Environment Setup

### Prerequisites
- **Python 3.11+** (recommended for optimal performance)
- **Virtual environment** (strongly recommended)
- **Git** for version control

### Installation
```bash
# Create and activate virtual environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# or
.\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
pip install -r requirements.txt

# Install development dependencies (optional)
pip install -r requirements-dev.txt
```

### Running Tests and Linters
```bash
# Run all tests with benchmarking
pytest -q

# Run specific test categories
pytest tests/test_plugins.py -v
pytest tests/test_security.py -v
pytest tests/test_agent.py -v

# Run linting and formatting
black core/ plugins/ tests/
flake8 core/ plugins/ tests/
mypy core/ plugins/
```

## Development Standards

### Code Quality Requirements
- **Type hints required** for all functions and methods
- **Pydantic models** for data contracts and validation
- **Async/await patterns** for I/O operations
- **Comprehensive error handling** with custom exceptions
- **Structured logging** with correlation IDs
- **Unit tests** with >80% coverage target

### Code Organization
- **Small, focused modules** with single responsibility
- **Clear separation** between core systems and plugins
- **Avoid circular dependencies** via dependency injection
- **Keep adapters side-effect free** for testability
- **Use factories** for complex object creation

## Testing & benchmarks

- Tests double as performance benchmarks with p95/p99 and QPS
- Output written to `.benchmarks.json` (override with `MGAIF_BENCH_OUT`)
- Thresholds via `benchmarks_thresholds.json` (CI uses `benchmarks_thresholds.ci.json`)

## Concurrency & streaming

- Edge API supports streaming via SSE-compatible chunking
- Ollama adapter supports streamed JSON lines from `/api/chat`

## Observability

- Metrics endpoint exposed at `GET /metrics` from `core/edge/api.py` (Prometheus format).
- Tracing via OpenTelemetry exporter endpoint:
  ```powershell
  $env:OTEL_EXPORTER_OTLP_ENDPOINT = "http://localhost:4318"
  ```
- JSON structured logs include request IDs.

## Adapter selection

- Edge API adapter via env:
  ```powershell
  $env:MGAIF_EDGE_ADAPTER = "echo"  # or "ollama"
  ```
- MCP API adapter via env:
  ```powershell
  $env:MGAIF_MCP_ADAPTER = "echo"  # or "ollama"
  ```
- Ollama configuration:
  - `OLLAMA_BASE_URL` (default `http://127.0.0.1:11434`)
  - `OLLAMA_MODEL` (default `llama3`)

## Security guardrails

- Basic prompt-injection detector can block flagged prompts when enabled:
  ```powershell
  $env:MGAIF_SECURITY_STRICT = "true"
  ```

## Release process (suggested)

1. Bump version (e.g., `__version__` module or tags)
2. Run full test suite locally
3. Ensure CI is green and thresholds satisfied
4. Generate docs and publish (e.g., GitHub Pages)

## Building docs

- Build HTML into `build/docs`:
```powershell
python -m sphinx -b html docs build/docs
```
- Open `build/docs/index.html` in your browser

## Contribution guidelines

- Branch naming: `feat/*`, `fix/*`, `docs/*`
- PRs must include tests and, when applicable, benchmark updates
- Document new endpoints/handlers in README and docs
