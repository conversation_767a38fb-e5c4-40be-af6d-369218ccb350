name: rag_llm_ollama
nodes:
  - id: set_docs
    handler: set
    params:
      docs:
        - {id: d1, text: "the quick brown fox"}
        - {id: d2, text: "jumps over the lazy dog"}
        - {id: d3, text: "lorem ipsum dolor sit amet"}
  - id: set_query
    handler: set
    params:
      query: "quick fox"
      prompt: "Summarize retrieved in a short sentence."
  - id: retrieve
    handler: retrieve
    params:
      doc_state_key: docs
      query_key: query
      result_key: hits
      result_mode: texts
      top_k: 2
  - id: generate
    handler: call_llm
    params:
      adapter: ollama
      input_key: prompt
      context_key: hits
      output_key: answer
      model: ${OLLAMA_MODEL}
      max_tokens: 64
  - id: end
    handler: noop
edges:
  - {from: set_docs, to: set_query}
  - {from: set_query, to: retrieve}
  - {from: retrieve, to: generate}
  - {from: generate, to: end}
