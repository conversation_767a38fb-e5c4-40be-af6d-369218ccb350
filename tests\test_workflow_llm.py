import os
import pytest
import httpx

from core.workflow.schema import WorkflowSpec
from core.workflow.engine import WorkflowEngine
from core.contracts.llm import ChatCompletionRequest, ChatMessage
from core.adapters.ollama_adapter import OllamaAdapter
from tests.realistic_data import get_chat_messages


@pytest.mark.asyncio
async def test_workflow_call_llm_echo_basic():
    spec = WorkflowSpec.model_validate({
        "name": "llm_echo_basic",
        "nodes": [
            {"id": "set", "handler": "set", "params": {"input": "Hello from workflow"}},
            {"id": "gen", "handler": "call_llm", "params": {"adapter": "echo", "input_key": "input", "output_key": "out"}},
            {"id": "end", "handler": "noop"},
        ],
        "edges": [
            {"from": "set", "to": "gen"},
            {"from": "gen", "to": "end"},
        ],
    })
    engine = WorkflowEngine(spec)
    state = await engine.run({})
    assert state["out"] == "Hello from workflow"


@pytest.mark.asyncio
async def test_workflow_rag_with_llm_echo():
    # retrieve texts, then pass as context to call_llm (echo adapter)
    docs = [
        {"id": "d1", "text": "the quick brown fox"},
        {"id": "d2", "text": "jumps over the lazy dog"},
        {"id": "d3", "text": "lorem ipsum"},
    ]
    spec = WorkflowSpec.model_validate({
        "name": "rag_llm_echo",
        "nodes": [
            {"id": "set_docs", "handler": "set", "params": {"docs": docs}},
            {"id": "set_q", "handler": "set", "params": {"query": "quick fox", "prompt": "Summarize retrieved in 5 words."}},
            {"id": "ret", "handler": "retrieve", "params": {"doc_state_key": "docs", "query_key": "query", "result_key": "hits", "result_mode": "texts", "top_k": 2}},
            {"id": "gen", "handler": "call_llm", "params": {"adapter": "echo", "input_key": "prompt", "context_key": "hits", "output_key": "answer"}},
            {"id": "end", "handler": "noop"},
        ],
        "edges": [
            {"from": "set_docs", "to": "set_q"},
            {"from": "set_q", "to": "ret"},
            {"from": "ret", "to": "gen"},
            {"from": "gen", "to": "end"},
        ],
    })
    engine = WorkflowEngine(spec)
    state = await engine.run({})
    # EchoAdapter echoes the final user prompt, not context
    assert state["answer"] == "Summarize retrieved in 5 words."


async def _ollama_alive(base_url: str) -> bool:
    """Check if Ollama server is running and responsive."""
    try:
        async with httpx.AsyncClient(timeout=3.0) as ac:
            # Check if the server is running by hitting the tags endpoint
            r = await ac.get(f"{base_url}/api/tags")
            if r.status_code != 200:
                return False

            # Also verify that we can make a basic chat request
            # This ensures the chat endpoint is available
            test_payload = {
                "model": "llama3",  # Default model
                "messages": [{"role": "user", "content": "test"}],
                "stream": False,
            }
            r = await ac.post(f"{base_url}/api/chat", json=test_payload)
            # If we get 404, the endpoint doesn't exist
            # If we get other errors (like model not found), that's still "alive"
            return r.status_code != 404
    except Exception:
        return False


@pytest.mark.asyncio
async def test_workflow_call_llm_ollama_smoke():
    base_url = os.getenv("OLLAMA_BASE_URL", "http://127.0.0.1:11434")
    model = os.getenv("OLLAMA_MODEL", "llama3")
    if not await _ollama_alive(base_url):
        pytest.skip("Ollama not available")
    spec = WorkflowSpec.model_validate({
        "name": "llm_ollama_smoke",
        "nodes": [
            {"id": "set", "handler": "set", "params": {"input": "Explain machine learning in one sentence."}},
            {"id": "gen", "handler": "call_llm", "params": {"adapter": "ollama", "input_key": "input", "output_key": "out", "model": model, "max_tokens": 32}},
            {"id": "end", "handler": "noop"},
        ],
        "edges": [
            {"from": "set", "to": "gen"},
            {"from": "gen", "to": "end"},
        ],
    })
    engine = WorkflowEngine(spec)
    state = await engine.run({})
    assert isinstance(state.get("out", ""), str)
    assert len(state["out"]) > 0
