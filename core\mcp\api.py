"""Model Context Protocol (MCP) API for M-GAIF.

This module implements the MCP-style API that exposes M-GAIF's core components
as individual tools. Each tool provides a specific capability (tokenization,
embedding, retrieval, etc.) through standardized HTTP endpoints.

The MCP API provides:
- Tool-based component access
- Standardized request/response formats
- Security middleware integration
- Component lifecycle management
- Workflow execution capabilities

Available Tools:
- /mcp/tools/tokenize: Text tokenization
- /mcp/tools/embed: Text embedding generation
- /mcp/tools/retriever/index: Document indexing
- /mcp/tools/retriever/search: Document search
- /mcp/tools/llm/chat: Chat completions
- /mcp/tools/workflow/run: Workflow execution

Example Usage:
    >>> import httpx
    >>> response = httpx.post(
    ...     "http://localhost:8001/mcp/tools/tokenize",
    ...     json={"text": "Hello, world!"}
    ... )
    >>> tokens = response.json()["tokens"]

Configuration:
- MGAIF_MCP_ADAPTER: LLM adapter selection (echo/ollama)
- MGAIF_SECURITY_STRICT: Enable strict security mode
- MGAIF_RATE_LIMIT: Requests per minute limit

Note:
    The MCP API is designed for programmatic access and integration
    with other systems. For human interaction, use the Edge API.
"""

from fastapi import FastAPI
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import os

# Import existing modules
from plugins.tokenizers.simple_tokenizer import SimpleTokenizer
from plugins.embedders.simple_embedder import SimpleEmbedder
from plugins.retrievers.in_memory_retriever import InMemoryRetriever
from core.adapters.openai_adapter import EchoAdapter
from core.adapters.ollama_adapter import OllamaAdapter
from core.workflow.schema import WorkflowSpec
from core.workflow.engine import WorkflowEngine
from core.security.middleware import create_security_middleware, SecurityMiddleware, ToolAccessMiddleware


app = FastAPI(title="M-GAIF MCP-like API")

# Add security middleware
_strict_mode = os.getenv("MGAIF_SECURITY_STRICT", "false").lower() in {"1", "true", "yes"}
_rate_limit = int(os.getenv("MGAIF_RATE_LIMIT", "100"))
_allowed_tools = ["tokenize", "embed", "retriever", "llm", "workflow"]
security_middleware, tool_access_middleware = create_security_middleware(
    strict_mode=_strict_mode,
    allowed_tools=_allowed_tools,
    rate_limit=_rate_limit
)
# Add the middleware instances
app.middleware("http")(security_middleware.dispatch)
app.middleware("http")(tool_access_middleware.dispatch)

# Shared singletons (simple demo; real MCP may handle sessions/resources differently)
_tokenizer = SimpleTokenizer()
_embedder = SimpleEmbedder()
_retriever = InMemoryRetriever()
_llm_choice = os.getenv("MGAIF_MCP_ADAPTER", "echo").lower()
_llm = OllamaAdapter() if _llm_choice == "ollama" else EchoAdapter()


class TokenizeRequest(BaseModel):
    text: str


class TokenizeResponse(BaseModel):
    tokens: List[int]
    token_count: int


@app.post("/mcp/tools/tokenize", response_model=TokenizeResponse)
async def tokenize(req: TokenizeRequest):
    """Tokenize text into integer token IDs.

    Converts input text into a sequence of integer tokens using the configured
    tokenizer. Useful for token counting, length validation, and preprocessing.

    Args:
        req: Request containing text to tokenize

    Returns:
        TokenizeResponse with token IDs and count

    Example:
        ```bash
        curl -X POST "http://localhost:8001/mcp/tools/tokenize" \\
             -H "Content-Type: application/json" \\
             -d '{"text": "Hello, world!"}'
        ```

        Response:
        ```json
        {
            "tokens": [0, 1, 2],
            "token_count": 3
        }
        ```
    """
    tokens = _tokenizer.encode(req.text)
    return TokenizeResponse(tokens=tokens, token_count=len(tokens))


class EmbedRequest(BaseModel):
    texts: List[str]


class EmbedResponse(BaseModel):
    vectors: List[List[float]]


@app.post("/mcp/tools/embed", response_model=EmbedResponse)
async def embed(req: EmbedRequest):
    """Generate vector embeddings for text inputs.

    Converts a list of text strings into dense vector representations
    suitable for similarity search and semantic analysis.

    Args:
        req: Request containing list of texts to embed

    Returns:
        EmbedResponse with vector embeddings for each input text

    Example:
        ```bash
        curl -X POST "http://localhost:8001/mcp/tools/embed" \\
             -H "Content-Type: application/json" \\
             -d '{"texts": ["Hello world", "AI is amazing"]}'
        ```

        Response:
        ```json
        {
            "vectors": [
                [0.1, 0.2, 0.3, ...],
                [0.4, 0.5, 0.6, ...]
            ]
        }
        ```

    Note:
        All vectors have the same dimensionality and are unit-normalized.
        Similar texts produce similar vectors (high cosine similarity).
    """
    vectors = _embedder.embed(req.texts)
    return EmbedResponse(vectors=vectors)


class RetrieverIndexRequest(BaseModel):
    items: List[Dict[str, str]]  # {id, text}


@app.post("/mcp/tools/retriever/index")
async def retriever_index(req: RetrieverIndexRequest):
    """Index documents for semantic search and retrieval.

    Processes a collection of documents by generating embeddings and storing
    them in the vector index for similarity-based retrieval.

    Args:
        req: Request containing list of documents with id and text fields

    Returns:
        Success status and count of indexed documents

    Example:
        ```bash
        curl -X POST "http://localhost:8001/mcp/tools/retriever/index" \\
             -H "Content-Type: application/json" \\
             -d '{
                 "items": [
                     {"id": "doc1", "text": "Machine learning overview"},
                     {"id": "doc2", "text": "Deep learning with neural networks"}
                 ]
             }'
        ```

        Response:
        ```json
        {
            "ok": true,
            "count": 2
        }
        ```

    Note:
        Documents with duplicate IDs will overwrite existing entries.
        Large document collections may consume significant memory.
    """
    _retriever.index([(str(i["id"]), str(i["text"])) for i in req.items])
    return {"ok": True, "count": len(req.items)}


class RetrieverSearchRequest(BaseModel):
    query: str
    top_k: int = 3


class RetrieverHit(BaseModel):
    id: str
    score: float


class RetrieverSearchResponse(BaseModel):
    hits: List[RetrieverHit]


@app.post("/mcp/tools/retriever/search", response_model=RetrieverSearchResponse)
async def retriever_search(req: RetrieverSearchRequest):
    hits = _retriever.search(req.query, top_k=req.top_k)
    return RetrieverSearchResponse(hits=[RetrieverHit(id=h.id, score=float(h.score)) for h in hits])


class ChatMessage(BaseModel):
    role: str
    content: str


class ChatRequest(BaseModel):
    messages: List[ChatMessage]


class ChatChoice(BaseModel):
    content: str


class ChatResponse(BaseModel):
    choices: List[ChatChoice]


@app.post("/mcp/tools/llm/chat", response_model=ChatResponse)
async def chat(req: ChatRequest):
    # Reuse EchoAdapter (deterministic)
    from core.contracts.llm import ChatCompletionRequest, ChatMessage as LLMessage

    oreq = ChatCompletionRequest(messages=[LLMessage(role=m.role, content=m.content) for m in req.messages])
    ores = await _llm.chat(oreq)
    content = ores.choices[0].message.content
    return ChatResponse(choices=[ChatChoice(content=content)])





class WorkflowRunRequest(BaseModel):
    spec: Dict[str, Any]  # WorkflowSpec JSON/YAML parsed dict
    state: Dict[str, Any] = {}


class WorkflowRunResponse(BaseModel):
    state: Dict[str, Any]


@app.post("/mcp/tools/workflow/run", response_model=WorkflowRunResponse)
async def workflow_run(req: WorkflowRunRequest):
    spec = WorkflowSpec.model_validate(req.spec)
    engine = WorkflowEngine(spec)
    state = await engine.run(req.state or {})
    return WorkflowRunResponse(state=state)



