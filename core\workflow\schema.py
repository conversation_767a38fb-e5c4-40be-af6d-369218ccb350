"""Workflow schema definitions and validation.

This module defines the data structures and validation logic for M-GAIF workflows.
Workflows are directed graphs of processing nodes connected by conditional edges,
enabling complex AI processing pipelines with branching logic.

The schema supports:
- Node-based workflow definitions
- Conditional edge traversal
- Parameter passing between nodes
- Built-in and custom handlers
- Workflow validation and optimization

Key Components:
- Node: Individual processing step with handler and parameters
- Edge: Connection between nodes with optional conditions
- WorkflowSpec: Complete workflow definition with validation

Example:
    >>> spec = WorkflowSpec(
    ...     name="simple_workflow",
    ...     nodes=[
    ...         Node(id="start", handler="set", params={"value": "Hello"}),
    ...         Node(id="end", handler="echo", params={"input_key": "value"})
    ...     ],
    ...     edges=[Edge(from_="start", to="end")]
    ... )

Note:
    Workflows are validated at creation time to ensure structural integrity
    and prevent cycles or missing node references.
"""

from __future__ import annotations

from typing import Dict, List, Optional

from pydantic import BaseModel, Field, model_validator


class Edge(BaseModel):
    """Directed edge between nodes with an optional condition.

    If condition is provided, the edge is taken only when the condition evaluates truthy
    against the state. For simplicity, condition is a key in state to check truthiness.
    """

    from_: str = Field(alias="from")
    to: str
    condition: Optional[str] = None

    model_config = {"populate_by_name": True}


class Node(BaseModel):
    """A workflow node.

    - handler: dotted path or built-in handler name (e.g., "noop", "set").
    - params: JSON-serializable parameters for the handler.
    """

    id: str
    type: Optional[str] = None  # legacy/simple type alias to built-ins
    handler: Optional[str] = None
    params: Dict[str, object] = Field(default_factory=dict)


class WorkflowSpec(BaseModel):
    name: str
    nodes: List[Node]
    edges: List[Edge] = Field(default_factory=list)
    entry: Optional[str] = None

    # derived maps (populated post-validate)
    node_map: Dict[str, Node] = Field(default_factory=dict)
    outgoing: Dict[str, List[Edge]] = Field(default_factory=dict)

    @model_validator(mode="after")
    def _build_indices(self) -> "WorkflowSpec":
        self.node_map = {n.id: n for n in self.nodes}
        if len(self.node_map) != len(self.nodes):
            raise ValueError("Duplicate node IDs in workflow spec")
        self.outgoing = {nid: [] for nid in self.node_map}
        for e in self.edges:
            if e.from_ not in self.node_map:
                raise ValueError(f"Edge.from references unknown node: {e.from_}")
            if e.to not in self.node_map:
                raise ValueError(f"Edge.to references unknown node: {e.to}")
            self.outgoing[e.from_].append(e)
        if not self.entry:
            # default to first node if not specified
            if not self.nodes:
                raise ValueError("Workflow has no nodes")
            self.entry = self.nodes[0].id
        if self.entry not in self.node_map:
            raise ValueError("Entry node not found in nodes")
        return self

