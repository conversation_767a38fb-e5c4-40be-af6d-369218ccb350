"""Security middleware for M-GAIF APIs.

This module provides comprehensive security middleware for protecting M-GAIF APIs
from various threats including prompt injection, PII exposure, and unauthorized
access. The middleware integrates with FastAPI to provide request/response
filtering and access control.

Security Features:
- Input validation and sanitization
- Prompt injection detection
- PII detection and redaction
- Rate limiting per client
- Tool access control
- Request/response logging
- Security violation tracking

Middleware Components:
- SecurityMiddleware: Core security validation and filtering
- ToolAccessMiddleware: Tool-based access control
- Rate limiting with configurable thresholds
- Comprehensive audit logging

Example:
    >>> from core.security.middleware import create_security_middleware
    >>>
    >>> # Create middleware instances
    >>> security_middleware, tool_access_middleware = create_security_middleware(
    ...     strict_mode=True,
    ...     rate_limit=100
    ... )
    >>>
    >>> # Add to FastAPI app
    >>> app.middleware("http")(security_middleware.dispatch)
    >>> app.middleware("http")(tool_access_middleware.dispatch)

Note:
    Security middleware should be added early in the middleware stack
    to ensure all requests are properly validated and filtered.
"""

import json
import logging
import time
from typing import Any, Callable, Optional
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from .input_validation import InputValidator, SecurityViolation, CapabilityManager
from .auth import AuthManager, Permission, AuthenticationError, AuthorizationError
from .threat_detection import ThreatDetectionEngine, ThreatLevel
from .audit import AuditLogger, CorrelationContext

logger = logging.getLogger(__name__)


class SecurityMiddleware(BaseHTTPMiddleware):
    """Middleware for input validation and security checks."""
    
    def __init__(
        self,
        app,
        validator: Optional[InputValidator] = None,
        capability_manager: Optional[CapabilityManager] = None,
        rate_limit_requests_per_minute: int = 100
    ):
        super().__init__(app)
        self.validator = validator or InputValidator()
        self.capability_manager = capability_manager or CapabilityManager()
        self.rate_limit = rate_limit_requests_per_minute
        self.request_counts = {}  # Simple in-memory rate limiting
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request through security checks."""
        start_time = time.time()
        
        try:
            # Rate limiting (simple implementation)
            client_ip = request.client.host if request.client else "unknown"
            current_minute = int(time.time() // 60)
            
            if client_ip not in self.request_counts:
                self.request_counts[client_ip] = {}
            
            if current_minute not in self.request_counts[client_ip]:
                self.request_counts[client_ip][current_minute] = 0
            
            self.request_counts[client_ip][current_minute] += 1
            
            # Clean old entries
            for ip in list(self.request_counts.keys()):
                self.request_counts[ip] = {
                    minute: count for minute, count in self.request_counts[ip].items()
                    if minute >= current_minute - 5  # Keep last 5 minutes
                }
                if not self.request_counts[ip]:
                    del self.request_counts[ip]
            
            # Check rate limit
            if self.request_counts[client_ip][current_minute] > self.rate_limit:
                logger.warning(f"Rate limit exceeded for {client_ip}")
                return JSONResponse(
                    status_code=429,
                    content={"error": {"type": "rate_limit_exceeded", "message": "Too many requests"}}
                )
            
            # Validate request content for specific endpoints
            if request.method == "POST" and request.url.path in [
                "/v1/chat/completions",
                "/mcp/tools/llm/chat",
                "/mcp/tools/workflow/run"
            ]:
                # Read and validate request body
                body = await request.body()
                if body:
                    try:
                        data = json.loads(body)
                        validated_data = await self._validate_request_data(request.url.path, data)
                        
                        # Replace request body with validated data
                        request._body = json.dumps(validated_data).encode()
                        
                    except SecurityViolation as e:
                        logger.warning(f"Security violation: {e}", extra=e.details)
                        return JSONResponse(
                            status_code=400,
                            content={
                                "error": {
                                    "type": "security_violation",
                                    "message": str(e),
                                    "violation_type": e.violation_type
                                }
                            }
                        )
                    except json.JSONDecodeError:
                        logger.warning("Invalid JSON in request body")
                        return JSONResponse(
                            status_code=400,
                            content={"error": {"type": "invalid_json", "message": "Invalid JSON in request body"}}
                        )
                    except Exception as e:
                        logger.error(f"Unexpected error during request validation: {e}")
                        return JSONResponse(
                            status_code=500,
                            content={"error": {"type": "internal_error", "message": "Internal server error"}}
                        )
            
            # Process the request
            response = await call_next(request)
            
            # Log request details
            process_time = time.time() - start_time
            logger.info(
                "Request processed",
                extra={
                    "method": request.method,
                    "path": request.url.path,
                    "client_ip": client_ip,
                    "status_code": response.status_code,
                    "process_time": process_time
                }
            )

            return response

        except Exception as e:
            logger.error(f"Security middleware error: {e}")
            return JSONResponse(
                status_code=500,
                content={"error": {"type": "middleware_error", "message": "Internal server error"}}
            )

    async def _validate_request_data(self, path: str, data: dict) -> dict:
        """Validate request data based on endpoint."""

        if path in ["/v1/chat/completions", "/mcp/tools/llm/chat"]:
            # Validate chat messages
            if "messages" in data:
                for message in data["messages"]:
                    if "content" in message and message["content"]:
                        # Validate and sanitize message content
                        message["content"] = self.validator.validate_input(
                            message["content"],
                            check_pii=True,
                            check_injection=True
                        )

        elif path == "/mcp/tools/workflow/run":
            # Validate workflow specification
            if "spec" in data:
                data["spec"] = self.validator.validate_workflow_spec(data["spec"])

        return data


class EnhancedSecurityMiddleware(BaseHTTPMiddleware):
    """Enhanced security middleware with authentication, authorization, and threat detection."""

    def __init__(self, app, auth_manager: AuthManager,
                 threat_engine: ThreatDetectionEngine = None,
                 audit_logger: AuditLogger = None,
                 rate_limit_requests_per_minute: int = 100):
        """Initialize enhanced security middleware.

        Args:
            app: FastAPI application
            auth_manager: Authentication manager
            threat_engine: Threat detection engine
            audit_logger: Audit logger
            rate_limit_requests_per_minute: Rate limit per user per minute
        """
        super().__init__(app)
        self.auth_manager = auth_manager
        self.threat_engine = threat_engine or ThreatDetectionEngine()
        self.audit_logger = audit_logger or AuditLogger()
        self.rate_limit = rate_limit_requests_per_minute

        # Rate limiting storage
        self.request_counts = {}
        self.last_reset = time.time()

        # Protected endpoints and required permissions
        self.endpoint_permissions = {
            "/mcp/tools/": Permission.AGENT_EXECUTE,
            "/v1/chat/completions": Permission.AGENT_EXECUTE,
            "/admin/": Permission.SYSTEM_CONFIG,
            "/metrics": Permission.SYSTEM_MONITOR,
        }

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with enhanced security checks."""
        start_time = time.time()

        # Generate correlation ID for request tracking
        with CorrelationContext() as correlation_id:
            try:
                # Extract user information
                user = await self._authenticate_request(request)
                user_id = user.id if user else "anonymous"

                # Rate limiting
                if not await self._check_rate_limit(user_id):
                    await self.audit_logger.log_security_event(
                        "rate_limit_exceeded",
                        user_id,
                        {
                            "endpoint": str(request.url),
                            "method": request.method,
                            "correlation_id": correlation_id
                        }
                    )
                    return JSONResponse(
                        status_code=429,
                        content={"error": "Rate limit exceeded"}
                    )

                # Authorization check
                if not await self._authorize_request(request, user):
                    await self.audit_logger.log_security_event(
                        "authorization_failed",
                        user_id,
                        {
                            "endpoint": str(request.url),
                            "method": request.method,
                            "correlation_id": correlation_id
                        }
                    )
                    return JSONResponse(
                        status_code=403,
                        content={"error": "Insufficient permissions"}
                    )

                # Threat detection on request body
                if request.method in ["POST", "PUT", "PATCH"]:
                    body = await self._get_request_body(request)
                    if body:
                        threats = await self.threat_engine.analyze_threats(
                            body,
                            {
                                "user_id": user_id,
                                "endpoint": str(request.url),
                                "method": request.method,
                                "correlation_id": correlation_id
                            }
                        )

                        # Block high-severity threats
                        critical_threats = [
                            t for t in threats
                            if t.threat_level in [ThreatLevel.HIGH, ThreatLevel.CRITICAL]
                        ]

                        if critical_threats:
                            await self.audit_logger.log_security_event(
                                "request_blocked",
                                user_id,
                                {
                                    "threats": [t.to_dict() for t in critical_threats],
                                    "endpoint": str(request.url),
                                    "correlation_id": correlation_id
                                }
                            )
                            return JSONResponse(
                                status_code=400,
                                content={"error": "Request blocked due to security concerns"}
                            )

                # Add security headers to request
                request.state.user = user
                request.state.correlation_id = correlation_id

                # Process request
                response = await call_next(request)

                # Add security headers to response
                response.headers["X-Correlation-ID"] = correlation_id
                response.headers["X-Content-Type-Options"] = "nosniff"
                response.headers["X-Frame-Options"] = "DENY"
                response.headers["X-XSS-Protection"] = "1; mode=block"

                # Log successful request
                processing_time = time.time() - start_time
                await self.audit_logger.log_system_event(
                    "request_processed",
                    {
                        "user_id": user_id,
                        "endpoint": str(request.url),
                        "method": request.method,
                        "status_code": response.status_code,
                        "processing_time": processing_time,
                        "correlation_id": correlation_id
                    }
                )

                return response

            except AuthenticationError as e:
                await self.audit_logger.log_security_event(
                    "authentication_error",
                    "unknown",
                    {
                        "error": str(e),
                        "endpoint": str(request.url),
                        "correlation_id": correlation_id
                    }
                )
                return JSONResponse(
                    status_code=401,
                    content={"error": "Authentication failed"}
                )

            except AuthorizationError as e:
                await self.audit_logger.log_security_event(
                    "authorization_error",
                    user_id if 'user_id' in locals() else "unknown",
                    {
                        "error": str(e),
                        "endpoint": str(request.url),
                        "correlation_id": correlation_id
                    }
                )
                return JSONResponse(
                    status_code=403,
                    content={"error": "Access denied"}
                )

            except Exception as e:
                await self.audit_logger.log_error_event(
                    e,
                    user_id if 'user_id' in locals() else "unknown",
                    {
                        "endpoint": str(request.url),
                        "method": request.method,
                        "correlation_id": correlation_id
                    }
                )
                return JSONResponse(
                    status_code=500,
                    content={"error": "Internal server error"}
                )

    async def _authenticate_request(self, request: Request) -> Optional[Any]:
        """Authenticate request and return user."""
        # Check for Authorization header
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            # Allow anonymous access to some endpoints
            if self._is_public_endpoint(str(request.url)):
                return None
            raise AuthenticationError("Missing authorization header")

        # Extract token
        if not auth_header.startswith("Bearer "):
            raise AuthenticationError("Invalid authorization header format")

        token = auth_header[7:]  # Remove "Bearer " prefix

        # Validate token
        user = await self.auth_manager.validate_token(token)
        if not user:
            raise AuthenticationError("Invalid or expired token")

        return user

    async def _authorize_request(self, request: Request, user) -> bool:
        """Check if user is authorized for the request."""
        if user is None:
            return self._is_public_endpoint(str(request.url))

        # Check endpoint permissions
        endpoint_path = request.url.path

        for protected_path, required_permission in self.endpoint_permissions.items():
            if endpoint_path.startswith(protected_path):
                return await self.auth_manager.authorize_action(user, required_permission)

        # Default: allow access if no specific permission required
        return True

    def _is_public_endpoint(self, url: str) -> bool:
        """Check if endpoint allows anonymous access."""
        public_endpoints = [
            "/health",
            "/docs",
            "/openapi.json",
            "/metrics"  # May want to protect this in production
        ]

        for endpoint in public_endpoints:
            if endpoint in url:
                return True

        return False

    async def _check_rate_limit(self, user_id: str) -> bool:
        """Check if user is within rate limits."""
        current_time = time.time()

        # Reset counts every minute
        if current_time - self.last_reset > 60:
            self.request_counts.clear()
            self.last_reset = current_time

        # Check user's request count
        user_requests = self.request_counts.get(user_id, 0)
        if user_requests >= self.rate_limit:
            return False

        # Increment count
        self.request_counts[user_id] = user_requests + 1
        return True

    async def _get_request_body(self, request: Request) -> Optional[str]:
        """Extract request body for threat analysis."""
        try:
            body = await request.body()
            if body:
                return body.decode('utf-8')
        except Exception:
            pass
        return None
    
    async def _validate_request_data(self, path: str, data: dict) -> dict:
        """Validate request data based on endpoint."""
        
        if path in ["/v1/chat/completions", "/mcp/tools/llm/chat"]:
            # Validate chat messages
            if "messages" in data:
                for message in data["messages"]:
                    if "content" in message and message["content"]:
                        # Validate and sanitize message content
                        message["content"] = self.validator.validate_input(
                            message["content"],
                            check_pii=True,
                            check_injection=True
                        )
        
        elif path == "/mcp/tools/workflow/run":
            # Validate workflow specification
            if "spec" in data:
                data["spec"] = self.validator.validate_workflow_spec(data["spec"])
        
        return data


class ToolAccessMiddleware(BaseHTTPMiddleware):
    """Middleware for tool access control."""
    
    def __init__(self, app, capability_manager: Optional[CapabilityManager] = None):
        super().__init__(app)
        self.capability_manager = capability_manager or CapabilityManager()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Check tool access permissions."""
        
        # Extract tool name from MCP endpoints
        if request.url.path.startswith("/mcp/tools/"):
            path_parts = request.url.path.split("/")
            if len(path_parts) >= 4:
                tool_name = path_parts[3]  # e.g., "tokenize", "embed", etc.
                
                # Check access
                user_token = request.headers.get("Authorization")
                if not self.capability_manager.check_tool_access(tool_name, user_token):
                    return JSONResponse(
                        status_code=403,
                        content={
                            "error": {
                                "type": "access_denied",
                                "message": f"Access denied to tool: {tool_name}"
                            }
                        }
                    )
        
        return await call_next(request)


def create_security_middleware(
    strict_mode: bool = False,
    allowed_tools: Optional[list] = None,
    rate_limit: int = 100
) -> tuple:
    """Create configured security middleware instances.

    This factory function creates and configures both SecurityMiddleware and
    ToolAccessMiddleware instances with shared components for consistent
    security enforcement across API endpoints.

    Args:
        strict_mode: Enable strict security validation (blocks suspicious requests)
        allowed_tools: List of allowed tool names for tool access control
        rate_limit: Maximum requests per minute per client

    Returns:
        tuple: (SecurityMiddleware, ToolAccessMiddleware) instances

    Example:
        >>> security_middleware, tool_access_middleware = create_security_middleware(
        ...     strict_mode=True,
        ...     allowed_tools=["tokenize", "embed", "llm"],
        ...     rate_limit=100
        ... )
        >>> app.middleware("http")(security_middleware.dispatch)
        >>> app.middleware("http")(tool_access_middleware.dispatch)
    """
    
    validator = InputValidator(strict_mode=strict_mode)
    capability_manager = CapabilityManager(allowed_tools=allowed_tools)
    
    security_middleware = SecurityMiddleware(
        app=None,  # Will be set by FastAPI
        validator=validator,
        capability_manager=capability_manager,
        rate_limit_requests_per_minute=rate_limit
    )
    
    tool_access_middleware = ToolAccessMiddleware(
        app=None,  # Will be set by FastAPI
        capability_manager=capability_manager
    )
    
    return security_middleware, tool_access_middleware
