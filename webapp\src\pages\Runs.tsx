import React, { useState } from 'react'
import { Button, List, Space, Typography } from 'antd'

export default function Runs() {
  const [runs, setRuns] = useState<Array<{ id: string; status: string; when: string }>>([])

  function addMock() {
    const id = `run_${runs.length + 1}`
    setRuns([{ id, status: 'completed', when: new Date().toISOString() }, ...runs])
  }

  return (
    <div>
      <Space style={{ marginBottom: 12 }}>
        <Typography.Title level={4} style={{ margin: 0 }}>Runs</Typography.Title>
        <Button onClick={addMock}>Add mock</Button>
      </Space>
      <List
        bordered
        dataSource={runs}
        renderItem={(r) => (
          <List.Item>
            <Space>
              <b>{r.id}</b>
              <span>{r.status}</span>
              <span>{r.when}</span>
            </Space>
          </List.Item>
        )}
      />
    </div>
  )
}
