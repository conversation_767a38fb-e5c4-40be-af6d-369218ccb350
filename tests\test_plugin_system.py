"""Tests for the plugin system."""

import asyncio
import pytest
from datetime import datetime

from core.plugins.base import Plug<PERSON>, PluginStatus, HealthCheckResult, HealthStatus
from core.plugins.registry import PluginRegistry
from core.plugins.factory import ReflectionPluginFactory, ConfigurablePluginFactory
from core.plugins.manager import ComponentManager


class TestPlugin(Plugin):
    """Test plugin implementation using real Plugin base class."""

    def __init__(self, name: str = "test_plugin", fail_init: bool = False, fail_health: bool = False):
        super().__init__(name, "Test plugin for testing")
        self.fail_init = fail_init
        self.fail_health = fail_health
        self.initialized = False
        self.shutdown_called = False

    async def initialize(self) -> None:
        """Initialize test plugin."""
        if self.fail_init:
            raise Exception("Test initialization failure")
        self.initialized = True
        self.status = PluginStatus.ACTIVE

    async def shutdown(self) -> None:
        """Shutdown test plugin."""
        self.shutdown_called = True
        self.status = PluginStatus.SHUTDOWN

    async def health_check(self) -> HealthCheckResult:
        """Test health check."""
        if self.fail_health:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message="Test health check failure",
                details={},
                timestamp=datetime.now(),
                response_time_ms=1.0
            )

        return HealthCheckResult(
            status=HealthStatus.HEALTHY,
            message="Test plugin is healthy",
            details={"test_metric": 42},
            timestamp=datetime.now(),
            response_time_ms=1.0
        )


class TestPluginBase:
    """Test Plugin base class functionality."""

    @pytest.mark.asyncio
    async def test_plugin_lifecycle(self):
        """Test plugin initialization and shutdown."""
        plugin = TestPlugin()

        # Initial state
        assert plugin.status == PluginStatus.UNLOADED
        assert not plugin.initialized
        assert not plugin.shutdown_called

        # Initialize
        await plugin.initialize()
        assert plugin.status == PluginStatus.ACTIVE
        assert plugin.initialized

        # Health check
        health = await plugin.health_check()
        assert health.status == HealthStatus.HEALTHY
        assert health.message == "Test plugin is healthy"
        assert health.details["test_metric"] == 42

        # Shutdown
        await plugin.shutdown()
        assert plugin.status == PluginStatus.SHUTDOWN
        assert plugin.shutdown_called

    @pytest.mark.asyncio
    async def test_plugin_initialization_failure(self):
        """Test plugin initialization failure handling."""
        plugin = TestPlugin(fail_init=True)

        with pytest.raises(Exception, match="Test initialization failure"):
            await plugin.initialize()

        assert plugin.status == PluginStatus.UNLOADED
        assert not plugin.initialized

    @pytest.mark.asyncio
    async def test_plugin_health_check_failure(self):
        """Test plugin health check failure."""
        plugin = TestPlugin(fail_health=True)
        await plugin.initialize()

        health = await plugin.health_check()
        assert health.status == HealthStatus.UNHEALTHY
        assert "failure" in health.message

    def test_plugin_metrics(self):
        """Test plugin metrics collection."""
        plugin = TestPlugin()

        metrics = plugin.get_metrics()
        assert "status_code" in metrics
        assert "uptime_seconds" in metrics
        assert isinstance(metrics["status_code"], float)
        assert isinstance(metrics["uptime_seconds"], float)


class TestPluginRegistry:
    """Test PluginRegistry functionality."""
    
    @pytest.fixture
    def registry(self):
        """Create a plugin registry for testing."""
        return PluginRegistry()
    
    @pytest.mark.asyncio
    async def test_plugin_registration(self, registry):
        """Test plugin registration and retrieval."""
        plugin = TestPlugin("test_plugin")

        # Register plugin
        await registry.register_plugin("test_plugin", plugin)

        # Check registration
        assert "test_plugin" in registry.list_plugins()

        # Retrieve plugin
        retrieved = await registry.get_plugin("test_plugin")
        assert retrieved is plugin
        assert retrieved.status == PluginStatus.ACTIVE

    @pytest.mark.asyncio
    async def test_plugin_unregistration(self, registry):
        """Test plugin unregistration."""
        plugin = TestPlugin("test_plugin")
        await registry.register_plugin("test_plugin", plugin)

        # Unregister plugin
        success = await registry.unregister_plugin("test_plugin")
        assert success
        assert plugin.shutdown_called

        # Check plugin is gone
        assert "test_plugin" not in registry.list_plugins()

        # Try to unregister non-existent plugin
        success = await registry.unregister_plugin("nonexistent")
        assert not success

    @pytest.mark.asyncio
    async def test_hot_swap(self, registry):
        """Test plugin hot-swapping."""
        old_plugin = TestPlugin("old_version")
        new_plugin = TestPlugin("new_version")

        # Register old plugin
        await registry.register_plugin("test_plugin", old_plugin)

        # Hot swap
        await registry.hot_swap("test_plugin", "old_version", "new_version", new_plugin)

        # Check swap occurred
        current = await registry.get_plugin("test_plugin")
        assert current is new_plugin
        assert current.status == PluginStatus.ACTIVE
        assert old_plugin.shutdown_called
    
    @pytest.mark.asyncio
    async def test_plugin_discovery(self, registry, tmp_path):
        """Test plugin discovery from directory."""
        # Create mock plugin file
        plugin_dir = tmp_path / "test_plugins"
        plugin_dir.mkdir()
        
        plugin_file = plugin_dir / "mock_plugin.py"
        plugin_file.write_text("""
from core.plugins.base import Plugin, PluginStatus

class TestDiscoveryPlugin(Plugin):
    def __init__(self):
        super().__init__("discovered_plugin", "Discovered plugin")
    
    async def initialize(self):
        self.status = PluginStatus.ACTIVE
    
    async def shutdown(self):
        self.status = PluginStatus.SHUTDOWN
""")
        
        # Mock the discovery process (in real implementation, this would use importlib)
        # For testing, we'll manually register a plugin to simulate discovery
        discovered_plugin = TestPlugin("discovered_plugin")
        await registry.register_plugin("discovered_plugin", discovered_plugin)
        
        # Verify discovery
        assert "discovered_plugin" in registry.list_plugins()
        plugin = await registry.get_plugin("discovered_plugin")
        assert plugin.status == PluginStatus.ACTIVE


class TestPluginFactory:
    """Test plugin factory functionality."""
    
    def test_reflection_factory(self):
        """Test reflection-based plugin factory."""
        factory = ReflectionPluginFactory()

        # Test plugin creation
        plugin_class = TestPlugin
        config = {"name": "factory_plugin"}

        plugin = factory.create_plugin(plugin_class, config)
        assert isinstance(plugin, TestPlugin)
        assert plugin.name == "factory_plugin"

    def test_configurable_factory(self):
        """Test configurable plugin factory."""
        factory = ConfigurablePluginFactory()

        # Register plugin type
        factory.register_plugin_type("test", TestPlugin)

        # Create plugin
        config = {"type": "test", "name": "configured_plugin"}
        plugin = factory.create_plugin_from_config(config)

        assert isinstance(plugin, TestPlugin)
        assert plugin.name == "configured_plugin"
    
    def test_factory_validation(self):
        """Test factory configuration validation."""
        factory = ConfigurablePluginFactory()
        
        # Test invalid configuration
        with pytest.raises(ValueError, match="Unknown plugin type"):
            factory.create_plugin_from_config({"type": "unknown"})


class TestComponentManager:
    """Test component manager functionality."""
    
    @pytest.fixture
    def manager(self):
        """Create component manager for testing."""
        registry = PluginRegistry()
        return ComponentManager(registry)
    
    @pytest.mark.asyncio
    async def test_health_monitoring(self, manager):
        """Test health monitoring functionality."""
        # Add test plugin
        plugin = TestPlugin("monitored_plugin")
        await manager.registry.register_plugin("monitored_plugin", plugin)

        # Enable health monitoring
        await manager.enable_health_monitoring(interval=0.1)  # Fast interval for testing

        # Wait for health check
        await asyncio.sleep(0.2)

        # Check health status
        health = await manager.get_system_health()
        assert "monitored_plugin" in health
        assert health["monitored_plugin"]["status"] == "healthy"

        # Disable monitoring
        await manager.disable_health_monitoring()

    @pytest.mark.asyncio
    async def test_backup_component(self, manager):
        """Test backup component functionality."""
        primary = TestPlugin("primary")
        backup = TestPlugin("backup")

        await manager.registry.register_plugin("primary", primary)
        await manager.set_backup_component("primary", backup)

        # Simulate primary failure
        primary.fail_health = True

        # Trigger failover (in real implementation, this would be automatic)
        await manager.failover_to_backup("primary")

        # Check backup is active
        current = await manager.registry.get_plugin("primary")
        assert current is backup
        assert current.status == PluginStatus.ACTIVE

    @pytest.mark.asyncio
    async def test_canary_deployment(self, manager):
        """Test canary deployment functionality."""
        stable = TestPlugin("stable_version")
        canary = TestPlugin("canary_version")

        await manager.registry.register_plugin("test_component", stable)

        # Start canary deployment
        await manager.start_canary_deployment(
            "test_component",
            canary,
            traffic_split=0.1
        )

        # Check canary is deployed
        canary_info = await manager.get_canary_status("test_component")
        assert canary_info is not None
        assert canary_info["traffic_split"] == 0.1
        assert canary_info["canary_version"] is canary

        # Promote canary
        await manager.promote_canary("test_component")

        # Check promotion
        current = await manager.registry.get_plugin("test_component")
        assert current is canary

    @pytest.mark.asyncio
    async def test_component_metrics(self, manager):
        """Test component metrics collection."""
        plugin = TestPlugin("metrics_plugin")
        await manager.registry.register_plugin("metrics_plugin", plugin)

        # Get metrics
        metrics = await manager.get_component_metrics("metrics_plugin")
        assert "name" in metrics
        assert "status" in metrics
        assert metrics["name"] == "metrics_plugin"

        # Get all metrics
        all_metrics = await manager.get_all_metrics()
        assert "metrics_plugin" in all_metrics


@pytest.mark.asyncio
async def test_plugin_system_integration():
    """Test complete plugin system integration."""
    # Create registry and manager
    registry = PluginRegistry()
    manager = ComponentManager(registry)
    
    # Create and register plugins
    plugin1 = TestPlugin("plugin1")
    plugin2 = TestPlugin("plugin2")

    await registry.register_plugin("plugin1", plugin1)
    await registry.register_plugin("plugin2", plugin2)

    # Enable health monitoring
    await manager.enable_health_monitoring(interval=0.1)

    # Wait for health checks
    await asyncio.sleep(0.2)

    # Check system health
    health = await manager.get_system_health()
    assert len(health) == 2
    assert all(status["status"] == "healthy" for status in health.values())

    # Test hot swap
    new_plugin = TestPlugin("plugin1_v2")
    await registry.hot_swap("plugin1", "plugin1", "plugin1_v2", new_plugin)

    # Verify swap
    current = await registry.get_plugin("plugin1")
    assert current is new_plugin
    assert plugin1.shutdown_called
    
    # Cleanup
    await manager.disable_health_monitoring()
    await registry.unregister_plugin("plugin1")
    await registry.unregister_plugin("plugin2")
