import React from 'react';
import { Form, Input, Card, Typography, Space, Avatar, Upload, Button } from 'antd';
import { UploadOutlined, RobotOutlined } from '@ant-design/icons';
import type { ChatbotFormData } from '../../../types/chatbot';

const { TextArea } = Input;

interface BasicInfoStepProps {
  initialValues?: Partial<ChatbotFormData>;
  onValuesChange?: (values: Partial<ChatbotFormData>) => void;
  form: any;
}

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  initialValues,
  onValuesChange,
  form,
}) => {
  const handleUpload = (info: any) => {
    // Handle avatar upload
    console.log('Avatar upload:', info);
  };

  return (
    <Card title="Basic Information" style={{ maxWidth: 600, margin: '0 auto' }}>
      <Typography.Paragraph type="secondary" style={{ marginBottom: '24px' }}>
        Let's start by setting up the basic information for your chatbot.
      </Typography.Paragraph>

      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={onValuesChange}
        size="large"
      >
        {/* Avatar Section */}
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <Space direction="vertical" size="middle">
            <Avatar size={80} icon={<RobotOutlined />} />
            <Upload
              showUploadList={false}
              beforeUpload={() => false}
              onChange={handleUpload}
            >
              <Button icon={<UploadOutlined />} size="small">
                Upload Avatar
              </Button>
            </Upload>
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              Optional: Add a custom avatar for your chatbot
            </Typography.Text>
          </Space>
        </div>

        {/* Basic Fields */}
        <Form.Item
          label="Chatbot Name"
          name="name"
          rules={[
            { required: true, message: 'Please enter a name for your chatbot' },
            { min: 2, message: 'Name must be at least 2 characters' },
            { max: 50, message: 'Name must be less than 50 characters' },
          ]}
        >
          <Input
            placeholder="e.g., Customer Support Bot, Sales Assistant"
            showCount
            maxLength={50}
          />
        </Form.Item>

        <Form.Item
          label="Description"
          name="description"
          rules={[
            { required: true, message: 'Please enter a description' },
            { min: 10, message: 'Description must be at least 10 characters' },
            { max: 200, message: 'Description must be less than 200 characters' },
          ]}
        >
          <TextArea
            rows={3}
            placeholder="Describe what your chatbot does and how it helps users..."
            showCount
            maxLength={200}
          />
        </Form.Item>

        {/* Use Case Examples */}
        <div style={{ marginTop: '32px' }}>
          <Typography.Title level={5}>Popular Use Cases</Typography.Title>
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px' }}>
            {[
              {
                title: 'Customer Support',
                description: 'Handle FAQs, troubleshooting, and support tickets',
                icon: '🎧',
              },
              {
                title: 'Sales Assistant',
                description: 'Product recommendations and sales inquiries',
                icon: '💼',
              },
              {
                title: 'Knowledge Base',
                description: 'Answer questions from documentation',
                icon: '📚',
              },
              {
                title: 'Lead Generation',
                description: 'Qualify leads and collect contact information',
                icon: '🎯',
              },
            ].map((useCase) => (
              <Card
                key={useCase.title}
                size="small"
                hoverable
                style={{ cursor: 'pointer', textAlign: 'center' }}
                onClick={() => {
                  form.setFieldsValue({
                    name: useCase.title,
                    description: useCase.description,
                  });
                }}
              >
                <div style={{ fontSize: '24px', marginBottom: '8px' }}>
                  {useCase.icon}
                </div>
                <Typography.Text strong style={{ fontSize: '12px' }}>
                  {useCase.title}
                </Typography.Text>
                <Typography.Text
                  type="secondary"
                  style={{ fontSize: '10px', display: 'block', marginTop: '4px' }}
                >
                  {useCase.description}
                </Typography.Text>
              </Card>
            ))}
          </div>
          <Typography.Text type="secondary" style={{ fontSize: '12px', marginTop: '8px', display: 'block' }}>
            Click on a use case to auto-fill the form
          </Typography.Text>
        </div>
      </Form>
    </Card>
  );
};
