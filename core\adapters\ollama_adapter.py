"""Ollama local LLM adapter for M-GAIF framework.

This module provides integration with Ollama, a local LLM serving platform.
The adapter enables M-GAIF to communicate with locally hosted language models
through Ollama's HTTP API, supporting both streaming and non-streaming modes.

Features:
- Local LLM integration via Ollama HTTP API
- Streaming and non-streaming chat completions
- Configurable model selection
- Automatic message format conversion
- Error handling and connection management

Configuration:
- OLLAMA_BASE_URL: Ollama server URL (default: http://127.0.0.1:11434)
- OLLAMA_MODEL: Model name to use (default: llama3)

Example:
    >>> adapter = OllamaAdapter()
    >>> request = ChatCompletionRequest(
    ...     messages=[ChatMessage(role="user", content="Explain AI")]
    ... )
    >>> response = await adapter.chat(request)

Note:
    Requires Ollama to be installed and running locally with at least one
    model pulled. See https://ollama.com for installation instructions.
"""

from __future__ import annotations

import asyncio
import os
import time
import uuid
from typing import AsyncIterator, Optional

import httpx

from .base import BaseModelAdapter
from ..contracts.llm import (
    ChatCompletionChunk,
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatChoice,
    ChatChunkChoice,
    ChatMessage,
    DeltaMessage,
    Usage,
)


class OllamaAdapter(BaseModelAdapter):
    """Adapter for local Ollama server (https://github.com/ollama/ollama).

    Env:
      - OLLAMA_BASE_URL: default http://127.0.0.1:11434
      - OLLAMA_MODEL: default "llama3"
    """

    name = "ollama"

    def __init__(self, base_url: Optional[str] = None, default_model: Optional[str] = None) -> None:
        self.base_url = base_url or os.getenv("OLLAMA_BASE_URL", "http://127.0.0.1:11434")
        self.default_model = default_model or os.getenv("OLLAMA_MODEL", "llama3")

    async def chat(self, request: ChatCompletionRequest) -> ChatCompletionResponse:
        model = request.model or self.default_model
        url = f"{self.base_url}/api/chat"
        payload = {
            "model": model,
            "messages": [{"role": m.role, "content": m.content} for m in request.messages],
            "stream": False,
            "options": {
                "temperature": request.temperature,
                **({"num_predict": request.max_tokens} if request.max_tokens else {}),
            },
        }
        async with httpx.AsyncClient(timeout=60.0) as client:
            resp = await client.post(url, json=payload)
            resp.raise_for_status()
            data = resp.json()
        content = data.get("message", {}).get("content", "")
        message = ChatMessage(role="assistant", content=content)
        now = int(time.time())
        # usage fields may not be available in Ollama response; compute minimal fallbacks
        prompt_tokens = len(request.messages)
        completion_tokens = len(content)
        return ChatCompletionResponse(
            id=str(uuid.uuid4()),
            created=now,
            model=model,
            choices=[ChatChoice(index=0, message=message, finish_reason="stop")],
            usage=Usage(prompt_tokens=prompt_tokens, completion_tokens=completion_tokens, total_tokens=prompt_tokens + completion_tokens),
        )

    async def chat_stream(self, request: ChatCompletionRequest) -> AsyncIterator[ChatCompletionChunk]:
        model = request.model or self.default_model
        url = f"{self.base_url}/api/chat"
        payload = {
            "model": model,
            "messages": [{"role": m.role, "content": m.content} for m in request.messages],
            "stream": True,
            "options": {
                "temperature": request.temperature,
                **({"num_predict": request.max_tokens} if request.max_tokens else {}),
            },
        }
        chunk_id = str(uuid.uuid4())
        now = int(time.time())
        # initial role chunk
        yield ChatCompletionChunk(
            id=chunk_id,
            created=now,
            model=model,
            choices=[ChatChunkChoice(index=0, delta=DeltaMessage(role="assistant"), finish_reason=None)],
        )
        async with httpx.AsyncClient(timeout=60.0) as client:
            async with client.stream("POST", url, json=payload) as stream:
                async for line in stream.aiter_lines():
                    if not line:
                        await asyncio.sleep(0)
                        continue
                    # Ollama streams JSON lines with { "message": {"content": "..."}, "done": bool }
                    try:
                        import json as _json

                        obj = _json.loads(line)
                    except Exception:
                        continue
                    content = (obj.get("message") or {}).get("content")
                    if content:
                        yield ChatCompletionChunk(
                            id=chunk_id,
                            created=now,
                            model=model,
                            choices=[ChatChunkChoice(index=0, delta=DeltaMessage(content=content), finish_reason=None)],
                        )
                    if obj.get("done"):
                        break
        # final finish chunk
        yield ChatCompletionChunk(
            id=chunk_id,
            created=now,
            model=model,
            choices=[ChatChunkChoice(index=0, delta=DeltaMessage(), finish_reason="stop")],
        )
