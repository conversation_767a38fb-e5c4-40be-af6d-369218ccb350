# M-GAIF Developer Guide

## Overview

M-GAIF (Modular Generative AI Framework) is a production-ready, enterprise-grade AI framework with comprehensive plugin architecture, agent systems, security, and evaluation capabilities.

## Architecture Overview

M-GAIF follows a modular, plugin-based architecture with the following core systems:

### Core Systems
- **Plugin System**: Dynamic loading, hot-swapping, health monitoring
- **Agent System**: Planning, memory, tool orchestration, multi-agent coordination
- **Security System**: Authentication, authorization, threat detection, audit logging
- **RAG System**: Document processing, chunking, retrieval, grounding validation
- **Evaluation System**: Metrics, benchmarking, performance analysis
- **Workflow Engine**: Node-based execution, error handling, state management

## Quick Start

### Prerequisites
- Python 3.11+
- Virtual environment recommended

### Installation
```bash
# Create virtual environment
python -m venv .venv
source .venv/bin/activate  # Linux/Mac
# or
.\.venv\Scripts\Activate.ps1  # Windows PowerShell

# Install dependencies
pip install -r requirements.txt
```

### Basic Usage
```python
from core.plugins import PluginRegistry
from core.agent import Agent, ReActPlanner
from core.security import Auth<PERSON>anager, JWTAuthProvider

# Initialize plugin system
registry = PluginRegistry()
await registry.discover_plugins(Path("plugins/"))

# Create agent with planning
agent = Agent(
    planner=ReActPlanner(),
    tools=["search", "calculator"]
)

# Execute agent task
result = await agent.execute("Research AI safety and summarize findings")
```

## Plugin Development

### Creating a Plugin
```python
from core.plugins import Plugin, PluginStatus

class MyCustomPlugin(Plugin):
    def __init__(self, config: Dict[str, Any] = None):
        super().__init__("my_plugin", "Custom functionality", config)

    async def initialize(self) -> None:
        """Initialize plugin resources."""
        # Setup code here
        self.status = PluginStatus.ACTIVE

    async def shutdown(self) -> None:
        """Cleanup plugin resources."""
        # Cleanup code here
        self.status = PluginStatus.SHUTDOWN

    async def health_check(self) -> HealthCheckResult:
        """Check plugin health."""
        return HealthCheckResult(
            healthy=True,
            message="Plugin is healthy",
            metrics={"requests": 100}
        )
```

### Plugin Registration
```python
# Register plugin
registry = PluginRegistry()
await registry.register_plugin("my_plugin", MyCustomPlugin())

# Hot-swap plugin
await registry.hot_swap("my_plugin", "old_version", "new_version", config)
```

## Agent Development

### Creating Custom Agents
```python
from core.agent import Agent, ExecutionContext

class CustomAgent(Agent):
    async def execute(self, goal: str, context: ExecutionContext) -> AgentResult:
        # Custom agent logic
        plan = await self.planner.create_plan(goal)

        for step in plan.steps:
            # Execute step
            result = await self.execute_step(step)

        return AgentResult(
            success=True,
            result="Task completed",
            reasoning="Custom agent reasoning"
        )
```

### Multi-Agent Coordination
```python
from core.agent import AgentCoordinator, AgentTask, CoordinationStrategy

coordinator = AgentCoordinator()
coordinator.register_agent("researcher", research_agent)
coordinator.register_agent("writer", writing_agent)

tasks = [
    AgentTask("researcher", "Research topic", context),
    AgentTask("writer", "Write summary", context, dependencies=["researcher"])
]

result = await coordinator.execute_tasks(tasks, CoordinationStrategy.SEQUENTIAL)
```

## Security Integration

### Authentication Setup
```python
from core.security import AuthManager, JWTAuthProvider, UserRole

# Setup authentication
auth_provider = JWTAuthProvider(secret_key="your-secret-key")
auth_manager = AuthManager(auth_provider)

# Create user
user = await auth_provider.create_user(
    username="developer",
    email="<EMAIL>",
    password="secure_password",
    roles=[UserRole.DEVELOPER]
)

# Authenticate
token = await auth_manager.authenticate_user({
    "username": "developer",
    "password": "secure_password"
})
```

### Security Middleware
```python
from core.security.middleware import create_security_middleware

# Create security middleware instances
strict_mode = True  # Enable strict security validation
rate_limit = 100    # Requests per minute
allowed_tools = ["tokenize", "embed", "retriever", "llm", "workflow"]

security_middleware, tool_access_middleware = create_security_middleware(
    strict_mode=strict_mode,
    allowed_tools=allowed_tools,
    rate_limit=rate_limit
)

# Add middleware to FastAPI app
app.middleware("http")(security_middleware.dispatch)
app.middleware("http")(tool_access_middleware.dispatch)
```

**Note**: The current implementation uses a factory function `create_security_middleware()` that returns configured middleware instances. These are added using the `app.middleware("http")` decorator pattern rather than `app.add_middleware()`.

## RAG System Usage

### Document Processing
```python
from core.rag import Document, SemanticChunker, RAGPipeline

# Create document
document = Document(
    content="Your document content here...",
    title="Document Title",
    metadata={"author": "John Doe"}
)

# Setup chunking
chunker = SemanticChunker(chunk_size=512, overlap=50)
chunks = chunker.chunk_document(document)

# Create RAG pipeline
pipeline = RAGPipeline(
    chunker=chunker,
    retriever=hybrid_retriever,
    reranker=bm25_reranker
)

# Query pipeline
result = await pipeline.query("What is the main topic?")
```

## Evaluation Framework

### Using Standard Metrics
```python
from core.evaluation import BLEUMetric, ROUGEMetric, Evaluator

# Setup evaluator
evaluator = Evaluator()
evaluator.add_metric(BLEUMetric(max_order=4))
evaluator.add_metric(ROUGEMetric(variant="rouge_l"))

# Evaluate model
results = await evaluator.evaluate_model(
    model=llm_adapter,
    predictions=["Generated text..."],
    references=["Reference text..."]
)

print(f"BLEU Score: {results.metrics['bleu_4']}")
```

### Custom Metrics
```python
from core.evaluation import Metric, EvaluationResult

class CustomMetric(Metric):
    def compute(self, predictions: List[str], references: List[str] = None) -> EvaluationResult:
        # Custom metric computation
        score = calculate_custom_score(predictions, references)

        return EvaluationResult(
            metric_name=self.name,
            score=score,
            details={"sample_count": len(predictions)},
            metadata={"version": "1.0"},
            timestamp=datetime.now()
        )
```

## Configuration Management

### Plugin Configuration
```yaml
# plugins/config.yaml
plugins:
  tokenizer:
    type: "huggingface"
    config:
      model_name: "bert-base-uncased"
      max_length: 512

  embedder:
    type: "sentence_transformers"
    config:
      model_name: "all-MiniLM-L6-v2"
```

### Agent Configuration
```yaml
# agents/config.yaml
agents:
  research_agent:
    planner: "react"
    memory: "episodic"
    tools: ["search", "summarize"]
    max_steps: 10
    timeout: 300
```

## Testing and Benchmarking

### Unit Testing
```python
import pytest
from core.plugins import PluginRegistry

@pytest.mark.asyncio
async def test_plugin_loading():
    registry = PluginRegistry()
    await registry.discover_plugins(Path("test_plugins/"))

    assert "test_plugin" in registry.list_plugins()

    plugin = await registry.get_plugin("test_plugin")
    assert plugin.status == PluginStatus.ACTIVE
```

### Performance Benchmarking
```python
from core.evaluation import PerformanceProfiler

@pytest.mark.benchmark
async def test_agent_performance():
    profiler = PerformanceProfiler()

    with profiler.profile("agent_execution"):
        result = await agent.execute("Test task")

    report = profiler.get_report()
    assert report.avg_execution_time < 1.0  # seconds
```

## Deployment

### Docker Deployment
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "-m", "uvicorn", "core.edge.api:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Production Configuration
```python
# production_config.py
from core.security import EnhancedSecurityMiddleware
from core.plugins import PluginRegistry

# Production setup
registry = PluginRegistry()
await registry.discover_plugins(Path("plugins/"))

# Enable health monitoring
manager = ComponentManager(registry)
await manager.enable_health_monitoring(interval=30)

# Setup security
security_middleware, tool_access_middleware = create_security_middleware(
    strict_mode=True,
    rate_limit=100
)
app.middleware("http")(security_middleware.dispatch)
app.middleware("http")(tool_access_middleware.dispatch)
```

## Monitoring and Observability

### Health Monitoring
```python
from core.plugins import ComponentManager

manager = ComponentManager(registry)
await manager.enable_health_monitoring(interval=30)

# Check system health
health_status = await manager.get_system_health()
print(f"System Health: {health_status}")
```

### Audit Logging
```python
from core.security import AuditLogger

audit_logger = AuditLogger(
    log_file=Path("logs/audit.log"),
    structured_logging=True
)

# Log events
await audit_logger.log_system_event(
    "component_started",
    {"component": "plugin_registry"}
)
```

## Best Practices

### Plugin Development
- Implement proper health checks
- Handle initialization and shutdown gracefully
- Use configuration for customization
- Follow async/await patterns
- Include comprehensive error handling

### Agent Development
- Design agents for specific tasks
- Use appropriate planning algorithms
- Implement proper memory management
- Handle tool failures gracefully
- Include reflection and learning

### Security
- Always validate inputs
- Use proper authentication
- Implement audit logging
- Monitor for threats
- Follow principle of least privilege

### Performance
- Use async operations
- Implement proper caching
- Monitor resource usage
- Profile critical paths
- Optimize hot code paths

## Troubleshooting

### Common Issues
1. **Plugin Loading Failures**: Check plugin configuration and dependencies
2. **Authentication Errors**: Verify JWT secret and user credentials
3. **Memory Issues**: Monitor agent memory usage and implement cleanup
4. **Performance Problems**: Use profiler to identify bottlenecks

### Debug Mode
```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable debug mode
registry = PluginRegistry(debug=True)
```

## Contributing

### Code Style
- Follow PEP 8
- Use type hints
- Write comprehensive docstrings
- Include unit tests
- Update documentation

### Pull Request Process
1. Fork the repository
2. Create feature branch
3. Implement changes with tests
4. Update documentation
5. Submit pull request

## API Reference

For detailed API documentation, see:
- `docs/api.md` - Core API reference
- `docs/plugins.md` - Plugin development guide
- `docs/agents.md` - Agent system documentation
- `docs/security.md` - Security implementation guide
