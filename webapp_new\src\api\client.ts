import axios, { AxiosInstance } from 'axios'

export type ChatMessage = { role: 'system' | 'user' | 'assistant'; content: string }

function getBase(): string | undefined {
  const fromLs = localStorage.getItem('mgaif.apiBase') || undefined
  return fromLs || (import.meta.env.VITE_API_BASE as string | undefined)
}

export class ApiClient {
  private http: AxiosInstance
  private base?: string

  constructor() {
    this.base = getBase()
    this.http = axios.create({ baseURL: this.base })
  }

  setBase(base?: string) {
    this.base = base
    if (base) localStorage.setItem('mgaif.apiBase', base)
    this.http = axios.create({ baseURL: base })
  }

  // MCP tools
  async workflowRun(spec: any, state?: any) {
    const url = this.base ? `${this.base}/mcp/tools/workflow/run` : '/mcp/tools/workflow/run'
    const { data } = await this.http.post(url, { spec, state })
    return data as { state: any }
  }

  async retrieverIndex(items: Array<{ id: string; text: string }>) {
    const url = this.base ? `${this.base}/mcp/tools/retriever/index` : '/mcp/tools/retriever/index'
    const { data } = await this.http.post(url, { items })
    return data as { ok: boolean; count: number }
  }

  async retrieverSearch(query: string, top_k = 3) {
    const url = this.base ? `${this.base}/mcp/tools/retriever/search` : '/mcp/tools/retriever/search'
    const { data } = await this.http.post(url, { query, top_k })
    return data as { hits: Array<{ id: string; score: number }> }
  }

  async mcpChat(messages: ChatMessage[]) {
    const url = this.base ? `${this.base}/mcp/tools/llm/chat` : '/mcp/tools/llm/chat'
    const { data } = await this.http.post(url, { messages })
    return data
  }

  // Edge OpenAI-compatible
  async edgeChat(messages: ChatMessage[], model?: string) {
    const url = this.base ? `${this.base}/v1/chat/completions` : '/v1/chat/completions'
    const { data } = await this.http.post(url, { messages, model, stream: false })
    return data
  }

  async edgeChatStream(messages: ChatMessage[], onDelta: (text: string) => void, model?: string) {
    const url = (this.base ? `${this.base}/v1/chat/completions` : '/v1/chat/completions')
    const resp = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ messages, model, stream: true }),
    })
    if (!resp.body) throw new Error('No stream body')
    const reader = resp.body.getReader()
    const decoder = new TextDecoder()
    let buf = ''
    while (true) {
      const { value, done } = await reader.read()
      if (done) break
      buf += decoder.decode(value, { stream: true })
      let idx
      while ((idx = buf.indexOf('\n')) >= 0) {
        const line = buf.slice(0, idx).trim()
        buf = buf.slice(idx + 1)
        if (!line) continue
        try {
          const obj = JSON.parse(line)
          const delta = obj.choices?.[0]?.delta?.content
          if (delta) onDelta(delta)
        } catch {
          // ignore parse errors for partial lines
        }
      }
    }
  }
}

export const api = new ApiClient()
