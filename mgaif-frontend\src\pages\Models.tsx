import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Progress,
  message,
  Tabs,
  Row,
  Col,
  Statistic,
  Tag,
  Tooltip,
  Divider,
  Steps,
  Slider,
} from 'antd';
import {
  PlusOutlined,
  CloudUploadOutlined,
  RocketOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  BrainOutlined,
  FileTextOutlined,
  LineChartOutlined,
} from '@ant-design/icons';
import type { UploadProps, TableColumnsType } from 'antd';
import { api } from '../services/api';
import type { SLMConfig, FineTuningConfig, TrainingDataset, TrainingMetrics } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Step } = Steps;

export const Models: React.FC = () => {
  const [models, setModels] = useState<SLMConfig[]>([]);
  const [trainingDatasets, setTrainingDatasets] = useState<TrainingDataset[]>([]);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [fineTuneModalVisible, setFineTuneModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedModel, setSelectedModel] = useState<SLMConfig | null>(null);
  const [activeTab, setActiveTab] = useState('models');
  
  const [form] = Form.useForm();
  const [fineTuneForm] = Form.useForm();
  const [uploadForm] = Form.useForm();

  // Load data on component mount
  useEffect(() => {
    loadModels();
    loadTrainingDatasets();
  }, []);

  const loadModels = async () => {
    try {
      setLoading(true);
      // Mock data for development
      setModels([
        {
          id: '1',
          name: 'Customer Support Model',
          description: 'Fine-tuned model for customer support conversations',
          baseModel: 'gpt-3.5-turbo',
          modelType: 'chat',
          parameters: {
            temperature: 0.7,
            maxTokens: 1000,
            topP: 0.9,
            frequencyPenalty: 0.1,
            presencePenalty: 0.1,
          },
          status: 'ready',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-15T10:30:00Z',
          fineTuning: {
            trainingData: [],
            hyperparameters: {
              learningRate: 0.0001,
              batchSize: 16,
              epochs: 3,
              warmupSteps: 100,
            },
            validationSplit: 0.2,
            status: 'completed',
            progress: 100,
            metrics: {
              loss: 0.45,
              accuracy: 0.92,
              perplexity: 1.8,
              validationLoss: 0.52,
              validationAccuracy: 0.89,
            }
          }
        },
        {
          id: '2',
          name: 'Document Classifier',
          description: 'Model for classifying documents by category',
          baseModel: 'text-embedding-ada-002',
          modelType: 'embedding',
          parameters: {
            temperature: 0.0,
            maxTokens: 512,
            topP: 1.0,
            frequencyPenalty: 0.0,
            presencePenalty: 0.0,
          },
          status: 'training',
          createdAt: '2024-01-14T15:45:00Z',
          updatedAt: '2024-01-16T09:20:00Z',
          fineTuning: {
            trainingData: [],
            hyperparameters: {
              learningRate: 0.0005,
              batchSize: 32,
              epochs: 5,
              warmupSteps: 50,
            },
            validationSplit: 0.15,
            status: 'running',
            progress: 65,
          }
        }
      ]);
    } catch (error) {
      console.error('Failed to load models:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTrainingDatasets = async () => {
    try {
      // Mock data for development
      setTrainingDatasets([
        {
          id: '1',
          name: 'Customer Support Conversations',
          type: 'chat',
          size: 5000,
          examples: [],
          uploadedAt: '2024-01-10T12:00:00Z'
        },
        {
          id: '2',
          name: 'Document Categories',
          type: 'classification',
          size: 2500,
          examples: [],
          uploadedAt: '2024-01-12T14:30:00Z'
        }
      ]);
    } catch (error) {
      console.error('Failed to load training datasets:', error);
    }
  };

  const handleCreateModel = async () => {
    try {
      const values = await form.validateFields();
      
      const newModel: Partial<SLMConfig> = {
        name: values.name,
        description: values.description,
        baseModel: values.baseModel,
        modelType: values.modelType,
        parameters: {
          temperature: values.temperature || 0.7,
          maxTokens: values.maxTokens || 1000,
          topP: values.topP || 1.0,
          frequencyPenalty: values.frequencyPenalty || 0.0,
          presencePenalty: values.presencePenalty || 0.0,
        },
        status: 'draft'
      };

      // In real implementation, call API
      // await api.models.create(newModel);
      
      message.success('Model created successfully!');
      setCreateModalVisible(false);
      form.resetFields();
      loadModels();
    } catch (error) {
      message.error('Failed to create model');
    }
  };

  const handleStartFineTuning = async () => {
    try {
      const values = await fineTuneForm.validateFields();
      
      if (!selectedModel) return;

      const fineTuningConfig: FineTuningConfig = {
        trainingData: values.trainingDatasets,
        hyperparameters: {
          learningRate: values.learningRate,
          batchSize: values.batchSize,
          epochs: values.epochs,
          warmupSteps: values.warmupSteps,
        },
        validationSplit: values.validationSplit,
        status: 'pending'
      };

      // In real implementation, call API
      // await api.models.startFineTuning(selectedModel.id, fineTuningConfig);
      
      message.success('Fine-tuning started successfully!');
      setFineTuneModalVisible(false);
      fineTuneForm.resetFields();
      loadModels();
    } catch (error) {
      message.error('Failed to start fine-tuning');
    }
  };

  const handleUploadDataset: UploadProps['customRequest'] = async (options) => {
    const { file, onSuccess, onError, onProgress } = options;
    
    try {
      // Simulate upload progress
      let progress = 0;
      const interval = setInterval(() => {
        progress += 10;
        onProgress?.({ percent: progress });
        if (progress >= 100) {
          clearInterval(interval);
        }
      }, 200);

      // In real implementation, call API
      // await api.models.uploadTrainingData(file as File, uploadForm.getFieldsValue());

      onSuccess?.('Upload successful');
      message.success(`${file.name} uploaded successfully`);
      setUploadModalVisible(false);
      uploadForm.resetFields();
      loadTrainingDatasets();
    } catch (error) {
      onError?.(error as Error);
      message.error(`Failed to upload ${file.name}`);
    }
  };

  const handleDeleteModel = async (id: string) => {
    try {
      // In real implementation, call API
      // await api.models.delete(id);
      
      message.success('Model deleted successfully');
      loadModels();
    } catch (error) {
      message.error('Failed to delete model');
    }
  };

  const handleDeployModel = async (model: SLMConfig) => {
    try {
      // In real implementation, call API
      // await api.models.deploy(model.id);
      
      message.success(`${model.name} deployed successfully`);
      loadModels();
    } catch (error) {
      message.error('Failed to deploy model');
    }
  };

  const modelColumns: TableColumnsType<SLMConfig> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <BrainOutlined />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Base Model',
      dataIndex: 'baseModel',
      key: 'baseModel',
      render: (baseModel) => <Tag color="blue">{baseModel}</Tag>,
    },
    {
      title: 'Type',
      dataIndex: 'modelType',
      key: 'modelType',
      render: (type) => <Tag>{type.toUpperCase()}</Tag>,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        const colors = {
          draft: 'default',
          training: 'processing',
          ready: 'success',
          deployed: 'green',
          failed: 'error'
        };
        
        return (
          <div>
            <Tag color={colors[status]}>{status.toUpperCase()}</Tag>
            {status === 'training' && record.fineTuning?.progress && (
              <div style={{ marginTop: '4px' }}>
                <Progress 
                  percent={record.fineTuning.progress} 
                  size="small" 
                  status="active"
                />
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="View Details">
            <Button icon={<EyeOutlined />} size="small" />
          </Tooltip>
          <Tooltip title="Fine-tune">
            <Button 
              icon={<SettingOutlined />} 
              size="small"
              onClick={() => {
                setSelectedModel(record);
                setFineTuneModalVisible(true);
              }}
              disabled={record.status === 'training'}
            />
          </Tooltip>
          <Tooltip title="Deploy">
            <Button 
              icon={<RocketOutlined />} 
              size="small"
              type="primary"
              onClick={() => handleDeployModel(record)}
              disabled={record.status !== 'ready'}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger 
              onClick={() => handleDeleteModel(record.id)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  const datasetColumns: TableColumnsType<TrainingDataset> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <FileTextOutlined />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.type} • {record.size} examples
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => <Tag color="green">{type.toUpperCase()}</Tag>,
    },
    {
      title: 'Size',
      dataIndex: 'size',
      key: 'size',
      render: (size) => `${size.toLocaleString()} examples`,
    },
    {
      title: 'Uploaded',
      dataIndex: 'uploadedAt',
      key: 'uploadedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: () => (
        <Space>
          <Button icon={<EyeOutlined />} size="small">
            Preview
          </Button>
          <Button icon={<DeleteOutlined />} size="small" danger>
            Delete
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>Small Language Models</Title>
        <Paragraph type="secondary">
          Create, fine-tune, and deploy custom language models for your specific use cases
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="Total Models"
              value={models.length}
              prefix={<BrainOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Ready Models"
              value={models.filter(m => m.status === 'ready').length}
              prefix={<RocketOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Training"
              value={models.filter(m => m.status === 'training').length}
              prefix={<LineChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="Training Datasets"
              value={trainingDatasets.length}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setCreateModalVisible(true)}
            >
              Create Model
            </Button>
            <Button 
              icon={<CloudUploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              Upload Training Data
            </Button>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="Models" key="models">
            <Table
              columns={modelColumns}
              dataSource={models}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          
          <TabPane tab="Training Datasets" key="datasets">
            <Table
              columns={datasetColumns}
              dataSource={trainingDatasets}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* Create Model Modal */}
      <Modal
        title="Create New Model"
        open={createModalVisible}
        onOk={handleCreateModel}
        onCancel={() => setCreateModalVisible(false)}
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Model Name"
            name="name"
            rules={[{ required: true, message: 'Please enter model name' }]}
          >
            <Input placeholder="Enter model name" />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe the model's purpose and use case" />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="Base Model"
                name="baseModel"
                rules={[{ required: true, message: 'Please select base model' }]}
              >
                <Select placeholder="Select base model">
                  <Select.Option value="gpt-3.5-turbo">GPT-3.5 Turbo</Select.Option>
                  <Select.Option value="gpt-4">GPT-4</Select.Option>
                  <Select.Option value="text-embedding-ada-002">Text Embedding Ada 002</Select.Option>
                  <Select.Option value="claude-3-haiku">Claude 3 Haiku</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="Model Type"
                name="modelType"
                rules={[{ required: true, message: 'Please select model type' }]}
              >
                <Select placeholder="Select model type">
                  <Select.Option value="chat">Chat</Select.Option>
                  <Select.Option value="completion">Completion</Select.Option>
                  <Select.Option value="embedding">Embedding</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Divider>Model Parameters</Divider>
          
          <Form.Item label="Temperature" name="temperature">
            <Slider min={0} max={2} step={0.1} defaultValue={0.7} />
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Max Tokens" name="maxTokens">
                <Input type="number" defaultValue={1000} min={1} max={4000} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Top P" name="topP">
                <Input type="number" defaultValue={1.0} min={0} max={1} step={0.1} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Fine-tuning Modal */}
      <Modal
        title={`Fine-tune ${selectedModel?.name}`}
        open={fineTuneModalVisible}
        onOk={handleStartFineTuning}
        onCancel={() => setFineTuneModalVisible(false)}
        width={800}
      >
        <Steps current={0} style={{ marginBottom: '24px' }}>
          <Step title="Select Data" />
          <Step title="Configure" />
          <Step title="Train" />
          <Step title="Evaluate" />
        </Steps>
        
        <Form form={fineTuneForm} layout="vertical">
          <Form.Item
            label="Training Datasets"
            name="trainingDatasets"
            rules={[{ required: true, message: 'Please select training datasets' }]}
          >
            <Select mode="multiple" placeholder="Select training datasets">
              {trainingDatasets.map(dataset => (
                <Select.Option key={dataset.id} value={dataset.id}>
                  {dataset.name} ({dataset.size} examples)
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Divider>Hyperparameters</Divider>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Learning Rate" name="learningRate">
                <Input type="number" defaultValue={0.0001} step={0.0001} min={0.00001} max={0.01} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Batch Size" name="batchSize">
                <Select defaultValue={16}>
                  <Select.Option value={8}>8</Select.Option>
                  <Select.Option value={16}>16</Select.Option>
                  <Select.Option value={32}>32</Select.Option>
                  <Select.Option value={64}>64</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label="Epochs" name="epochs">
                <Input type="number" defaultValue={3} min={1} max={10} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="Validation Split" name="validationSplit">
                <Slider min={0.1} max={0.3} step={0.05} defaultValue={0.2} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* Upload Dataset Modal */}
      <Modal
        title="Upload Training Dataset"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={uploadForm} layout="vertical">
          <Form.Item
            label="Dataset Name"
            name="name"
            rules={[{ required: true, message: 'Please enter dataset name' }]}
          >
            <Input placeholder="Enter dataset name" />
          </Form.Item>
          
          <Form.Item
            label="Dataset Type"
            name="type"
            rules={[{ required: true, message: 'Please select dataset type' }]}
          >
            <Select placeholder="Select dataset type">
              <Select.Option value="chat">Chat Conversations</Select.Option>
              <Select.Option value="completion">Text Completion</Select.Option>
              <Select.Option value="classification">Classification</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item label="Dataset File">
            <Upload.Dragger
              customRequest={handleUploadDataset}
              accept=".jsonl,.csv,.txt"
              maxCount={1}
            >
              <p className="ant-upload-drag-icon">
                <CloudUploadOutlined />
              </p>
              <p className="ant-upload-text">
                Click or drag file to this area to upload
              </p>
              <p className="ant-upload-hint">
                Support JSONL, CSV, and TXT formats
              </p>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
