"""Component manager for hot-swappable plugin management."""

from __future__ import annotations

import asyncio
import logging
from typing import Any, Dict, List, Optional, Callable
from datetime import datetime, timedelta

from .base import Plugin, PluginStatus, HealthStatus, HealthCheckResult
from .registry import PluginRegistry

logger = logging.getLogger(__name__)


class ComponentManager:
    """Manages hot-swappable components with health monitoring.
    
    Provides high-level component management including hot-swapping,
    health monitoring, automatic failover, and performance tracking.
    
    Features:
    - Hot-swapping with zero downtime
    - Automatic health monitoring
    - Failover to backup components
    - Blue-green deployments
    - Canary deployments with traffic splitting
    - Performance metrics collection
    
    Example:
        >>> manager = ComponentManager(registry)
        >>> await manager.set_active_component("tokenizer", "simple")
        >>> await manager.hot_swap_component("tokenizer", "huggingface", config)
        >>> health = await manager.get_component_health("tokenizer")
    """
    
    def __init__(self, registry: PluginRegistry, health_check_interval: float = 30.0):
        """Initialize component manager.
        
        Args:
            registry: Plugin registry for component access
            health_check_interval: Seconds between health checks
        """
        self.registry = registry
        self.health_check_interval = health_check_interval
        
        # Active components by type
        self._active_components: Dict[str, Plugin] = {}
        
        # Backup components for failover
        self._backup_components: Dict[str, Plugin] = {}
        
        # Health monitoring
        self._health_tasks: Dict[str, asyncio.Task] = {}
        self._health_results: Dict[str, HealthCheckResult] = {}
        
        # Traffic splitting for canary deployments
        self._canary_components: Dict[str, Plugin] = {}
        self._canary_traffic_percent: Dict[str, float] = {}
        
        # Component locks for thread safety
        self._component_locks: Dict[str, asyncio.Lock] = {}
        
        # Event callbacks
        self._health_callbacks: List[Callable[[str, HealthCheckResult], None]] = []
        self._swap_callbacks: List[Callable[[str, str, str], None]] = []
    
    async def set_active_component(self, component_type: str, plugin_name: str, 
                                  config: Dict[str, Any] = None) -> None:
        """Set the active component for a given type.
        
        Args:
            component_type: Type of component (e.g., "tokenizer")
            plugin_name: Name of plugin to make active
            config: Optional configuration for the plugin
            
        Raises:
            PluginNotFoundError: If plugin not found
        """
        # Get or create component lock
        if component_type not in self._component_locks:
            self._component_locks[component_type] = asyncio.Lock()
        
        async with self._component_locks[component_type]:
            # Get plugin from registry
            plugin = await self.registry.get_plugin(component_type, plugin_name)
            
            # Initialize if needed
            if plugin.status != PluginStatus.ACTIVE:
                await plugin.initialize(config or {})
                plugin.status = PluginStatus.ACTIVE
            
            # Shutdown old active component if exists
            if component_type in self._active_components:
                old_plugin = self._active_components[component_type]
                await old_plugin.shutdown()
                
                # Stop health monitoring for old component
                if component_type in self._health_tasks:
                    self._health_tasks[component_type].cancel()
            
            # Set new active component
            self._active_components[component_type] = plugin
            
            # Start health monitoring
            self._health_tasks[component_type] = asyncio.create_task(
                self._monitor_component_health(component_type, plugin)
            )
            
            logger.info(f"Set active component {component_type} -> {plugin_name}")
    
    async def hot_swap_component(self, component_type: str, new_plugin_name: str,
                               config: Dict[str, Any] = None) -> None:
        """Hot-swap a component with zero downtime.
        
        Args:
            component_type: Type of component to swap
            new_plugin_name: Name of new plugin to swap in
            config: Configuration for new plugin
        """
        if component_type not in self._active_components:
            await self.set_active_component(component_type, new_plugin_name, config)
            return
        
        async with self._component_locks[component_type]:
            old_plugin = self._active_components[component_type]
            old_name = old_plugin.name
            
            # Get new plugin
            new_plugin = await self.registry.get_plugin(component_type, new_plugin_name)
            
            # Initialize new plugin
            if new_plugin.status != PluginStatus.ACTIVE:
                await new_plugin.initialize(config or {})
                new_plugin.status = PluginStatus.ACTIVE
            
            # Atomic swap
            self._active_components[component_type] = new_plugin
            
            # Update health monitoring
            if component_type in self._health_tasks:
                self._health_tasks[component_type].cancel()
            
            self._health_tasks[component_type] = asyncio.create_task(
                self._monitor_component_health(component_type, new_plugin)
            )
            
            # Graceful shutdown of old plugin
            await old_plugin.shutdown()
            
            # Notify callbacks
            for callback in self._swap_callbacks:
                try:
                    callback(component_type, old_name, new_plugin_name)
                except Exception as e:
                    logger.warning(f"Swap callback failed: {e}")
            
            logger.info(f"Hot-swapped {component_type}: {old_name} -> {new_plugin_name}")
    
    def get_active_component(self, component_type: str) -> Optional[Plugin]:
        """Get the currently active component.
        
        Args:
            component_type: Type of component to get
            
        Returns:
            Active plugin instance or None if not set
        """
        return self._active_components.get(component_type)
    
    async def get_component_health(self, component_type: str) -> Optional[HealthCheckResult]:
        """Get the latest health check result for a component.
        
        Args:
            component_type: Type of component
            
        Returns:
            Latest health check result or None if not monitored
        """
        return self._health_results.get(component_type)
    
    async def set_backup_component(self, component_type: str, plugin_name: str,
                                  config: Dict[str, Any] = None) -> None:
        """Set a backup component for automatic failover.
        
        Args:
            component_type: Type of component
            plugin_name: Name of backup plugin
            config: Configuration for backup plugin
        """
        plugin = await self.registry.get_plugin(component_type, plugin_name)
        
        if plugin.status != PluginStatus.ACTIVE:
            await plugin.initialize(config or {})
            plugin.status = PluginStatus.ACTIVE
        
        self._backup_components[component_type] = plugin
        logger.info(f"Set backup component {component_type} -> {plugin_name}")
    
    async def start_canary_deployment(self, component_type: str, canary_plugin_name: str,
                                    traffic_percent: float, config: Dict[str, Any] = None) -> None:
        """Start canary deployment with traffic splitting.
        
        Args:
            component_type: Type of component
            canary_plugin_name: Name of canary plugin
            traffic_percent: Percentage of traffic to route to canary (0-100)
            config: Configuration for canary plugin
        """
        if not 0 <= traffic_percent <= 100:
            raise ValueError("Traffic percent must be between 0 and 100")
        
        # Get and initialize canary plugin
        canary_plugin = await self.registry.get_plugin(component_type, canary_plugin_name)
        
        if canary_plugin.status != PluginStatus.ACTIVE:
            await canary_plugin.initialize(config or {})
            canary_plugin.status = PluginStatus.ACTIVE
        
        self._canary_components[component_type] = canary_plugin
        self._canary_traffic_percent[component_type] = traffic_percent
        
        logger.info(f"Started canary deployment {component_type}: {canary_plugin_name} ({traffic_percent}%)")
    
    def get_component_for_request(self, component_type: str, request_id: str = None) -> Optional[Plugin]:
        """Get component for a request, considering canary deployments.
        
        Args:
            component_type: Type of component
            request_id: Optional request ID for consistent routing
            
        Returns:
            Plugin to use for this request
        """
        # Check if canary deployment is active
        if component_type in self._canary_components:
            traffic_percent = self._canary_traffic_percent[component_type]
            
            # Simple hash-based routing for consistency
            if request_id:
                hash_val = hash(request_id) % 100
                if hash_val < traffic_percent:
                    return self._canary_components[component_type]
            else:
                # Random routing
                import random
                if random.random() * 100 < traffic_percent:
                    return self._canary_components[component_type]
        
        return self._active_components.get(component_type)
    
    async def promote_canary(self, component_type: str) -> None:
        """Promote canary to active component.
        
        Args:
            component_type: Type of component
        """
        if component_type not in self._canary_components:
            raise ValueError(f"No canary deployment for {component_type}")
        
        canary_plugin = self._canary_components[component_type]
        await self.hot_swap_component(component_type, canary_plugin.name)
        
        # Clean up canary deployment
        del self._canary_components[component_type]
        del self._canary_traffic_percent[component_type]
        
        logger.info(f"Promoted canary to active: {component_type}")
    
    async def _monitor_component_health(self, component_type: str, plugin: Plugin) -> None:
        """Monitor component health in background task."""
        while True:
            try:
                # Perform health check
                health_result = await plugin.health_check()
                self._health_results[component_type] = health_result
                
                # Check for unhealthy status
                if health_result.status == HealthStatus.UNHEALTHY:
                    logger.warning(f"Component {component_type} is unhealthy: {health_result.message}")
                    
                    # Attempt failover to backup if available
                    if component_type in self._backup_components:
                        backup_plugin = self._backup_components[component_type]
                        backup_health = await backup_plugin.health_check()
                        
                        if backup_health.status == HealthStatus.HEALTHY:
                            logger.info(f"Failing over {component_type} to backup")
                            await self.hot_swap_component(component_type, backup_plugin.name)
                
                # Notify health callbacks
                for callback in self._health_callbacks:
                    try:
                        callback(component_type, health_result)
                    except Exception as e:
                        logger.warning(f"Health callback failed: {e}")
                
                await asyncio.sleep(self.health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitoring error for {component_type}: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    def add_health_callback(self, callback: Callable[[str, HealthCheckResult], None]) -> None:
        """Add callback for health check events."""
        self._health_callbacks.append(callback)
    
    def add_swap_callback(self, callback: Callable[[str, str, str], None]) -> None:
        """Add callback for component swap events."""
        self._swap_callbacks.append(callback)
    
    async def shutdown(self) -> None:
        """Shutdown component manager and all components."""
        # Cancel health monitoring tasks
        for task in self._health_tasks.values():
            task.cancel()
        
        # Shutdown all active components
        for plugin in self._active_components.values():
            await plugin.shutdown()
        
        # Shutdown backup components
        for plugin in self._backup_components.values():
            await plugin.shutdown()
        
        # Shutdown canary components
        for plugin in self._canary_components.values():
            await plugin.shutdown()
        
        logger.info("Component manager shutdown complete")
