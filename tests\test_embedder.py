from plugins.embedders.simple_embedder import <PERSON>Embedder
from tests.realistic_data import get_realistic_text


def test_embedder_determinism_and_shape(benchmarker):
    emb = SimpleEmbedder(dim=8)
    # Use realistic texts including a duplicate for determinism test
    realistic_texts = [
        get_realistic_text("short", 0),
        get_realistic_text("short", 1),
        get_realistic_text("short", 2),
        get_realistic_text("short", 0)  # Duplicate for determinism test
    ]
    vecs = emb.embed(realistic_texts)
    assert len(vecs) == len(realistic_texts)
    for v in vecs:
        assert len(v) == 8
        # unit-normalized or close
        norm = sum(x * x for x in v) ** 0.5
        assert abs(norm - 1.0) < 1e-6
    # determinism - first and last should be identical
    assert vecs[0] == vecs[3]

    # Benchmark with realistic texts
    benchmark_texts = [get_realistic_text("short", i) for i in range(3)]
    metrics = benchmarker(lambda: emb.embed(benchmark_texts), iterations=100)
    assert metrics["p95_ms"] < 2.5
