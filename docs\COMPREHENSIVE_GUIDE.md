# M-GAIF Comprehensive Developer Guide

## Table of Contents
- [Overview](#overview)
- [Architecture Deep Dive](#architecture-deep-dive)
- [Security Features](#security-features)
- [Performance Optimization](#performance-optimization)
- [API Reference](#api-reference)
- [Plugin Development](#plugin-development)
- [Deployment Guide](#deployment-guide)
- [Troubleshooting](#troubleshooting)

## Overview

The Modular Generative AI Framework (M-GAIF) is a production-ready framework for building AI applications with pluggable components, comprehensive security, and enterprise-grade observability.

### Key Features
- **🔧 Modular Architecture**: Plugin-based system with standardized contracts
- **🔒 Security First**: Input validation, PII detection, prompt injection protection
- **📊 Observability**: OpenTelemetry integration with metrics and tracing
- **⚡ High Performance**: Optimized for throughput with async/await patterns
- **🚀 Production Ready**: Docker, CI/CD, comprehensive testing

## Architecture Deep Dive

### Core Components

#### 1. Contracts (`core/contracts/`)
Define standard interfaces for all framework components:

```python
# Example: LLM Contract
from abc import ABC, abstractmethod
from typing import List, AsyncIterator

class LLMAdapter(ABC):
    @abstractmethod
    async def chat_completion(self, messages: List[dict]) -> dict:
        pass
    
    @abstractmethod
    async def chat_completion_stream(self, messages: List[dict]) -> AsyncIterator[dict]:
        pass
```

#### 2. Security Layer (`core/security/`)
Comprehensive security middleware:

- **Input Validation**: Detects prompt injection, validates content
- **PII Protection**: Automatically redacts sensitive information
- **Rate Limiting**: Configurable request throttling
- **Access Control**: Tool-based permission system

#### 3. Plugin System (`plugins/`)
Extensible components with hot-swappable implementations:

- **Tokenizers**: Text tokenization strategies
- **Embedders**: Text-to-vector conversion
- **Retrievers**: Document search and retrieval

#### 4. Workflow Engine (`core/workflow/`)
Orchestrates complex AI pipelines with:

- **Node-based execution**: Define processing steps
- **Conditional branching**: Dynamic workflow paths
- **State management**: Persistent execution context
- **Error handling**: Robust failure recovery

### Data Flow

```
Request → Security Middleware → API Router → Plugin Components → Response
    ↓
Observability Layer (Metrics, Logs, Traces)
```

## Security Features

### Input Validation

The framework includes comprehensive input validation:

```python
from core.security.input_validation import InputValidator

validator = InputValidator(strict_mode=True)

# Validates and sanitizes input
safe_text = validator.validate_input(
    user_input,
    check_pii=True,
    check_injection=True
)
```

### Security Patterns Detected

- **Prompt Injection**: "Ignore all previous instructions"
- **System Prompts**: "Reveal your system prompt"
- **Code Injection**: SQL, JavaScript, shell commands
- **PII**: SSN, credit cards, emails, phone numbers

### Configuration

```bash
# Enable strict security mode
export MGAIF_SECURITY_STRICT=true

# Set rate limits
export MGAIF_RATE_LIMIT=100

# Configure allowed tools
export MGAIF_ALLOWED_TOOLS="tokenize,embed,chat"
```

## Performance Optimization

### Benchmark Results

Based on our performance testing:

| Component | Operations/Second | Notes |
|-----------|------------------|-------|
| Tokenizer | ~68,000 | Regex-based, highly optimized |
| Embedder | ~13,000 | SHA-256 hash to vector mapping |
| Retriever | ~950 queries | In-memory vector search |
| Overall | ~11,900 | Combined operations |

### Optimization Strategies

1. **Async/Await**: All I/O operations are non-blocking
2. **Connection Pooling**: Efficient HTTP client management
3. **Batch Processing**: Process multiple items together
4. **Caching**: Intelligent caching at multiple layers

### Running Benchmarks

```bash
# Local component benchmarks
python run_benchmarks.py

# API benchmarks (requires running servers)
python benchmarks/performance_benchmark.py
```

## API Reference

### Edge API (OpenAI-Compatible)

#### Chat Completions
```http
POST /v1/chat/completions
Content-Type: application/json

{
  "messages": [
    {"role": "user", "content": "Hello!"}
  ],
  "stream": false
}
```

#### Health Check
```http
GET /health
```

#### Metrics
```http
GET /metrics
```

### MCP API

#### Tokenize Text
```http
POST /mcp/tools/tokenize
Content-Type: application/json

{
  "text": "Hello, world!"
}
```

#### Generate Embeddings
```http
POST /mcp/tools/embed
Content-Type: application/json

{
  "text": "Hello, world!"
}
```

#### Search Documents
```http
POST /mcp/tools/retriever
Content-Type: application/json

{
  "query": "search term",
  "top_k": 5
}
```

## Plugin Development

### Creating a Custom Tokenizer

```python
from core.text.tokenizer import Tokenizer
from typing import List

class CustomTokenizer(Tokenizer):
    def __init__(self):
        self.vocab = {}
        self.reverse_vocab = {}
    
    def encode(self, text: str) -> List[int]:
        # Your tokenization logic here
        tokens = text.split()
        return [self._get_token_id(token) for token in tokens]
    
    def decode(self, token_ids: List[int]) -> str:
        tokens = [self.reverse_vocab.get(id, "<UNK>") for id in token_ids]
        return " ".join(tokens)
    
    def _get_token_id(self, token: str) -> int:
        if token not in self.vocab:
            token_id = len(self.vocab)
            self.vocab[token] = token_id
            self.reverse_vocab[token_id] = token
        return self.vocab[token]
```

### Creating a Custom Embedder

```python
from core.text.embeddings import Embedder
from typing import List, Union
import numpy as np

class CustomEmbedder(Embedder):
    def __init__(self, dim: int = 128):
        self.dim = dim
    
    def embed(self, texts: Union[str, List[str]]) -> Union[List[float], List[List[float]]]:
        if isinstance(texts, str):
            return self._embed_single(texts)
        return [self._embed_single(text) for text in texts]
    
    def _embed_single(self, text: str) -> List[float]:
        # Your embedding logic here
        # This is a simple example using hash-based embeddings
        import hashlib
        hash_obj = hashlib.sha256(text.encode())
        hash_bytes = hash_obj.digest()
        
        # Convert to float vector
        vector = []
        for i in range(0, min(len(hash_bytes), self.dim // 8), 4):
            val = int.from_bytes(hash_bytes[i:i+4], 'big')
            vector.append(float(val) / (2**32))
        
        # Pad or truncate to desired dimension
        while len(vector) < self.dim:
            vector.append(0.0)
        
        return vector[:self.dim]
```

### Plugin Registration

Register your plugins in the appropriate API:

```python
# In core/mcp/api.py
from your_plugin.custom_tokenizer import CustomTokenizer

# Initialize your plugin
custom_tokenizer = CustomTokenizer()

@app.post("/mcp/tools/custom_tokenize")
async def custom_tokenize(request: dict):
    text = request["text"]
    tokens = custom_tokenizer.encode(text)
    return {"tokens": tokens}
```

## Deployment Guide

### Docker Deployment

```bash
# Build the image
docker build -t mgaif:latest .

# Run with docker-compose
cd deploy
docker-compose up -d
```

### Environment Variables

```bash
# API Configuration
MGAIF_EDGE_ADAPTER=echo  # or ollama
MGAIF_OLLAMA_BASE_URL=http://localhost:11434

# Security Configuration
MGAIF_SECURITY_STRICT=false
MGAIF_RATE_LIMIT=100

# Observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
LOG_LEVEL=info
```

### Production Checklist

- [ ] Configure security settings
- [ ] Set up monitoring and alerting
- [ ] Configure log aggregation
- [ ] Set resource limits
- [ ] Enable health checks
- [ ] Configure backup strategies
- [ ] Set up CI/CD pipelines

## Troubleshooting

### Common Issues

#### 1. Import Errors
```bash
# Ensure you're in the project root and virtual environment is activated
cd /path/to/mgaif
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
```

#### 2. API Not Responding
```bash
# Check if the service is running
curl http://localhost:8000/health

# Check logs
docker-compose logs edge
```

#### 3. Performance Issues
```bash
# Run benchmarks to identify bottlenecks
python run_benchmarks.py

# Check resource usage
docker stats
```

#### 4. Security Violations
```bash
# Check security logs
grep "SecurityViolation" logs/app.log

# Adjust security settings if needed
export MGAIF_SECURITY_STRICT=false
```

### Debug Mode

Enable debug logging:

```bash
export LOG_LEVEL=debug
uvicorn core.edge.api:app --reload --log-level debug
```

### Performance Profiling

```python
import cProfile
import pstats

# Profile your code
profiler = cProfile.Profile()
profiler.enable()

# Your code here

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(20)
```

## Contributing

### Development Setup

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

### Code Standards

- Follow PEP 8 style guidelines
- Add type hints to all functions
- Write comprehensive tests
- Update documentation
- Ensure security best practices

### Testing

```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=core --cov=plugins

# Run security tests
pytest tests/test_security.py -v
```
