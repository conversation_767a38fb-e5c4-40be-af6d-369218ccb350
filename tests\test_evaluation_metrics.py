"""Tests for evaluation metrics.

This module tests the various evaluation metrics including BLEU, ROUGE,
BERTScore, and Accuracy metrics.
"""

import pytest
from datetime import datetime

from core.evaluation.base import Metric, EvaluationResult, EvaluationError
from core.evaluation.metrics import BLEUMetric, ROUGEMetric, BERTScoreMetric, AccuracyMetric


class TestBLEUMetric:
    """Test BLEU metric implementation."""
    
    def test_bleu_metric_creation(self):
        """Test basic BLEU metric creation."""
        bleu = BLEUMetric(max_order=4, smooth=True)
        
        assert bleu.name == "bleu"
        assert bleu.max_order == 4
        assert bleu.smooth is True
        assert "n-gram overlap" in bleu.description.lower()
    
    def test_bleu_perfect_match(self):
        """Test BLEU with perfect match."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert isinstance(result, EvaluationResult)
        assert result.metric_name == "bleu"
        assert result.score == 1.0  # Perfect match
        assert "precision" in result.details
        assert "brevity_penalty" in result.details
    
    def test_bleu_partial_match(self):
        """Test BLEU with partial match."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = ["the cat sat on the mat"]
        references = ["a cat was sitting on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert 0.0 <= result.score <= 1.0
        assert result.score > 0.0  # Should have some overlap
        assert result.score < 1.0  # Not perfect match
    
    def test_bleu_no_match(self):
        """Test BLEU with no match."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = ["completely different text"]
        references = ["the cat sat on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert result.score == 0.0  # No overlap
    
    def test_bleu_multiple_predictions(self):
        """Test BLEU with multiple predictions."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = [
            "the cat sat on the mat",
            "a dog ran in the park",
            "birds fly in the sky"
        ]
        references = [
            "the cat sat on the mat",
            "the dog ran in the park",
            "birds fly in the sky"
        ]
        
        result = bleu.compute(predictions, references)
        
        assert 0.0 <= result.score <= 1.0
        assert result.score > 0.5  # Should have good overlap
    
    def test_bleu_empty_prediction(self):
        """Test BLEU with empty prediction."""
        bleu = BLEUMetric(max_order=4)
        
        predictions = [""]
        references = ["the cat sat on the mat"]
        
        result = bleu.compute(predictions, references)
        
        assert result.score == 0.0
    
    def test_bleu_smoothing(self):
        """Test BLEU with smoothing enabled."""
        bleu_smooth = BLEUMetric(max_order=4, smooth=True)
        bleu_no_smooth = BLEUMetric(max_order=4, smooth=False)
        
        predictions = ["cat"]  # Very short prediction
        references = ["the cat sat on the mat"]
        
        result_smooth = bleu_smooth.compute(predictions, references)
        result_no_smooth = bleu_no_smooth.compute(predictions, references)
        
        # Smoothing should help with short predictions
        assert result_smooth.score >= result_no_smooth.score


class TestROUGEMetric:
    """Test ROUGE metric implementation."""
    
    def test_rouge_metric_creation(self):
        """Test basic ROUGE metric creation."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        assert rouge.name == "rouge"
        assert rouge.variant == "rouge_1"
        assert "recall" in rouge.description.lower()
    
    def test_rouge_1_perfect_match(self):
        """Test ROUGE-1 with perfect match."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        result = rouge.compute(predictions, references)
        
        assert result.score == 1.0  # Perfect match
        assert "precision" in result.details
        assert "recall" in result.details
        assert "f1" in result.details
    
    def test_rouge_1_partial_match(self):
        """Test ROUGE-1 with partial match."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        predictions = ["the cat sat"]
        references = ["the cat sat on the mat"]
        
        result = rouge.compute(predictions, references)
        
        assert 0.0 < result.score < 1.0
        # Should have high recall (all prediction words in reference)
        assert result.details["recall"] > 0.5
    
    def test_rouge_2_bigrams(self):
        """Test ROUGE-2 with bigram matching."""
        rouge = ROUGEMetric(variant="rouge_2")
        
        predictions = ["the cat sat on"]
        references = ["the cat sat on the mat"]
        
        result = rouge.compute(predictions, references)
        
        assert 0.0 <= result.score <= 1.0
        assert "bigram" in str(result.details).lower() or "2-gram" in str(result.details).lower()
    
    def test_rouge_l_lcs(self):
        """Test ROUGE-L with longest common subsequence."""
        rouge = ROUGEMetric(variant="rouge_l")
        
        predictions = ["cat sat mat"]  # Missing some words but preserves order
        references = ["the cat sat on the mat"]
        
        result = rouge.compute(predictions, references)
        
        assert 0.0 <= result.score <= 1.0
        assert result.score > 0.0  # Should find common subsequence
    
    def test_rouge_multiple_predictions(self):
        """Test ROUGE with multiple predictions."""
        rouge = ROUGEMetric(variant="rouge_1")
        
        predictions = [
            "the cat sat",
            "a dog ran",
            "birds fly"
        ]
        references = [
            "the cat sat on the mat",
            "the dog ran in the park",
            "birds fly in the sky"
        ]
        
        result = rouge.compute(predictions, references)
        
        assert 0.0 <= result.score <= 1.0
        assert result.score > 0.3  # Should have reasonable overlap


class TestAccuracyMetric:
    """Test Accuracy metric implementation."""
    
    def test_accuracy_metric_creation(self):
        """Test basic accuracy metric creation."""
        accuracy = AccuracyMetric(case_sensitive=True)
        
        assert accuracy.name == "accuracy"
        assert accuracy.case_sensitive is True
        assert "exact match" in accuracy.description.lower()
    
    def test_accuracy_perfect_match(self):
        """Test accuracy with perfect matches."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B", "C", "D"]
        references = ["A", "B", "C", "D"]
        
        result = accuracy.compute(predictions, references)
        
        assert result.score == 1.0  # 100% accuracy
        assert result.details["correct"] == 4
        assert result.details["total"] == 4
    
    def test_accuracy_partial_match(self):
        """Test accuracy with partial matches."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B", "C", "D"]
        references = ["A", "B", "X", "D"]
        
        result = accuracy.compute(predictions, references)
        
        assert result.score == 0.75  # 3/4 correct
        assert result.details["correct"] == 3
        assert result.details["total"] == 4
    
    def test_accuracy_no_match(self):
        """Test accuracy with no matches."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B", "C"]
        references = ["X", "Y", "Z"]
        
        result = accuracy.compute(predictions, references)
        
        assert result.score == 0.0  # 0% accuracy
        assert result.details["correct"] == 0
        assert result.details["total"] == 3
    
    def test_accuracy_case_sensitivity(self):
        """Test accuracy case sensitivity."""
        accuracy_sensitive = AccuracyMetric(case_sensitive=True)
        accuracy_insensitive = AccuracyMetric(case_sensitive=False)
        
        predictions = ["A", "b", "C"]
        references = ["a", "B", "c"]
        
        result_sensitive = accuracy_sensitive.compute(predictions, references)
        result_insensitive = accuracy_insensitive.compute(predictions, references)
        
        assert result_sensitive.score == 0.0  # No matches due to case
        assert result_insensitive.score == 1.0  # All matches ignoring case
    
    def test_accuracy_mismatched_lengths(self):
        """Test accuracy with mismatched prediction/reference lengths."""
        accuracy = AccuracyMetric()
        
        predictions = ["A", "B"]
        references = ["A", "B", "C"]
        
        with pytest.raises(EvaluationError):
            accuracy.compute(predictions, references)


class TestBERTScoreMetric:
    """Test BERTScore metric implementation."""
    
    def test_bert_score_creation(self):
        """Test basic BERTScore metric creation."""
        bert_score = BERTScoreMetric()
        
        assert bert_score.name == "bert_score"
        assert "contextual embeddings" in bert_score.description.lower()
    
    def test_bert_score_computation(self):
        """Test BERTScore computation (simplified version)."""
        bert_score = BERTScoreMetric()
        
        predictions = ["the cat is sleeping"]
        references = ["a cat is resting"]
        
        result = bert_score.compute(predictions, references)
        
        # This is a simplified implementation, so we just check basic structure
        assert isinstance(result, EvaluationResult)
        assert result.metric_name == "bert_score"
        assert 0.0 <= result.score <= 1.0
        assert "note" in result.details  # Should mention it's simplified
    
    def test_bert_score_identical_texts(self):
        """Test BERTScore with identical texts."""
        bert_score = BERTScoreMetric()
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        result = bert_score.compute(predictions, references)
        
        # Identical texts should have high similarity
        assert result.score > 0.8


class TestMetricIntegration:
    """Test integration scenarios for metrics."""
    
    def test_metric_result_structure(self):
        """Test that all metrics return properly structured results."""
        metrics = [
            BLEUMetric(),
            ROUGEMetric(variant="rouge_1"),
            AccuracyMetric(),
            BERTScoreMetric()
        ]
        
        predictions = ["the cat sat on the mat"]
        references = ["the cat sat on the mat"]
        
        for metric in metrics:
            result = metric.compute(predictions, references)
            
            # Verify result structure
            assert isinstance(result, EvaluationResult)
            assert result.metric_name == metric.name
            assert isinstance(result.score, (int, float))
            assert 0.0 <= result.score <= 1.0
            assert isinstance(result.details, dict)
            assert isinstance(result.timestamp, datetime)
    
    def test_metric_error_handling(self):
        """Test metric error handling."""
        accuracy = AccuracyMetric()
        
        # Test with mismatched lengths
        with pytest.raises(EvaluationError) as exc_info:
            accuracy.compute(["A"], ["A", "B"])
        
        assert "length mismatch" in str(exc_info.value).lower()
        assert exc_info.value.metric_name == "accuracy"
    
    def test_metric_empty_inputs(self):
        """Test metrics with empty inputs."""
        bleu = BLEUMetric()
        
        predictions = []
        references = []
        
        with pytest.raises(EvaluationError):
            bleu.compute(predictions, references)
    
    @pytest.mark.parametrize("metric_class,kwargs", [
        (BLEUMetric, {"max_order": 2}),
        (ROUGEMetric, {"variant": "rouge_1"}),
        (AccuracyMetric, {"case_sensitive": False}),
        (BERTScoreMetric, {})
    ])
    def test_metric_consistency(self, metric_class, kwargs):
        """Test that metrics produce consistent results."""
        metric = metric_class(**kwargs)
        
        predictions = ["the quick brown fox"]
        references = ["the quick brown fox"]
        
        # Run multiple times
        results = []
        for _ in range(3):
            result = metric.compute(predictions, references)
            results.append(result.score)
        
        # Results should be consistent
        assert all(score == results[0] for score in results)
    
    def test_metric_performance(self):
        """Test metric performance with larger inputs."""
        bleu = BLEUMetric()
        
        # Create larger test data
        predictions = ["This is a test sentence. " * 100] * 10
        references = ["This is a test sentence. " * 100] * 10
        
        result = bleu.compute(predictions, references)
        
        # Should complete without issues
        assert isinstance(result, EvaluationResult)
        assert result.score > 0.9  # Should be high similarity


@pytest.mark.asyncio
async def test_async_metric_computation():
    """Test asynchronous metric computation."""
    from core.evaluation.base import AsyncMetric
    
    class TestAsyncMetric(AsyncMetric):
        def __init__(self):
            super().__init__("test_async", "Test async metric")
        
        async def compute_async(self, predictions, references=None, **kwargs):
            # Simulate async computation
            import asyncio
            await asyncio.sleep(0.01)
            
            return EvaluationResult(
                metric_name=self.name,
                score=0.5,
                details={"async": True},
                timestamp=datetime.now()
            )
    
    metric = TestAsyncMetric()
    
    predictions = ["test"]
    references = ["test"]
    
    # Test async computation
    result = await metric.compute_async(predictions, references)
    assert result.score == 0.5
    assert result.details["async"] is True
    
    # Test sync wrapper
    result_sync = metric.compute(predictions, references)
    assert result_sync.score == 0.5
