import json
import os
from pathlib import Path

import pytest


def test_benchmarks_meet_thresholds():
    bench_path = Path(os.environ.get("MGAIF_BENCH_OUT", ".benchmarks.json"))
    thresholds_path = Path(os.environ.get("MGAIF_BENCH_THRESHOLDS", "benchmarks_thresholds.json"))

    if not bench_path.exists() or not thresholds_path.exists():
        pytest.skip("Benchmark output or thresholds file not found")

    data = json.loads(bench_path.read_text(encoding="utf-8"))
    thresholds = json.loads(thresholds_path.read_text(encoding="utf-8"))

    defaults = thresholds.get("defaults", {})
    per_test = thresholds.get("tests", {})

    metrics_by_test = {m.get("test"): m for m in data.get("results", [])}

    failures: list[str] = []
    for test_name, m in metrics_by_test.items():
        th = {**defaults, **per_test.get(test_name, {})}
        if not th:
            # no thresholds defined; skip
            continue
        # Max latency checks
        p95_max = th.get("p95_ms_max")
        p99_max = th.get("p99_ms_max")
        if p95_max is not None and (m.get("p95_ms") is not None) and m["p95_ms"] > p95_max:
            failures.append(f"{test_name}: p95 {m['p95_ms']:.2f}ms > {p95_max}ms")
        if p99_max is not None and (m.get("p99_ms") is not None) and m["p99_ms"] > p99_max:
            failures.append(f"{test_name}: p99 {m['p99_ms']:.2f}ms > {p99_max}ms")
        # Min throughput
        qps_min = th.get("qps_min")
        if qps_min is not None and (m.get("qps") is not None) and m["qps"] < qps_min:
            failures.append(f"{test_name}: qps {m['qps']:.2f} < {qps_min}")

    if failures:
        pytest.fail("Benchmark thresholds violated:\n- " + "\n- ".join(failures))
