import pytest

from core.workflow.schema import WorkflowSpec
from core.workflow.engine import WorkflowEngine
from tests.realistic_data import get_document_corpus


@pytest.mark.asyncio
async def test_workflow_rag_retrieve_and_echo(async_bench):
    # RAG-like flow: set docs -> set query -> retrieve -> echo top ids
    cfg = {
        "name": "rag_flow",
        "nodes": [
            {
                "id": "set_docs",
                "handler": "set",
                "params": {
                    "docs": [
                        {"id": doc["id"], "text": doc["content"]}
                        for doc in get_document_corpus(3)
                    ]
                },
            },
            {"id": "set_query", "handler": "set", "params": {"query": "machine learning applications"}},
            {
                "id": "retrieve",
                "handler": "retrieve",
                "params": {"doc_state_key": "docs", "query_key": "query", "result_key": "hits", "top_k": 2},
            },
            {
                "id": "echo",
                "handler": "echo",
                "params": {"input_key": "hits", "output_key": "echoed_hits"},
            },
            {"id": "end", "handler": "noop"},
        ],
        "edges": [
            {"from": "set_docs", "to": "set_query"},
            {"from": "set_query", "to": "retrieve"},
            {"from": "retrieve", "to": "echo"},
            {"from": "echo", "to": "end"},
        ],
    }

    spec = WorkflowSpec.model_validate(cfg)
    engine = WorkflowEngine(spec)
    state = await engine.run({})
    assert state.get("hits") and isinstance(state["hits"], list)
    # Should find relevant AI/ML documents
    corpus_ids = {doc["id"] for doc in get_document_corpus(3)}
    assert any(h in corpus_ids for h in state["hits"])
    # echoed is string representation of list
    assert "echoed_hits" in state
    assert isinstance(state["echoed_hits"], str)

    metrics = await async_bench(lambda: engine.run({}), iterations=10)
    assert metrics["qps"] > 30
