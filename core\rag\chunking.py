"""Advanced document chunking strategies for RAG."""

from __future__ import annotations

import re
from abc import ABC, abstractmethod
from typing import List, Optional, Tuple

from .base import Document, Chunk, ChunkingError


class ChunkingStrategy(ABC):
    """Abstract base class for document chunking strategies.
    
    Chunking strategies split documents into smaller, manageable pieces
    that can be efficiently processed and retrieved.
    
    Different strategies optimize for different use cases:
    - Fixed size: Consistent chunk sizes for uniform processing
    - Semantic: Preserve meaning by splitting at natural boundaries
    - Recursive: Hierarchical splitting with fallback strategies
    
    Example:
        >>> class CustomChunker(ChunkingStrategy):
        ...     def chunk_document(self, document: Document) -> List[Chunk]:
        ...         # Custom chunking logic
        ...         return chunks
    """
    
    def __init__(self, chunk_size: int = 512, overlap: int = 50):
        """Initialize chunking strategy.
        
        Args:
            chunk_size: Target size for chunks (in characters)
            overlap: Number of characters to overlap between chunks
        """
        self.chunk_size = chunk_size
        self.overlap = overlap
        
        if overlap >= chunk_size:
            raise ValueError("Overlap must be less than chunk_size")
    
    @abstractmethod
    def chunk_document(self, document: Document) -> List[Chunk]:
        """Split document into chunks.
        
        Args:
            document: Document to chunk
            
        Returns:
            List of chunks extracted from document
            
        Raises:
            ChunkingError: If chunking fails
        """
        pass
    
    def _create_chunk(self, content: str, document: Document, 
                     chunk_index: int, start_char: int, end_char: int) -> Chunk:
        """Create a chunk with proper metadata.
        
        Args:
            content: Chunk content
            document: Source document
            chunk_index: Index of chunk in document
            start_char: Starting character position
            end_char: Ending character position
            
        Returns:
            Chunk object with metadata
        """
        return Chunk(
            content=content.strip(),
            document_id=document.id,
            chunk_index=chunk_index,
            start_char=start_char,
            end_char=end_char,
            metadata={
                "document_title": document.title,
                "document_type": document.doc_type.value,
                "chunk_method": self.__class__.__name__,
                "chunk_size": self.chunk_size,
                "overlap": self.overlap,
                **document.metadata
            }
        )


class FixedSizeChunker(ChunkingStrategy):
    """Fixed-size chunking with character overlap.
    
    Splits documents into chunks of approximately equal size,
    with configurable overlap between adjacent chunks.
    
    Features:
    - Consistent chunk sizes
    - Configurable overlap
    - Word boundary preservation
    - Simple and fast processing
    
    Example:
        >>> chunker = FixedSizeChunker(chunk_size=512, overlap=50)
        >>> chunks = chunker.chunk_document(document)
    """
    
    def chunk_document(self, document: Document) -> List[Chunk]:
        """Split document into fixed-size chunks."""
        if not document.content.strip():
            return []
        
        content = document.content
        chunks = []
        start = 0
        chunk_index = 0
        
        while start < len(content):
            # Calculate end position
            end = min(start + self.chunk_size, len(content))
            
            # Try to break at word boundary if not at end of document
            if end < len(content):
                # Look for last space within reasonable distance
                last_space = content.rfind(' ', start, end)
                if last_space > start + self.chunk_size * 0.8:  # At least 80% of target size
                    end = last_space
            
            # Extract chunk content
            chunk_content = content[start:end]
            
            if chunk_content.strip():  # Only create non-empty chunks
                chunk = self._create_chunk(
                    chunk_content, document, chunk_index, start, end
                )
                chunks.append(chunk)
                chunk_index += 1
            
            # Move start position with overlap
            start = max(start + 1, end - self.overlap)
        
        return chunks


class SemanticChunker(ChunkingStrategy):
    """Semantic chunking that preserves meaning boundaries.
    
    Splits documents at natural semantic boundaries like sentences,
    paragraphs, or sections while respecting size constraints.
    
    Features:
    - Sentence boundary detection
    - Paragraph preservation
    - Section header recognition
    - Meaning-preserving splits
    
    Example:
        >>> chunker = SemanticChunker(chunk_size=512, min_chunk_size=100)
        >>> chunks = chunker.chunk_document(document)
    """
    
    def __init__(self, chunk_size: int = 512, overlap: int = 50, 
                 min_chunk_size: int = 100):
        """Initialize semantic chunker.
        
        Args:
            chunk_size: Target chunk size
            overlap: Overlap between chunks
            min_chunk_size: Minimum acceptable chunk size
        """
        super().__init__(chunk_size, overlap)
        self.min_chunk_size = min_chunk_size
        
        # Sentence boundary patterns
        self.sentence_endings = re.compile(r'[.!?]+\s+')
        self.paragraph_breaks = re.compile(r'\n\s*\n')
        self.section_headers = re.compile(r'\n#+\s+.*\n|^\s*\d+\.\s+.*\n', re.MULTILINE)
    
    def chunk_document(self, document: Document) -> List[Chunk]:
        """Split document using semantic boundaries."""
        if not document.content.strip():
            return []
        
        content = document.content
        
        # First, try to split by paragraphs
        paragraphs = self.paragraph_breaks.split(content)
        
        chunks = []
        current_chunk = ""
        chunk_index = 0
        start_pos = 0
        
        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue
            
            # If adding this paragraph would exceed chunk size
            if len(current_chunk) + len(paragraph) > self.chunk_size and current_chunk:
                # Create chunk from current content
                if len(current_chunk) >= self.min_chunk_size:
                    chunk = self._create_chunk(
                        current_chunk, document, chunk_index, 
                        start_pos, start_pos + len(current_chunk)
                    )
                    chunks.append(chunk)
                    chunk_index += 1
                
                # Start new chunk with overlap
                if self.overlap > 0 and current_chunk:
                    overlap_text = current_chunk[-self.overlap:]
                    current_chunk = overlap_text + "\n\n" + paragraph
                    start_pos = start_pos + len(current_chunk) - len(overlap_text) - len(paragraph) - 2
                else:
                    current_chunk = paragraph
                    start_pos = content.find(paragraph, start_pos)
            else:
                # Add paragraph to current chunk
                if current_chunk:
                    current_chunk += "\n\n" + paragraph
                else:
                    current_chunk = paragraph
                    start_pos = content.find(paragraph, start_pos)
        
        # Add final chunk
        if current_chunk and len(current_chunk) >= self.min_chunk_size:
            chunk = self._create_chunk(
                current_chunk, document, chunk_index,
                start_pos, start_pos + len(current_chunk)
            )
            chunks.append(chunk)
        
        # If no chunks created (document too small), create single chunk
        if not chunks and content.strip():
            chunk = self._create_chunk(content, document, 0, 0, len(content))
            chunks.append(chunk)
        
        return chunks


class RecursiveChunker(ChunkingStrategy):
    """Recursive chunking with multiple fallback strategies.
    
    Attempts to split documents using a hierarchy of separators,
    falling back to simpler methods if semantic splitting fails.
    
    Splitting hierarchy:
    1. Section headers (##, ###, etc.)
    2. Paragraph breaks (\n\n)
    3. Sentence endings (.!?)
    4. Line breaks (\n)
    5. Word boundaries (spaces)
    6. Character-level splitting
    
    Example:
        >>> chunker = RecursiveChunker(
        ...     chunk_size=512,
        ...     separators=["\n\n", ". ", "\n", " "]
        ... )
        >>> chunks = chunker.chunk_document(document)
    """
    
    def __init__(self, chunk_size: int = 512, overlap: int = 50,
                 separators: Optional[List[str]] = None):
        """Initialize recursive chunker.
        
        Args:
            chunk_size: Target chunk size
            overlap: Overlap between chunks
            separators: List of separators in order of preference
        """
        super().__init__(chunk_size, overlap)
        
        if separators is None:
            self.separators = [
                "\n\n\n",  # Section breaks
                "\n\n",    # Paragraph breaks
                ". ",      # Sentence endings
                "! ",      # Exclamation sentences
                "? ",      # Question sentences
                "\n",      # Line breaks
                " ",       # Word boundaries
            ]
        else:
            self.separators = separators
    
    def chunk_document(self, document: Document) -> List[Chunk]:
        """Split document using recursive strategy."""
        if not document.content.strip():
            return []
        
        # Start recursive splitting
        chunks = self._recursive_split(document.content, document)
        
        # Add chunk indices and metadata
        for i, chunk in enumerate(chunks):
            chunk.chunk_index = i
        
        return chunks
    
    def _recursive_split(self, text: str, document: Document, 
                        separator_index: int = 0) -> List[Chunk]:
        """Recursively split text using separators."""
        if len(text) <= self.chunk_size:
            # Text is small enough, create single chunk
            return [self._create_chunk(text, document, 0, 0, len(text))]
        
        if separator_index >= len(self.separators):
            # No more separators, force split at chunk_size
            return self._force_split(text, document)
        
        separator = self.separators[separator_index]
        parts = text.split(separator)
        
        if len(parts) == 1:
            # Separator not found, try next separator
            return self._recursive_split(text, document, separator_index + 1)
        
        # Combine parts into chunks
        chunks = []
        current_chunk = ""
        start_pos = 0
        
        for i, part in enumerate(parts):
            # Calculate what the chunk would be with this part
            test_chunk = current_chunk + separator + part if current_chunk else part
            
            if len(test_chunk) <= self.chunk_size:
                # Part fits, add it
                current_chunk = test_chunk
            else:
                # Part doesn't fit
                if current_chunk:
                    # Save current chunk
                    chunk_chunks = self._recursive_split(current_chunk, document, separator_index + 1)
                    chunks.extend(chunk_chunks)
                
                # Start new chunk with this part
                current_chunk = part
        
        # Handle remaining chunk
        if current_chunk:
            chunk_chunks = self._recursive_split(current_chunk, document, separator_index + 1)
            chunks.extend(chunk_chunks)
        
        return chunks
    
    def _force_split(self, text: str, document: Document) -> List[Chunk]:
        """Force split text at character boundaries."""
        chunks = []
        start = 0
        
        while start < len(text):
            end = min(start + self.chunk_size, len(text))
            chunk_text = text[start:end]
            
            chunk = self._create_chunk(chunk_text, document, 0, start, end)
            chunks.append(chunk)
            
            start = end - self.overlap if end < len(text) else end
        
        return chunks
