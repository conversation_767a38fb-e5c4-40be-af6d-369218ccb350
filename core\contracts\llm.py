"""Large Language Model (LLM) contracts and data structures.

This module defines the standardized interfaces and data models for LLM interactions
within the M-GAIF framework. It provides OpenAI-compatible contracts for chat
completions, both streaming and non-streaming, enabling seamless integration with
various LLM providers.

The contracts support:
- Chat-based interactions with role-based messages
- Streaming and non-streaming responses
- Token usage tracking
- Extensible metadata and parameters
- OpenAI API compatibility

Example:
    >>> from core.contracts.llm import ChatCompletionRequest, ChatMessage
    >>> request = ChatCompletionRequest(
    ...     messages=[ChatMessage(role="user", content="Hello!")],
    ...     temperature=0.7
    ... )
    >>> # Use with any LLM adapter
    >>> response = await adapter.chat(request)

Note:
    All data models use Pydantic for validation and serialization, ensuring
    type safety and automatic documentation generation.
"""

from __future__ import annotations

from typing import Any, Dict, List, Literal, Optional
from pydantic import BaseModel, Field


ChatRole = Literal["system", "user", "assistant", "tool"]


class ChatMessage(BaseModel):
    """Unified chat message type for requests and non-stream responses.

    Represents a single message in a chat conversation with role-based content.
    Compatible with OpenAI's chat completion format.

    Args:
        role: The role of the message sender (system, user, assistant, tool)
        content: The text content of the message
        name: Optional name identifier for the message sender
        metadata: Additional metadata for the message

    Example:
        >>> message = ChatMessage(role="user", content="Hello, AI!")
        >>> print(message.role)  # "user"
        >>> print(message.content)  # "Hello, AI!"
    """

    role: ChatRole
    content: str
    name: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)


class ChatCompletionRequest(BaseModel):
    """Request contract for chat completion APIs.

    Defines the structure for chat completion requests, compatible with OpenAI's
    chat completions API. Supports both streaming and non-streaming modes.

    Args:
        messages: List of chat messages forming the conversation context
        model: Optional model identifier (e.g., "gpt-4", "llama3")
        temperature: Sampling temperature (0.0 = deterministic, 1.0 = creative)
        max_tokens: Maximum number of tokens to generate
        stream: Whether to stream the response as chunks
        extras: Additional provider-specific parameters

    Example:
        >>> request = ChatCompletionRequest(
        ...     messages=[
        ...         ChatMessage(role="system", content="You are a helpful assistant."),
        ...         ChatMessage(role="user", content="What is AI?")
        ...     ],
        ...     temperature=0.7,
        ...     max_tokens=150
        ... )
    """

    messages: List[ChatMessage]
    model: Optional[str] = None
    temperature: float = 0.0
    max_tokens: Optional[int] = None
    stream: bool = False
    # room for optional tool / rag fields later without breaking contract
    extras: Dict[str, Any] = Field(default_factory=dict)


class Usage(BaseModel):
    """Token usage statistics for chat completions.

    Tracks the number of tokens consumed during a chat completion request,
    providing detailed breakdown for billing and monitoring purposes.

    Args:
        prompt_tokens: Number of tokens in the input prompt
        completion_tokens: Number of tokens in the generated completion
        total_tokens: Total tokens used (prompt + completion)

    Example:
        >>> usage = Usage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        >>> print(f"Cost estimation: {usage.total_tokens * 0.001} credits")
    """
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class ChatChoice(BaseModel):
    """A single choice in a chat completion response.

    Represents one possible completion generated by the language model,
    including the message content and metadata about completion status.

    Args:
        index: The index of this choice in the response
        message: The generated message content
        finish_reason: Reason why generation stopped ("stop", "length", "content_filter", etc.)

    Example:
        >>> choice = ChatChoice(
        ...     index=0,
        ...     message=ChatMessage(role="assistant", content="Hello!"),
        ...     finish_reason="stop"
        ... )
    """
    index: int
    message: ChatMessage
    finish_reason: Optional[str] = None


class ChatCompletionResponse(BaseModel):
    """Response from a chat completion request.

    Contains the generated completions, metadata, and usage statistics
    from a language model chat completion request.

    Args:
        id: Unique identifier for this completion
        created: Unix timestamp when the completion was created
        model: Model used for the completion
        choices: List of generated completion choices
        usage: Token usage statistics

    Example:
        >>> response = ChatCompletionResponse(
        ...     id="chatcmpl-123",
        ...     created=1677652288,
        ...     model="gpt-3.5-turbo",
        ...     choices=[choice],
        ...     usage=Usage(prompt_tokens=10, completion_tokens=5, total_tokens=15)
        ... )
    """
    id: str
    created: int
    model: Optional[str] = None
    choices: List[ChatChoice]
    usage: Usage = Field(default_factory=Usage)


# Streaming chunk types
class DeltaMessage(BaseModel):
    role: Optional[ChatRole] = None
    content: Optional[str] = None


class ChatChunkChoice(BaseModel):
    index: int
    delta: DeltaMessage
    finish_reason: Optional[str] = None


class ChatCompletionChunk(BaseModel):
    id: str
    created: int
    model: Optional[str] = None
    choices: List[ChatChunkChoice]

# Contracts to be implemented in Phase 2
