"""Tests for evaluation framework orchestration.

This module tests the Evaluator, benchmarks, cost tracking, and
performance profiling components of the evaluation framework.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, AsyncMock

from core.evaluation.base import Metric, EvaluationResult, EvaluationError
from core.evaluation.metrics import BLEUMetric, AccuracyMetric

# TODO: Import these when implemented
# from core.evaluation.evaluator import Evaluator, EvaluationConfig
# from core.evaluation.cost_tracker import CostTracker, CostReport
# from core.evaluation.profiler import PerformanceProfiler, PerformanceReport


class MockMetric(Metric):
    """Mock metric for testing."""
    
    def __init__(self, name="mock_metric", score=0.8):
        super().__init__(name, f"Mock metric: {name}")
        self._score = score
    
    def compute(self, predictions, references=None, **kwargs):
        return EvaluationResult(
            metric_name=self.name,
            score=self._score,
            details={"mock": True, "predictions_count": len(predictions)},
            timestamp=datetime.now()
        )


# TODO: Implement these tests when Evaluator is available
# class TestEvaluator:
#     """Test Evaluator orchestration."""
#
#     def test_evaluator_creation(self):
#         """Test basic evaluator creation."""
#         evaluator = Evaluator()
#
#         assert len(evaluator.metrics) == 0
#         assert evaluator.config is not None
#         assert isinstance(evaluator.config, EvaluationConfig)
    
    def test_evaluator_with_config(self):
        """Test evaluator creation with custom config."""
        config = EvaluationConfig(
            parallel_execution=True,
            max_workers=4,
            timeout_seconds=30
        )
        evaluator = Evaluator(config=config)
        
        assert evaluator.config.parallel_execution is True
        assert evaluator.config.max_workers == 4
        assert evaluator.config.timeout_seconds == 30
    
    def test_add_metric(self):
        """Test adding metrics to evaluator."""
        evaluator = Evaluator()
        metric1 = MockMetric("metric1", 0.8)
        metric2 = MockMetric("metric2", 0.9)
        
        evaluator.add_metric(metric1)
        evaluator.add_metric(metric2)
        
        assert len(evaluator.metrics) == 2
        assert "metric1" in evaluator.metrics
        assert "metric2" in evaluator.metrics
    
    def test_remove_metric(self):
        """Test removing metrics from evaluator."""
        evaluator = Evaluator()
        metric = MockMetric("test_metric")
        
        evaluator.add_metric(metric)
        assert len(evaluator.metrics) == 1
        
        evaluator.remove_metric("test_metric")
        assert len(evaluator.metrics) == 0
    
    def test_evaluate_single_metric(self):
        """Test evaluation with single metric."""
        evaluator = Evaluator()
        metric = MockMetric("test_metric", 0.85)
        evaluator.add_metric(metric)
        
        predictions = ["prediction 1", "prediction 2"]
        references = ["reference 1", "reference 2"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert len(results) == 1
        assert "test_metric" in results
        assert results["test_metric"].score == 0.85
        assert results["test_metric"].details["predictions_count"] == 2
    
    def test_evaluate_multiple_metrics(self):
        """Test evaluation with multiple metrics."""
        evaluator = Evaluator()
        metric1 = MockMetric("metric1", 0.8)
        metric2 = MockMetric("metric2", 0.9)
        
        evaluator.add_metric(metric1)
        evaluator.add_metric(metric2)
        
        predictions = ["test prediction"]
        references = ["test reference"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert len(results) == 2
        assert "metric1" in results
        assert "metric2" in results
        assert results["metric1"].score == 0.8
        assert results["metric2"].score == 0.9
    
    def test_evaluate_with_real_metrics(self):
        """Test evaluation with real metrics."""
        evaluator = Evaluator()
        evaluator.add_metric(BLEUMetric())
        evaluator.add_metric(AccuracyMetric())
        
        predictions = ["the cat sat on the mat", "A", "B"]
        references = ["the cat sat on the mat", "A", "C"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert "bleu" in results
        assert "accuracy" in results
        assert 0.0 <= results["bleu"].score <= 1.0
        assert 0.0 <= results["accuracy"].score <= 1.0
    
    def test_evaluate_empty_metrics(self):
        """Test evaluation with no metrics."""
        evaluator = Evaluator()
        
        predictions = ["test"]
        references = ["test"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert len(results) == 0
    
    def test_evaluate_metric_error_handling(self):
        """Test evaluation with metric errors."""
        class ErrorMetric(Metric):
            def __init__(self):
                super().__init__("error_metric", "Metric that always errors")
            
            def compute(self, predictions, references=None, **kwargs):
                raise EvaluationError("Test error", metric_name=self.name)
        
        evaluator = Evaluator()
        evaluator.add_metric(ErrorMetric())
        evaluator.add_metric(MockMetric("good_metric", 0.8))
        
        predictions = ["test"]
        references = ["test"]
        
        results = evaluator.evaluate(predictions, references)
        
        # Should continue with other metrics despite error
        assert "good_metric" in results
        assert results["good_metric"].score == 0.8
        # Error metric should not be in results or should have error info


class TestCostTracker:
    """Test cost tracking functionality."""
    
    def test_cost_tracker_creation(self):
        """Test basic cost tracker creation."""
        tracker = CostTracker()
        
        assert tracker.total_cost == 0.0
        assert len(tracker.cost_entries) == 0
    
    def test_track_llm_cost(self):
        """Test tracking LLM costs."""
        tracker = CostTracker()
        
        tracker.track_llm_cost(
            model="gpt-4",
            input_tokens=1000,
            output_tokens=500,
            cost_per_input_token=0.00003,
            cost_per_output_token=0.00006
        )
        
        assert tracker.total_cost == 0.06  # (1000 * 0.00003) + (500 * 0.00006)
        assert len(tracker.cost_entries) == 1
        
        entry = tracker.cost_entries[0]
        assert entry["component"] == "llm"
        assert entry["model"] == "gpt-4"
        assert entry["cost"] == 0.06
    
    def test_track_embedding_cost(self):
        """Test tracking embedding costs."""
        tracker = CostTracker()
        
        tracker.track_embedding_cost(
            model="text-embedding-ada-002",
            tokens=2000,
            cost_per_token=0.0000001
        )
        
        assert tracker.total_cost == 0.0002
        assert len(tracker.cost_entries) == 1
        
        entry = tracker.cost_entries[0]
        assert entry["component"] == "embedding"
        assert entry["model"] == "text-embedding-ada-002"
        assert entry["cost"] == 0.0002
    
    def test_track_custom_cost(self):
        """Test tracking custom costs."""
        tracker = CostTracker()
        
        tracker.track_cost(
            component="custom_service",
            cost=5.0,
            details={"service": "external_api", "requests": 100}
        )
        
        assert tracker.total_cost == 5.0
        assert len(tracker.cost_entries) == 1
        
        entry = tracker.cost_entries[0]
        assert entry["component"] == "custom_service"
        assert entry["cost"] == 5.0
        assert entry["details"]["service"] == "external_api"
    
    def test_multiple_cost_tracking(self):
        """Test tracking multiple costs."""
        tracker = CostTracker()
        
        tracker.track_llm_cost("gpt-3.5-turbo", 1000, 500, 0.000002, 0.000002)
        tracker.track_embedding_cost("text-embedding-ada-002", 1000, 0.0000001)
        tracker.track_cost("storage", 0.5)
        
        expected_total = (1000 * 0.000002) + (500 * 0.000002) + (1000 * 0.0000001) + 0.5
        assert abs(tracker.total_cost - expected_total) < 0.0001
        assert len(tracker.cost_entries) == 3
    
    def test_cost_report_generation(self):
        """Test cost report generation."""
        tracker = CostTracker()
        
        tracker.track_llm_cost("gpt-4", 1000, 500, 0.00003, 0.00006)
        tracker.track_embedding_cost("ada-002", 2000, 0.0000001)
        
        report = tracker.generate_report()
        
        assert isinstance(report, CostReport)
        assert report.total_cost == tracker.total_cost
        assert len(report.cost_breakdown) == 2
        assert "llm" in report.cost_by_component
        assert "embedding" in report.cost_by_component
    
    def test_cost_reset(self):
        """Test resetting cost tracker."""
        tracker = CostTracker()
        
        tracker.track_cost("test", 10.0)
        assert tracker.total_cost == 10.0
        
        tracker.reset()
        assert tracker.total_cost == 0.0
        assert len(tracker.cost_entries) == 0


class TestPerformanceProfiler:
    """Test performance profiling functionality."""
    
    def test_profiler_creation(self):
        """Test basic profiler creation."""
        profiler = PerformanceProfiler()
        
        assert len(profiler.measurements) == 0
    
    def test_context_manager_timing(self):
        """Test profiler as context manager."""
        profiler = PerformanceProfiler()
        
        with profiler.measure("test_operation"):
            # Simulate some work
            import time
            time.sleep(0.01)
        
        assert len(profiler.measurements) == 1
        measurement = profiler.measurements[0]
        assert measurement["operation"] == "test_operation"
        assert measurement["duration"] >= 0.01
        assert "start_time" in measurement
        assert "end_time" in measurement
    
    def test_manual_timing(self):
        """Test manual start/stop timing."""
        profiler = PerformanceProfiler()
        
        profiler.start_measurement("manual_test")
        import time
        time.sleep(0.005)
        profiler.end_measurement("manual_test")
        
        assert len(profiler.measurements) == 1
        measurement = profiler.measurements[0]
        assert measurement["operation"] == "manual_test"
        assert measurement["duration"] >= 0.005
    
    def test_multiple_measurements(self):
        """Test multiple performance measurements."""
        profiler = PerformanceProfiler()
        
        with profiler.measure("operation1"):
            import time
            time.sleep(0.01)
        
        with profiler.measure("operation2"):
            import time
            time.sleep(0.005)
        
        assert len(profiler.measurements) == 2
        assert profiler.measurements[0]["operation"] == "operation1"
        assert profiler.measurements[1]["operation"] == "operation2"
    
    def test_performance_report_generation(self):
        """Test performance report generation."""
        profiler = PerformanceProfiler()
        
        # Add some measurements
        with profiler.measure("fast_op"):
            import time
            time.sleep(0.001)
        
        with profiler.measure("slow_op"):
            import time
            time.sleep(0.01)
        
        report = profiler.generate_report()
        
        assert isinstance(report, PerformanceReport)
        assert report.total_operations == 2
        assert report.total_time >= 0.011
        assert len(report.operation_stats) == 2
        assert "fast_op" in report.operation_stats
        assert "slow_op" in report.operation_stats
    
    def test_profiler_statistics(self):
        """Test profiler statistics calculation."""
        profiler = PerformanceProfiler()
        
        # Add multiple measurements of same operation
        for _ in range(5):
            with profiler.measure("repeated_op"):
                import time
                time.sleep(0.001)
        
        report = profiler.generate_report()
        stats = report.operation_stats["repeated_op"]
        
        assert stats["count"] == 5
        assert stats["total_time"] >= 0.005
        assert stats["avg_time"] >= 0.001
        assert "min_time" in stats
        assert "max_time" in stats


class TestEvaluationIntegration:
    """Test integration of evaluation components."""
    
    def test_evaluator_with_cost_tracking(self):
        """Test evaluator with cost tracking enabled."""
        config = EvaluationConfig(track_costs=True)
        evaluator = Evaluator(config=config)
        evaluator.add_metric(MockMetric("test_metric"))
        
        predictions = ["test"]
        references = ["test"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert "test_metric" in results
        # Cost tracking should be available
        assert hasattr(evaluator, 'cost_tracker')
    
    def test_evaluator_with_performance_profiling(self):
        """Test evaluator with performance profiling enabled."""
        config = EvaluationConfig(profile_performance=True)
        evaluator = Evaluator(config=config)
        evaluator.add_metric(MockMetric("test_metric"))
        
        predictions = ["test"]
        references = ["test"]
        
        results = evaluator.evaluate(predictions, references)
        
        assert "test_metric" in results
        # Performance profiling should be available
        assert hasattr(evaluator, 'profiler')
    
    @pytest.mark.asyncio
    async def test_async_evaluation(self):
        """Test asynchronous evaluation."""
        from core.evaluation.base import AsyncMetric
        
        class AsyncMockMetric(AsyncMetric):
            def __init__(self, name, score):
                super().__init__(name, f"Async mock: {name}")
                self._score = score
            
            async def compute_async(self, predictions, references=None, **kwargs):
                import asyncio
                await asyncio.sleep(0.001)  # Simulate async work
                return EvaluationResult(
                    metric_name=self.name,
                    score=self._score,
                    details={"async": True},
                    timestamp=datetime.now()
                )
        
        evaluator = Evaluator()
        evaluator.add_metric(AsyncMockMetric("async_metric", 0.9))
        
        predictions = ["test"]
        references = ["test"]
        
        # Test async evaluation
        results = await evaluator.evaluate_async(predictions, references)
        
        assert "async_metric" in results
        assert results["async_metric"].score == 0.9
        assert results["async_metric"].details["async"] is True
    
    def test_evaluation_config_validation(self):
        """Test evaluation configuration validation."""
        # Valid config
        config = EvaluationConfig(
            parallel_execution=True,
            max_workers=2,
            timeout_seconds=60
        )
        assert config.parallel_execution is True
        assert config.max_workers == 2
        
        # Test with invalid values
        with pytest.raises(ValueError):
            EvaluationConfig(max_workers=0)  # Should be positive
        
        with pytest.raises(ValueError):
            EvaluationConfig(timeout_seconds=-1)  # Should be positive
