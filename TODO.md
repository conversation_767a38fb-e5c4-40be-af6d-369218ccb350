# TODO — Junior Developer Implementation Guide (M‑GAIF)

This checklist guides you to implement the Modular Generative AI Framework (M‑GAIF) from the PRD. Follow the phases in order. Each phase lists deliverables, steps, and acceptance criteria.

Key reference: `Modular GenAI Framework PRD.md` (Sections 5–13).

---

## Phase 0 — Workstation Setup (Day 0)

- [ ] Install Python 3.11 (preferred) or 3.10/3.12
- [ ] Install Git, Node.js (optional for UI examples), <PERSON><PERSON> Desktop (for containers)
- [ ] Create a virtual environment
  - Windows PowerShell:
    ```powershell
    py -3.11 -m venv .venv
    .\.venv\Scripts\Activate.ps1
    ```
- [x] Create initial files:
  - [x] `requirements.txt` (pin baseline libs):
    ```text
    fastapi==0.112.0
    uvicorn[standard]==0.30.5
    pydantic==2.8.2
    numpy==1.26.4
    httpx==0.27.0
    tenacity==8.5.0
    opentelemetry-sdk==1.25.0
    opentelemetry-exporter-otlp==1.25.0
    prometheus-client==0.20.0
    pytest==8.3.2
    pytest-asyncio==0.23.7
    pip-audit==2.7.3
    pyyaml==6.0.2
    ```
  - [x] `.gitignore` for Python/venv/build artifacts
  - [x] `README.md` minimal quickstart
- [ ] Install dependencies:
  ```powershell
  pip install -r requirements.txt
  ```

Acceptance Criteria
- [ ] `python -V` shows 3.10–3.12
- [ ] `pip list` shows packages installed without errors

---

## Phase 1 — Repository Scaffold (Day 1)

Create the directories and empty files matching the PRD’s starter scaffold. Keep modules under `core/` for contracts and `plugins/` for implementations.

- [x] Create folders:
  - [x] `core/primitives/`, `core/contracts/`, `core/adapters/`, `core/text/`, `core/rag/`, `core/stores/`, `core/agent/`, `core/workflow/`, `core/edge/`, `core/telemetry/`
  - [x] `plugins/tokenizers/`, `plugins/embedders/`, `plugins/retrievers/`
  - [x] `configs/workflows/`, `tests/`, `.github/workflows/`
- [x] Add `__init__.py` to all Python folders
- [x] Create placeholder files referenced in PRD (empty class stubs if needed):
  - [x] `core/workflow/schema.py`, `core/workflow/engine.py`
  - [x] `core/edge/api.py`
  - [x] `plugins/tokenizers/simple_tokenizer.py`
  - [x] `plugins/embedders/simple_embedder.py`
  - [x] `plugins/retrievers/in_memory_retriever.py`
  - [x] `core/stores/in_memory_vector.py`
  - [x] `core/contracts/llm.py`, `core/contracts/retrieval.py`
  - [x] `run.py`, `DEVELOPER_GUIDE.md`
- [x] Create `configs/workflows/example.yaml` (from PRD example)

Acceptance Criteria
- [ ] `pytest -q` runs (even if 0 tests) without ImportError
- [ ] `python run.py configs/workflows/example.yaml` prints a state dict (placeholder is OK)

---

## Phase 2 — Contracts & Types (Days 2–3)

Implement typed Pydantic models and ABC interfaces per PRD Section 5 contracts.

- [x] `core/contracts/llm.py`: `ChatMessage`, `ChatCompletionRequest`, `ChatCompletionResponse`, streaming chunk types
- [x] `core/contracts/retrieval.py`: `VectorRecord`, `SearchResult`
- [x] `core/text/tokenizer.py`: `Tokenizer` ABC with `encode`, `decode`, `token_count`
- [x] `core/text/embeddings.py`: `Embedder` ABC `embed(texts)`
- [x] `core/adapters/base.py`: `BaseModelAdapter` with `chat`, `chat_stream` signatures and `ModelAdapterError`
- [x] Add docstrings and type hints to all public APIs

Acceptance Criteria
- [ ] `mypy`/`pyright` (optional) report no type errors for contracts
- [ ] Contracts are importable by plugin modules without circular imports

---

## Phase 3 — Minimal Implementations (Days 3–4)

Create minimal, deterministic implementations for development and tests.

- [x] `plugins/tokenizers/simple_tokenizer.py` (whitespace + punctuation split)
- [x] `plugins/embedders/simple_embedder.py` (hash-based 8-dim vectors)
- [x] `core/stores/in_memory_vector.py` (in-memory list + cosine similarity)
- [x] `plugins/retrievers/in_memory_retriever.py` using the in-memory store
- [x] `core/adapters/openai_adapter.py` placeholder (wire later to real API)

Acceptance Criteria
- [ ] Unit tests for each module (see Phase 8 for tests) pass locally
- [ ] `example.yaml` workflow can import and execute handlers

---

## Phase 4 — Workflow Engine & Runner (Days 4–5)

Implement the DAG schema and a simple async executor as per PRD.

- [x] `core/workflow/schema.py`: `WorkflowSpec`, `Node`, `Edge` (Pydantic models)
- [x] `core/workflow/engine.py`: linear/decision flow, async handler resolution
- [x] `run.py`: YAML load → build `WorkflowSpec` → `WorkflowEngine.run()` → print state

Acceptance Criteria
- [ ] Provide a workflow that calls a tool-like handler and then a second step
- [ ] Errors for cycles or missing entry node raise helpful messages

---

## Phase 5 — Edge API (Days 5–6)

Expose a minimal `/v1/chat/completions` endpoint with streaming support.

- [x] `core/edge/api.py`: FastAPI app using `BaseModelAdapter` (start with a stub adapter)
- [x] Implement streaming via `StreamingResponse` and an async generator
- [ ] Local run:
  ```powershell
  uvicorn core.edge.api:app --reload --port 8000
  ```
- [ ] Test with `httpx` or `curl` to confirm JSON and stream modes

Acceptance Criteria
- [ ] OpenAPI docs load at `http://localhost:8000/docs`
- [ ] Non-stream and stream responses both work

---

## Phase 6 — Observability (Days 6–7)

Add structured logging, tracing, and metrics per PRD Section 6.

- [x] Logging: JSON logs with request IDs and workflow IDs
- [x] Tracing: OpenTelemetry spans for RAG → LLM → Agent → Workflow
- [x] Metrics: Prometheus counters/histograms (latency, tokens, requests)
- [x] Provide `OTEL_EXPORTER_OTLP_ENDPOINT` env var support

Acceptance Criteria
- [ ] Traces visible in OTLP-compatible collector (local dev ok)
- [ ] `/metrics` endpoint exposes Prometheus metrics (via middleware or separate app)

---

## Phase 7 — Security & Privacy (Days 7–8)

Implement guardrails and basic compliance hygiene (PRD 6.4–6.5).

- [ ] Input sanitization and prompt-injection checks at API boundary
- [ ] Tool-call allowlist and capability tokens
- [ ] Redact PII before persistence; add configurable TTL for stored artifacts
- [ ] SBOM generation and dependency scan steps (CI)

Acceptance Criteria
- [ ] Malicious prompts are flagged or blocked in tests
- [ ] `pip-audit` step passes with no high/critical issues

---

## Phase 8 — Tests & CI (Days 8–9)

Create modular unit tests and a GitHub Actions CI.

- [ ] Tests:
  - [ ] `tests/test_import_policy.py` (from PRD)
  - [ ] Tokenizer round-trip and special tokens
  - [ ] Embedder determinism and vector shapes
  - [ ] Vector store search top-k shape and ordering (mock)
  - [ ] Workflow engine executes N steps; decision branches
  - [ ] Edge API non-stream/stream routes (pytest-asyncio)
- [ ] GitHub Actions: `.github/workflows/ci.yml`
  ```yaml
  name: CI
  on: [push, pull_request]
  jobs:
    test:
      runs-on: ubuntu-latest
      steps:
        - uses: actions/checkout@v4
        - uses: actions/setup-python@v4
          with:
            python-version: '3.11'
        - name: Install
          run: pip install -r requirements.txt
        - name: Type check (optional)
          run: |
            pip install mypy
            mypy core || true
        - name: Security scan
          run: pip-audit -r requirements.txt || true
        - name: Tests
          run: pytest -q
  ```

Acceptance Criteria
- [ ] CI green on PRs; coverage ≥ 90% for core and plugins

---

## Phase 9 — Packaging & Deployment (Days 9–10)

Prepare containerization and basic deployment options.

- [x] `Dockerfile` (multi-stage, non-root user)
- [x] `docker-compose.yml` for local API + OTEL Collector + Prometheus
- [ ] Basic Helm chart skeleton (optional, stretch)

Acceptance Criteria
- [ ] `docker build .` succeeds; container serves `/docs`
- [ ] Compose stack starts and metrics scrape works

---

## Phase 10 — SLOs & Benchmarks (Day 10)

Measure SLOs defined in PRD Section 6.6.

- [ ] Add simple benchmark script for chat and retrieval
- [ ] Record P50/P95 latency and throughput locally
- [ ] Document results and gaps

Acceptance Criteria
- [ ] Report shows current numbers vs targets; tracked as issues if behind

---

## Phase 11 — Documentation & Examples (Day 11)

- [ ] Fill out `DEVELOPER_GUIDE.md` with plugin authoring steps
- [ ] Expand `README.md` (Quickstart, API, workflows, troubleshooting)
- [ ] Add example notebooks or scripts (optional)

Acceptance Criteria
- [ ] A new developer can run an example workflow in <15 minutes

---

## Phase 12 — Release Readiness (Day 12)

- [ ] Draft CHANGELOG.md with features and breaking changes
- [ ] Tag v0.1.0; signed release artifact (optional)
- [ ] Close all P0/P1 issues

Acceptance Criteria
- [ ] All acceptance criteria from PRD Section 11 satisfied

---

## Environment Variables (sample)

- `OPENAI_API_KEY` (when wiring real adapter)
- `OTEL_EXPORTER_OTLP_ENDPOINT`
- `LOG_LEVEL` (info/debug)

---

## Common Pitfalls & Tips

- Prefer async I/O for any network-bound calls
- Keep `core/` contracts stable; implement variations in `plugins/`
- Add docstrings and type hints early to avoid refactors
- Write a unit test with each module you create
- Use capability flags for backend-specific features to avoid leaks into contracts

---

## Daily Checklists

- [ ] Pull latest changes and rebase your branch
- [ ] Run linters/tests before pushing
- [ ] Update TODO checkboxes in your PR description
- [ ] Keep docs in sync when APIs change
