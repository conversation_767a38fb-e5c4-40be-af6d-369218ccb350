"""Structured logging for M-GAIF.

This module provides structured JSON logging with correlation IDs,
request tracking, and contextual information for all M-GAIF components.
"""

import json
import logging
import os
import sys
import uuid
from contextvars import ContextVar
from datetime import datetime
from typing import Any, Dict, Optional, Union
from pathlib import Path

# Context variables for request tracking
correlation_id_var: ContextVar[Optional[str]] = ContextVar('correlation_id', default=None)
request_id_var: ContextVar[Optional[str]] = ContextVar('request_id', default=None)
user_id_var: ContextVar[Optional[str]] = ContextVar('user_id', default=None)
workflow_id_var: ContextVar[Optional[str]] = ContextVar('workflow_id', default=None)


class StructuredFormatter(logging.Formatter):
    """JSON formatter for structured logging."""
    
    def __init__(self, service_name: str = "mgaif", include_extra: bool = True):
        """Initialize structured formatter.
        
        Args:
            service_name: Name of the service for log identification
            include_extra: Whether to include extra fields from log records
        """
        super().__init__()
        self.service_name = service_name
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """Format log record as structured JSON."""
        # Base log structure
        log_entry = {
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "level": record.levelname,
            "logger": record.name,
            "service": self.service_name,
            "message": record.getMessage(),
        }
        
        # Add context information
        if correlation_id_var.get():
            log_entry["correlation_id"] = correlation_id_var.get()
        if request_id_var.get():
            log_entry["request_id"] = request_id_var.get()
        if user_id_var.get():
            log_entry["user_id"] = user_id_var.get()
        if workflow_id_var.get():
            log_entry["workflow_id"] = workflow_id_var.get()
        
        # Add location information
        log_entry["location"] = {
            "file": record.pathname,
            "line": record.lineno,
            "function": record.funcName,
            "module": record.module
        }
        
        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }
        
        # Add extra fields
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'getMessage', 'exc_info',
                    'exc_text', 'stack_info'
                }:
                    try:
                        # Ensure value is JSON serializable
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False)


class StructuredLogger:
    """Enhanced logger with structured logging capabilities."""
    
    def __init__(self, name: str, service_name: str = "mgaif"):
        """Initialize structured logger.
        
        Args:
            name: Logger name (typically module or component name)
            service_name: Service name for log identification
        """
        self.logger = logging.getLogger(name)
        self.service_name = service_name
    
    def _log_with_context(self, level: int, message: str, **kwargs):
        """Log message with additional context."""
        extra = kwargs.pop('extra', {})
        
        # Add any additional keyword arguments as extra fields
        for key, value in kwargs.items():
            if key not in {'exc_info', 'stack_info'}:
                extra[key] = value
        
        self.logger.log(level, message, extra=extra, **{k: v for k, v in kwargs.items() if k in {'exc_info', 'stack_info'}})
    
    def debug(self, message: str, **kwargs):
        """Log debug message."""
        self._log_with_context(logging.DEBUG, message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """Log info message."""
        self._log_with_context(logging.INFO, message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """Log warning message."""
        self._log_with_context(logging.WARNING, message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """Log error message."""
        self._log_with_context(logging.ERROR, message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """Log critical message."""
        self._log_with_context(logging.CRITICAL, message, **kwargs)
    
    def exception(self, message: str, **kwargs):
        """Log exception with traceback."""
        kwargs['exc_info'] = True
        self._log_with_context(logging.ERROR, message, **kwargs)


def configure_logging(
    level: Union[str, int] = "INFO",
    service_name: str = "mgaif",
    log_file: Optional[Path] = None,
    console_output: bool = True,
    json_format: bool = True
) -> None:
    """Configure structured logging for the application.
    
    Args:
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        service_name: Service name for log identification
        log_file: Optional file path for log output
        console_output: Whether to output logs to console
        json_format: Whether to use JSON formatting
    """
    # Convert string level to logging constant
    if isinstance(level, str):
        level = getattr(logging, level.upper())
    
    # Get root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create formatter
    if json_format:
        formatter = StructuredFormatter(service_name=service_name)
    else:
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
    
    # File handler
    if log_file:
        log_file.parent.mkdir(parents=True, exist_ok=True)
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
    
    # Suppress noisy third-party loggers
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("opentelemetry").setLevel(logging.WARNING)


def get_logger(name: str, service_name: str = "mgaif") -> StructuredLogger:
    """Get a structured logger instance.
    
    Args:
        name: Logger name (typically module or component name)
        service_name: Service name for log identification
    
    Returns:
        StructuredLogger instance
    """
    return StructuredLogger(name, service_name)


def set_correlation_id(correlation_id: str) -> None:
    """Set correlation ID for current context."""
    correlation_id_var.set(correlation_id)


def get_correlation_id() -> Optional[str]:
    """Get correlation ID from current context."""
    return correlation_id_var.get()


def set_request_id(request_id: str) -> None:
    """Set request ID for current context."""
    request_id_var.set(request_id)


def get_request_id() -> Optional[str]:
    """Get request ID from current context."""
    return request_id_var.get()


def set_user_id(user_id: str) -> None:
    """Set user ID for current context."""
    user_id_var.set(user_id)


def get_user_id() -> Optional[str]:
    """Get user ID from current context."""
    return user_id_var.get()


def set_workflow_id(workflow_id: str) -> None:
    """Set workflow ID for current context."""
    workflow_id_var.set(workflow_id)


def get_workflow_id() -> Optional[str]:
    """Get workflow ID from current context."""
    return workflow_id_var.get()


def generate_correlation_id() -> str:
    """Generate a new correlation ID."""
    return str(uuid.uuid4())


def generate_request_id() -> str:
    """Generate a new request ID."""
    return str(uuid.uuid4())


class LoggingContext:
    """Context manager for setting logging context variables."""
    
    def __init__(
        self,
        correlation_id: Optional[str] = None,
        request_id: Optional[str] = None,
        user_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        auto_generate: bool = True
    ):
        """Initialize logging context.
        
        Args:
            correlation_id: Correlation ID for request tracking
            request_id: Request ID for request tracking
            user_id: User ID for user tracking
            workflow_id: Workflow ID for workflow tracking
            auto_generate: Whether to auto-generate missing IDs
        """
        self.correlation_id = correlation_id or (generate_correlation_id() if auto_generate else None)
        self.request_id = request_id or (generate_request_id() if auto_generate else None)
        self.user_id = user_id
        self.workflow_id = workflow_id
        
        # Store previous values for restoration
        self.prev_correlation_id = None
        self.prev_request_id = None
        self.prev_user_id = None
        self.prev_workflow_id = None
    
    def __enter__(self):
        """Enter logging context."""
        # Store previous values
        self.prev_correlation_id = correlation_id_var.get()
        self.prev_request_id = request_id_var.get()
        self.prev_user_id = user_id_var.get()
        self.prev_workflow_id = workflow_id_var.get()
        
        # Set new values
        if self.correlation_id:
            correlation_id_var.set(self.correlation_id)
        if self.request_id:
            request_id_var.set(self.request_id)
        if self.user_id:
            user_id_var.set(self.user_id)
        if self.workflow_id:
            workflow_id_var.set(self.workflow_id)
        
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit logging context."""
        # Restore previous values
        correlation_id_var.set(self.prev_correlation_id)
        request_id_var.set(self.prev_request_id)
        user_id_var.set(self.prev_user_id)
        workflow_id_var.set(self.prev_workflow_id)


# Initialize logging configuration from environment
def _init_from_env():
    """Initialize logging from environment variables."""
    log_level = os.getenv("LOG_LEVEL", "INFO")
    service_name = os.getenv("SERVICE_NAME", "mgaif")
    log_file_path = os.getenv("LOG_FILE")
    json_format = os.getenv("LOG_FORMAT", "json").lower() == "json"
    
    log_file = Path(log_file_path) if log_file_path else None
    
    configure_logging(
        level=log_level,
        service_name=service_name,
        log_file=log_file,
        json_format=json_format
    )


# Auto-initialize if not already configured
if not logging.getLogger().handlers:
    _init_from_env()
