import React from 'react'
import { Layout, Menu, theme } from 'antd'
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom'
import Dashboard from './pages/Dashboard'
import Workflows from './pages/Workflows'
import RAGStores from './pages/RAGStores'
import LLMs from './pages/LLMs'
import Agents from './pages/Agents'
import Runs from './pages/Runs'
import Settings from './pages/Settings'

const { Header, Content, Sider } = Layout

const items = [
  { key: '/', label: 'Dashboard' },
  { key: '/workflows', label: 'Workflows' },
  { key: '/rag', label: 'RAG Stores' },
  { key: '/llms', label: 'LLMs' },
  { key: '/agents', label: 'Agents' },
  { key: '/runs', label: 'Runs' },
  { key: '/settings', label: 'Settings' },
]

export default function App() {
  const navigate = useNavigate()
  const location = useLocation()
  const {
    token: { colorBgContainer },
  } = theme.useToken()

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider breakpoint="lg" collapsedWidth="0">
        <div style={{ color: '#fff', padding: 16, fontWeight: 600 }}>M-GAIF</div>
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          onClick={(e) => navigate(e.key)}
          items={items}
        />
      </Sider>
      <Layout>
        <Header style={{ background: colorBgContainer, paddingInline: 16 }}>
          <div style={{ fontSize: 16, fontWeight: 600 }}>Modular GenAI Framework Console</div>
        </Header>
        <Content style={{ margin: 16 }}>
          <div style={{ padding: 16, minHeight: 360, background: colorBgContainer }}>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/workflows" element={<Workflows />} />
              <Route path="/rag" element={<RAGStores />} />
              <Route path="/llms" element={<LLMs />} />
              <Route path="/agents" element={<Agents />} />
              <Route path="/runs" element={<Runs />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </div>
        </Content>
      </Layout>
    </Layout>
  )
}
