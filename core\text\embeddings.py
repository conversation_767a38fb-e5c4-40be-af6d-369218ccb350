"""Text embedding interfaces and contracts.

This module defines the abstract embedder interface for converting text into
dense vector representations. Embedders are crucial for semantic search,
similarity computation, and various NLP tasks within the M-GAIF framework.

The embedder interface supports:
- Batch text-to-vector conversion
- Consistent vector dimensions
- Semantic similarity preservation
- Integration with vector databases

Implementations should provide:
- Consistent vector dimensions across all inputs
- Semantic similarity preservation (similar texts → similar vectors)
- Efficient batch processing
- Deterministic outputs for reproducibility

Example:
    >>> from plugins.embedders.simple_embedder import SimpleEmbedder
    >>> embedder = SimpleEmbedder(dim=128)
    >>> vectors = embedder.embed(["Hello world", "AI is amazing"])
    >>> print(len(vectors))  # 2
    >>> print(len(vectors[0]))  # 128

Note:
    Vector dimensions must be consistent within a single embedder instance.
    Different embedder implementations may produce incompatible vector spaces.
"""

from __future__ import annotations

from abc import ABC, abstractmethod
from typing import List


class Embedder(ABC):
    """Abstract embeddings interface.

    Implementations return a list of float vectors corresponding to input texts.
    """

    @abstractmethod
    def embed(self, texts: List[str]) -> List[List[float]]:
        """Produce embeddings for a batch of texts.

        Converts a list of text strings into dense vector representations
        that capture semantic meaning for similarity computation and retrieval.

        Args:
            texts: List of text strings to embed

        Returns:
            List of float vectors, one per input text, all with equal dimensions

        Example:
            >>> embedder = SimpleEmbedder(dim=128)
            >>> vectors = embedder.embed(["Hello world", "AI is amazing"])
            >>> print(len(vectors))  # 2
            >>> print(len(vectors[0]))  # 128
            >>> print(len(vectors[1]))  # 128

        Note:
            - All returned vectors must have the same dimensionality
            - Similar texts should produce similar vectors (high cosine similarity)
            - Empty input list should return empty output list
            - Implementation should be deterministic for reproducibility
        """
        raise NotImplementedError
