{"name": "mgaif-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.0", "react-router-dom": "^6.20.0", "zustand": "^4.4.0", "axios": "^1.6.0", "@tanstack/react-query": "^4.36.0", "react-hook-form": "^7.48.0", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "@vitejs/plugin-react": "^5.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}