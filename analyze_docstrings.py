#!/usr/bin/env python3
"""Analyze docstring quality and coverage in M-GAIF codebase."""

import ast
import os
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Set
from dataclasses import dataclass
from enum import Enum

class DocstringQuality(Enum):
    MISSING = "missing"
    MINIMAL = "minimal"  # Just a brief description
    GOOD = "good"       # Description + some parameters/returns
    EXCELLENT = "excellent"  # Full Sphinx-style with all details

@dataclass
class DocstringAnalysis:
    """Analysis result for a single docstring."""
    quality: DocstringQuality
    has_description: bool
    has_parameters: bool
    has_returns: bool
    has_raises: bool
    has_examples: bool
    sphinx_style: bool
    line_count: int
    content: str

@dataclass
class ModuleAnalysis:
    """Analysis result for a Python module."""
    file_path: str
    module_docstring: Optional[DocstringAnalysis]
    classes: Dict[str, DocstringAnalysis]
    functions: Dict[str, DocstringAnalysis]
    methods: Dict[str, DocstringAnalysis]
    total_items: int
    documented_items: int

class DocstringAnalyzer:
    """Analyzes Python code for docstring quality and coverage."""
    
    def __init__(self):
        self.sphinx_keywords = {
            ':param', ':type', ':return', ':returns', ':rtype', ':raises', 
            ':example', ':note', ':warning', ':see', ':since', ':deprecated',
            'Args:', 'Arguments:', 'Parameters:', 'Returns:', 'Return:', 
            'Yields:', 'Raises:', 'Note:', 'Example:', 'Examples:'
        }
    
    def analyze_docstring(self, docstring: Optional[str]) -> DocstringAnalysis:
        """Analyze a single docstring for quality and completeness."""
        if not docstring:
            return DocstringAnalysis(
                quality=DocstringQuality.MISSING,
                has_description=False,
                has_parameters=False,
                has_returns=False,
                has_raises=False,
                has_examples=False,
                sphinx_style=False,
                line_count=0,
                content=""
            )
        
        lines = docstring.strip().split('\n')
        line_count = len(lines)
        content = docstring.strip()
        
        # Check for Sphinx-style keywords
        sphinx_style = any(keyword in docstring for keyword in self.sphinx_keywords)
        
        # Check for specific sections
        has_description = len(content) > 10  # Minimal description
        has_parameters = any(keyword in docstring.lower() for keyword in 
                           ['param', 'parameter', 'arg', 'argument', ':param', 'args:'])
        has_returns = any(keyword in docstring.lower() for keyword in 
                         ['return', ':return', ':rtype', 'returns:'])
        has_raises = any(keyword in docstring.lower() for keyword in 
                        ['raise', ':raise', 'raises:'])
        has_examples = any(keyword in docstring.lower() for keyword in 
                          ['example', ':example', 'examples:'])
        
        # Determine quality
        if line_count >= 10 and sphinx_style and has_parameters and has_returns:
            quality = DocstringQuality.EXCELLENT
        elif line_count >= 5 and (sphinx_style or has_parameters or has_returns):
            quality = DocstringQuality.GOOD
        elif has_description:
            quality = DocstringQuality.MINIMAL
        else:
            quality = DocstringQuality.MISSING
        
        return DocstringAnalysis(
            quality=quality,
            has_description=has_description,
            has_parameters=has_parameters,
            has_returns=has_returns,
            has_raises=has_raises,
            has_examples=has_examples,
            sphinx_style=sphinx_style,
            line_count=line_count,
            content=content
        )
    
    def analyze_module(self, file_path: Path) -> ModuleAnalysis:
        """Analyze a Python module for docstring coverage."""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            tree = ast.parse(content)
            
            # Module docstring
            module_docstring = None
            if (tree.body and isinstance(tree.body[0], ast.Expr) and 
                isinstance(tree.body[0].value, ast.Constant) and 
                isinstance(tree.body[0].value.value, str)):
                module_docstring = self.analyze_docstring(tree.body[0].value.value)
            else:
                module_docstring = self.analyze_docstring(None)
            
            classes = {}
            functions = {}
            methods = {}
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    docstring = ast.get_docstring(node)
                    classes[node.name] = self.analyze_docstring(docstring)
                    
                    # Analyze methods within classes
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_docstring = ast.get_docstring(item)
                            method_name = f"{node.name}.{item.name}"
                            methods[method_name] = self.analyze_docstring(method_docstring)
                
                elif isinstance(node, ast.FunctionDef) and not any(
                    isinstance(parent, ast.ClassDef) for parent in ast.walk(tree) 
                    if hasattr(parent, 'body') and node in getattr(parent, 'body', [])
                ):
                    docstring = ast.get_docstring(node)
                    functions[node.name] = self.analyze_docstring(docstring)
            
            total_items = 1 + len(classes) + len(functions) + len(methods)  # +1 for module
            documented_items = sum([
                1 if module_docstring.quality != DocstringQuality.MISSING else 0,
                sum(1 for analysis in classes.values() if analysis.quality != DocstringQuality.MISSING),
                sum(1 for analysis in functions.values() if analysis.quality != DocstringQuality.MISSING),
                sum(1 for analysis in methods.values() if analysis.quality != DocstringQuality.MISSING)
            ])
            
            return ModuleAnalysis(
                file_path=str(file_path),
                module_docstring=module_docstring,
                classes=classes,
                functions=functions,
                methods=methods,
                total_items=total_items,
                documented_items=documented_items
            )
            
        except Exception as e:
            print(f"Error analyzing {file_path}: {e}")
            return ModuleAnalysis(
                file_path=str(file_path),
                module_docstring=self.analyze_docstring(None),
                classes={},
                functions={},
                methods={},
                total_items=0,
                documented_items=0
            )
    
    def analyze_codebase(self, root_path: Path) -> Dict[str, ModuleAnalysis]:
        """Analyze entire codebase for docstring quality."""
        results = {}
        
        # Analyze core and plugins directories
        for pattern in ["core/**/*.py", "plugins/**/*.py"]:
            for py_file in root_path.glob(pattern):
                if py_file.name.startswith('test_') or py_file.name == '__init__.py':
                    continue
                
                relative_path = py_file.relative_to(root_path)
                results[str(relative_path)] = self.analyze_module(py_file)
        
        return results
    
    def generate_report(self, analyses: Dict[str, ModuleAnalysis]) -> str:
        """Generate a comprehensive docstring quality report."""
        report = []
        report.append("# M-GAIF Docstring Quality Analysis Report")
        report.append("=" * 50)
        
        # Overall statistics
        total_modules = len(analyses)
        total_items = sum(analysis.total_items for analysis in analyses.values())
        total_documented = sum(analysis.documented_items for analysis in analyses.values())
        overall_coverage = (total_documented / total_items * 100) if total_items > 0 else 0
        
        report.append(f"\n## Overall Statistics")
        report.append(f"- **Total Modules**: {total_modules}")
        report.append(f"- **Total Items**: {total_items}")
        report.append(f"- **Documented Items**: {total_documented}")
        report.append(f"- **Overall Coverage**: {overall_coverage:.1f}%")
        
        # Quality breakdown
        quality_counts = {quality: 0 for quality in DocstringQuality}
        sphinx_count = 0
        
        for analysis in analyses.values():
            all_docstrings = [analysis.module_docstring] + list(analysis.classes.values()) + list(analysis.functions.values()) + list(analysis.methods.values())
            for docstring in all_docstrings:
                quality_counts[docstring.quality] += 1
                if docstring.sphinx_style:
                    sphinx_count += 1
        
        report.append(f"\n## Quality Breakdown")
        for quality, count in quality_counts.items():
            percentage = (count / total_items * 100) if total_items > 0 else 0
            report.append(f"- **{quality.value.title()}**: {count} ({percentage:.1f}%)")
        
        sphinx_percentage = (sphinx_count / total_items * 100) if total_items > 0 else 0
        report.append(f"- **Sphinx-style**: {sphinx_count} ({sphinx_percentage:.1f}%)")
        
        # Module-by-module analysis
        report.append(f"\n## Module Analysis")
        report.append("-" * 30)
        
        for module_path, analysis in sorted(analyses.items()):
            coverage = (analysis.documented_items / analysis.total_items * 100) if analysis.total_items > 0 else 0
            report.append(f"\n### {module_path}")
            report.append(f"- **Coverage**: {analysis.documented_items}/{analysis.total_items} ({coverage:.1f}%)")
            report.append(f"- **Module Docstring**: {analysis.module_docstring.quality.value}")
            
            if analysis.classes:
                report.append(f"- **Classes**: {len(analysis.classes)}")
                for class_name, class_analysis in analysis.classes.items():
                    report.append(f"  - `{class_name}`: {class_analysis.quality.value}")
            
            if analysis.functions:
                report.append(f"- **Functions**: {len(analysis.functions)}")
                for func_name, func_analysis in analysis.functions.items():
                    report.append(f"  - `{func_name}`: {func_analysis.quality.value}")
            
            if analysis.methods:
                report.append(f"- **Methods**: {len(analysis.methods)}")
                for method_name, method_analysis in analysis.methods.items():
                    report.append(f"  - `{method_name}`: {method_analysis.quality.value}")
        
        # Recommendations
        report.append(f"\n## Recommendations")
        report.append("-" * 20)
        
        if overall_coverage < 80:
            report.append("- **LOW COVERAGE**: Increase docstring coverage to at least 80%")
        
        if sphinx_percentage < 50:
            report.append("- **SPHINX STYLE**: Adopt Sphinx-style docstrings for better documentation")
        
        missing_modules = [path for path, analysis in analyses.items() 
                          if analysis.module_docstring.quality == DocstringQuality.MISSING]
        if missing_modules:
            report.append(f"- **MODULE DOCSTRINGS**: Add module docstrings to: {', '.join(missing_modules[:5])}")
        
        return "\n".join(report)

def main():
    """Main analysis execution."""
    project_root = Path(__file__).parent
    analyzer = DocstringAnalyzer()
    
    print("🔍 Analyzing M-GAIF codebase docstrings...")
    analyses = analyzer.analyze_codebase(project_root)
    
    print("📊 Generating report...")
    report = analyzer.generate_report(analyses)
    
    # Save report
    report_path = project_root / "docstring_analysis_report.md"
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"✅ Report saved to: {report_path}")
    print("\n" + "=" * 50)
    print(report)

if __name__ == "__main__":
    main()
