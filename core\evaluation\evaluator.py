"""Evaluation orchestration system.

This module provides the main Evaluator class that orchestrates comprehensive
model evaluation using multiple metrics and benchmarks.

The evaluator provides:
- Multi-metric evaluation workflows
- Benchmark orchestration and comparison
- Statistical analysis and significance testing
- Performance profiling and cost tracking
- Comprehensive reporting and visualization

Key Components:
- Evaluator: Main evaluation orchestration class
- EvaluationConfig: Configuration management
- EvaluationReport: Comprehensive evaluation results

Example:
    >>> from core.evaluation import Evaluator, BLEUMetric, HellaSwagBenchmark
    >>> evaluator = Evaluator()
    >>> evaluator.add_metric(BLEUMetric())
    >>> evaluator.add_benchmark(HellaSwagBenchmark())
    >>> 
    >>> results = await evaluator.evaluate_model(model, test_data)
    >>> report = evaluator.generate_report(results)
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime
import statistics
import time

from .base import Metric, EvaluationResult, EvaluationError
from .benchmarks import Benchmark, BenchmarkResult

logger = logging.getLogger(__name__)


@dataclass
class EvaluationConfig:
    """Configuration for evaluation runs."""
    
    # General settings
    run_name: str = "evaluation_run"
    description: str = ""
    
    # Sampling settings
    sample_size: Optional[int] = None
    random_seed: int = 42
    
    # Performance settings
    max_concurrent_evaluations: int = 5
    timeout_seconds: float = 300.0
    
    # Reporting settings
    include_detailed_results: bool = True
    include_statistical_analysis: bool = True
    save_intermediate_results: bool = True
    
    # Cost tracking
    track_costs: bool = True
    cost_per_token: float = 0.0001
    
    # Comparison settings
    baseline_model: Optional[str] = None
    significance_threshold: float = 0.05


@dataclass
class EvaluationReport:
    """Comprehensive evaluation report."""
    
    config: EvaluationConfig
    model_name: str
    
    # Metric results
    metric_scores: Dict[str, float] = field(default_factory=dict)
    metric_details: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Benchmark results
    benchmark_scores: Dict[str, float] = field(default_factory=dict)
    benchmark_details: Dict[str, BenchmarkResult] = field(default_factory=dict)
    
    # Performance metrics
    total_evaluation_time: float = 0.0
    total_cost: float = 0.0
    tokens_used: int = 0
    
    # Statistical analysis
    statistical_summary: Dict[str, Any] = field(default_factory=dict)
    
    # Comparison results (if baseline provided)
    comparison_results: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def get_overall_score(self) -> float:
        """Calculate overall evaluation score."""
        all_scores = list(self.metric_scores.values()) + list(self.benchmark_scores.values())
        return statistics.mean(all_scores) if all_scores else 0.0
    
    def get_summary(self) -> Dict[str, Any]:
        """Get evaluation summary."""
        return {
            "model_name": self.model_name,
            "overall_score": self.get_overall_score(),
            "metric_scores": self.metric_scores,
            "benchmark_scores": self.benchmark_scores,
            "evaluation_time": self.total_evaluation_time,
            "total_cost": self.total_cost,
            "timestamp": self.timestamp.isoformat()
        }


class Evaluator:
    """Main evaluation orchestration system."""
    
    def __init__(self, config: Optional[EvaluationConfig] = None):
        self.config = config or EvaluationConfig()
        self.metrics: List[Metric] = []
        self.benchmarks: List[Benchmark] = []
        self.evaluation_history: List[EvaluationReport] = []
        
        logger.info(f"Evaluator initialized with config: {self.config.run_name}")
    
    def add_metric(self, metric: Metric) -> None:
        """Add a metric to the evaluation suite."""
        self.metrics.append(metric)
        logger.info(f"Added metric: {metric.name}")
    
    def add_benchmark(self, benchmark: Benchmark) -> None:
        """Add a benchmark to the evaluation suite."""
        self.benchmarks.append(benchmark)
        logger.info(f"Added benchmark: {benchmark.name}")
    
    def remove_metric(self, metric_name: str) -> bool:
        """Remove a metric by name."""
        for i, metric in enumerate(self.metrics):
            if metric.name == metric_name:
                del self.metrics[i]
                logger.info(f"Removed metric: {metric_name}")
                return True
        return False
    
    def remove_benchmark(self, benchmark_name: str) -> bool:
        """Remove a benchmark by name."""
        for i, benchmark in enumerate(self.benchmarks):
            if benchmark.name == benchmark_name:
                del self.benchmarks[i]
                logger.info(f"Removed benchmark: {benchmark_name}")
                return True
        return False
    
    async def evaluate_model(self, model, test_data: Optional[List[Dict[str, Any]]] = None,
                           model_name: str = "unknown_model") -> EvaluationReport:
        """Run comprehensive evaluation on a model."""
        logger.info(f"Starting evaluation of {model_name}")
        start_time = time.time()
        
        report = EvaluationReport(
            config=self.config,
            model_name=model_name
        )
        
        try:
            # Run metrics evaluation
            if self.metrics and test_data:
                await self._evaluate_metrics(model, test_data, report)
            
            # Run benchmark evaluation
            if self.benchmarks:
                await self._evaluate_benchmarks(model, report)
            
            # Calculate total evaluation time
            report.total_evaluation_time = time.time() - start_time
            
            # Perform statistical analysis
            if self.config.include_statistical_analysis:
                self._perform_statistical_analysis(report)
            
            # Compare with baseline if provided
            if self.config.baseline_model:
                await self._compare_with_baseline(report)
            
            # Store in history
            self.evaluation_history.append(report)
            
            logger.info(f"Evaluation completed in {report.total_evaluation_time:.2f}s")
            logger.info(f"Overall score: {report.get_overall_score():.3f}")
            
            return report
            
        except Exception as e:
            logger.error(f"Evaluation failed: {e}")
            raise EvaluationError(f"Model evaluation failed: {e}")
    
    async def _evaluate_metrics(self, model, test_data: List[Dict[str, Any]], 
                              report: EvaluationReport) -> None:
        """Evaluate model using all configured metrics."""
        logger.info(f"Evaluating {len(self.metrics)} metrics on {len(test_data)} samples")
        
        # Limit sample size if configured
        if self.config.sample_size and self.config.sample_size < len(test_data):
            import random
            random.seed(self.config.random_seed)
            test_data = random.sample(test_data, self.config.sample_size)
        
        # Run metrics in parallel with concurrency limit
        semaphore = asyncio.Semaphore(self.config.max_concurrent_evaluations)
        
        async def evaluate_metric_with_semaphore(metric: Metric) -> Tuple[str, EvaluationResult]:
            async with semaphore:
                try:
                    result = await asyncio.wait_for(
                        metric.evaluate(model, test_data),
                        timeout=self.config.timeout_seconds
                    )
                    return metric.name, result
                except asyncio.TimeoutError:
                    logger.error(f"Metric {metric.name} timed out")
                    return metric.name, EvaluationResult(
                        metric_name=metric.name,
                        score=0.0,
                        metadata={"error": "timeout"}
                    )
                except Exception as e:
                    logger.error(f"Metric {metric.name} failed: {e}")
                    return metric.name, EvaluationResult(
                        metric_name=metric.name,
                        score=0.0,
                        metadata={"error": str(e)}
                    )
        
        # Execute all metrics
        metric_tasks = [evaluate_metric_with_semaphore(metric) for metric in self.metrics]
        metric_results = await asyncio.gather(*metric_tasks)
        
        # Store results
        for metric_name, result in metric_results:
            report.metric_scores[metric_name] = result.score
            report.metric_details[metric_name] = {
                "score": result.score,
                "metadata": result.metadata,
                "timestamp": result.timestamp.isoformat()
            }
            
            # Track costs if enabled
            if self.config.track_costs and "tokens_used" in result.metadata:
                tokens = result.metadata["tokens_used"]
                cost = tokens * self.config.cost_per_token
                report.tokens_used += tokens
                report.total_cost += cost
    
    async def _evaluate_benchmarks(self, model, report: EvaluationReport) -> None:
        """Evaluate model using all configured benchmarks."""
        logger.info(f"Running {len(self.benchmarks)} benchmarks")
        
        # Run benchmarks in parallel with concurrency limit
        semaphore = asyncio.Semaphore(self.config.max_concurrent_evaluations)
        
        async def evaluate_benchmark_with_semaphore(benchmark: Benchmark) -> Tuple[str, BenchmarkResult]:
            async with semaphore:
                try:
                    result = await asyncio.wait_for(
                        benchmark.run_benchmark(model, self.config.sample_size),
                        timeout=self.config.timeout_seconds
                    )
                    return benchmark.name, result
                except asyncio.TimeoutError:
                    logger.error(f"Benchmark {benchmark.name} timed out")
                    return benchmark.name, BenchmarkResult(
                        benchmark_name=benchmark.name,
                        score=0.0,
                        total_items=0,
                        correct_items=0,
                        accuracy=0.0,
                        metadata={"error": "timeout"}
                    )
                except Exception as e:
                    logger.error(f"Benchmark {benchmark.name} failed: {e}")
                    return benchmark.name, BenchmarkResult(
                        benchmark_name=benchmark.name,
                        score=0.0,
                        total_items=0,
                        correct_items=0,
                        accuracy=0.0,
                        metadata={"error": str(e)}
                    )
        
        # Execute all benchmarks
        benchmark_tasks = [evaluate_benchmark_with_semaphore(benchmark) for benchmark in self.benchmarks]
        benchmark_results = await asyncio.gather(*benchmark_tasks)
        
        # Store results
        for benchmark_name, result in benchmark_results:
            report.benchmark_scores[benchmark_name] = result.score
            report.benchmark_details[benchmark_name] = result
    
    def _perform_statistical_analysis(self, report: EvaluationReport) -> None:
        """Perform statistical analysis on evaluation results."""
        all_scores = list(report.metric_scores.values()) + list(report.benchmark_scores.values())
        
        if not all_scores:
            return
        
        try:
            analysis = {
                "mean_score": statistics.mean(all_scores),
                "median_score": statistics.median(all_scores),
                "std_deviation": statistics.stdev(all_scores) if len(all_scores) > 1 else 0.0,
                "min_score": min(all_scores),
                "max_score": max(all_scores),
                "score_range": max(all_scores) - min(all_scores),
                "num_evaluations": len(all_scores)
            }
            
            # Calculate percentiles
            sorted_scores = sorted(all_scores)
            n = len(sorted_scores)
            analysis["percentile_25"] = sorted_scores[int(0.25 * n)]
            analysis["percentile_75"] = sorted_scores[int(0.75 * n)]
            analysis["iqr"] = analysis["percentile_75"] - analysis["percentile_25"]
            
            report.statistical_summary = analysis
            
        except Exception as e:
            logger.error(f"Statistical analysis failed: {e}")
            report.statistical_summary = {"error": str(e)}
    
    async def _compare_with_baseline(self, report: EvaluationReport) -> None:
        """Compare results with baseline model."""
        # Find baseline results in history
        baseline_report = None
        for historical_report in self.evaluation_history:
            if historical_report.model_name == self.config.baseline_model:
                baseline_report = historical_report
                break
        
        if not baseline_report:
            logger.warning(f"Baseline model {self.config.baseline_model} not found in history")
            return
        
        try:
            comparison = {
                "baseline_model": self.config.baseline_model,
                "current_model": report.model_name,
                "overall_improvement": report.get_overall_score() - baseline_report.get_overall_score(),
                "metric_comparisons": {},
                "benchmark_comparisons": {}
            }
            
            # Compare metrics
            for metric_name, current_score in report.metric_scores.items():
                if metric_name in baseline_report.metric_scores:
                    baseline_score = baseline_report.metric_scores[metric_name]
                    improvement = current_score - baseline_score
                    comparison["metric_comparisons"][metric_name] = {
                        "current_score": current_score,
                        "baseline_score": baseline_score,
                        "improvement": improvement,
                        "relative_improvement": improvement / baseline_score if baseline_score != 0 else 0.0
                    }
            
            # Compare benchmarks
            for benchmark_name, current_score in report.benchmark_scores.items():
                if benchmark_name in baseline_report.benchmark_scores:
                    baseline_score = baseline_report.benchmark_scores[benchmark_name]
                    improvement = current_score - baseline_score
                    comparison["benchmark_comparisons"][benchmark_name] = {
                        "current_score": current_score,
                        "baseline_score": baseline_score,
                        "improvement": improvement,
                        "relative_improvement": improvement / baseline_score if baseline_score != 0 else 0.0
                    }
            
            report.comparison_results = comparison
            
        except Exception as e:
            logger.error(f"Baseline comparison failed: {e}")
            report.comparison_results = {"error": str(e)}
    
    def generate_report(self, evaluation_result: EvaluationReport, 
                       format: str = "dict") -> Union[Dict[str, Any], str]:
        """Generate formatted evaluation report."""
        if format == "dict":
            return evaluation_result.get_summary()
        elif format == "json":
            import json
            return json.dumps(evaluation_result.get_summary(), indent=2)
        elif format == "markdown":
            return self._generate_markdown_report(evaluation_result)
        else:
            raise ValueError(f"Unsupported report format: {format}")
    
    def _generate_markdown_report(self, report: EvaluationReport) -> str:
        """Generate markdown evaluation report."""
        md = f"""# Evaluation Report: {report.model_name}

**Run Name:** {report.config.run_name}
**Timestamp:** {report.timestamp.strftime('%Y-%m-%d %H:%M:%S')}
**Overall Score:** {report.get_overall_score():.3f}

## Summary

- **Evaluation Time:** {report.total_evaluation_time:.2f} seconds
- **Total Cost:** ${report.total_cost:.4f}
- **Tokens Used:** {report.tokens_used:,}

## Metric Scores

"""
        
        for metric_name, score in report.metric_scores.items():
            md += f"- **{metric_name}:** {score:.3f}\n"
        
        md += "\n## Benchmark Scores\n\n"
        
        for benchmark_name, score in report.benchmark_scores.items():
            md += f"- **{benchmark_name}:** {score:.3f}\n"
        
        if report.statistical_summary:
            md += f"\n## Statistical Analysis\n\n"
            stats = report.statistical_summary
            md += f"- **Mean Score:** {stats.get('mean_score', 0):.3f}\n"
            md += f"- **Standard Deviation:** {stats.get('std_deviation', 0):.3f}\n"
            md += f"- **Score Range:** {stats.get('min_score', 0):.3f} - {stats.get('max_score', 0):.3f}\n"
        
        if report.comparison_results:
            md += f"\n## Baseline Comparison\n\n"
            comp = report.comparison_results
            md += f"- **Baseline Model:** {comp.get('baseline_model', 'N/A')}\n"
            md += f"- **Overall Improvement:** {comp.get('overall_improvement', 0):.3f}\n"
        
        return md
    
    def get_evaluation_history(self) -> List[EvaluationReport]:
        """Get evaluation history."""
        return self.evaluation_history.copy()
    
    def clear_history(self) -> None:
        """Clear evaluation history."""
        self.evaluation_history.clear()
        logger.info("Evaluation history cleared")
