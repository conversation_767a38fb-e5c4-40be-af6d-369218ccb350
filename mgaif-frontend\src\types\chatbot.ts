// Chatbot Configuration Types
export interface ChatbotConfig {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  personality: ChatbotPersonality;
  knowledgeBase: KnowledgeBase;
  llmSettings: LLMSettings;
  conversationSettings: ConversationSettings;
  integrations: ChatbotIntegration[];
  status: 'draft' | 'training' | 'active' | 'inactive';
  createdAt: string;
  updatedAt: string;
  userId: string;
}

// Personality Configuration
export interface ChatbotPersonality {
  tone: 'professional' | 'friendly' | 'casual' | 'formal' | 'enthusiastic' | 'empathetic';
  style: 'concise' | 'detailed' | 'conversational' | 'technical';
  language: string;
  customInstructions?: string;
  greeting: string;
  fallbackMessage: string;
  examples: PersonalityExample[];
}

export interface PersonalityExample {
  id: string;
  userMessage: string;
  botResponse: string;
  category: 'greeting' | 'question' | 'help' | 'error' | 'goodbye';
}

// Knowledge Base Types
export interface KnowledgeBase {
  id: string;
  name: string;
  documents: KnowledgeDocument[];
  sources: KnowledgeSource[];
  indexingStatus: 'pending' | 'processing' | 'completed' | 'failed';
  lastUpdated: string;
  totalDocuments: number;
  totalChunks: number;
}

export interface KnowledgeDocument {
  id: string;
  name: string;
  type: 'pdf' | 'txt' | 'docx' | 'md' | 'url' | 'api';
  size: number;
  uploadedAt: string;
  status: 'pending' | 'processing' | 'indexed' | 'failed';
  chunks: number;
  metadata: Record<string, any>;
  content?: string;
  error?: string;
}

export interface KnowledgeSource {
  id: string;
  type: 'website' | 'api' | 'database' | 'rss';
  name: string;
  url: string;
  config: Record<string, any>;
  lastSync: string;
  status: 'active' | 'inactive' | 'error';
}

// LLM Configuration
export interface LLMSettings {
  provider: 'openai' | 'anthropic' | 'cohere' | 'huggingface';
  model: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  systemPrompt: string;
  contextWindow: number;
}

// Conversation Settings
export interface ConversationSettings {
  maxHistory: number;
  contextRetention: 'session' | 'persistent' | 'none';
  enableMemory: boolean;
  memoryDuration: number; // in days
  enableFeedback: boolean;
  enableAnalytics: boolean;
  rateLimiting: RateLimitConfig;
  moderationSettings: ModerationSettings;
}

export interface RateLimitConfig {
  enabled: boolean;
  messagesPerMinute: number;
  messagesPerHour: number;
  messagesPerDay: number;
}

export interface ModerationSettings {
  enabled: boolean;
  filterProfanity: boolean;
  filterSpam: boolean;
  customFilters: string[];
  escalationRules: EscalationRule[];
}

export interface EscalationRule {
  id: string;
  trigger: 'keyword' | 'sentiment' | 'confidence' | 'repetition';
  condition: string;
  action: 'flag' | 'transfer' | 'block' | 'notify';
  target?: string;
}

// Integration Types
export interface ChatbotIntegration {
  id: string;
  type: 'website' | 'slack' | 'discord' | 'whatsapp' | 'telegram' | 'api';
  name: string;
  config: IntegrationConfig;
  status: 'active' | 'inactive' | 'error';
  createdAt: string;
}

export interface IntegrationConfig {
  // Website Widget
  widgetSettings?: {
    position: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    theme: 'light' | 'dark' | 'auto';
    primaryColor: string;
    welcomeMessage: string;
    placeholder: string;
    showAvatar: boolean;
    showTyping: boolean;
  };
  
  // API Integration
  apiSettings?: {
    webhookUrl?: string;
    apiKey?: string;
    headers?: Record<string, string>;
    authentication?: 'none' | 'bearer' | 'basic' | 'custom';
  };
  
  // Platform-specific settings
  platformSettings?: Record<string, any>;
}

// Conversation & Message Types
export interface Conversation {
  id: string;
  chatbotId: string;
  userId?: string;
  sessionId: string;
  messages: Message[];
  metadata: ConversationMetadata;
  status: 'active' | 'ended' | 'transferred';
  startedAt: string;
  endedAt?: string;
  rating?: number;
  feedback?: string;
}

export interface Message {
  id: string;
  conversationId: string;
  type: 'user' | 'bot' | 'system';
  content: string;
  timestamp: string;
  metadata: MessageMetadata;
  attachments?: MessageAttachment[];
}

export interface MessageMetadata {
  confidence?: number;
  sources?: string[];
  processingTime?: number;
  tokens?: number;
  cost?: number;
  intent?: string;
  entities?: Record<string, any>;
  sentiment?: 'positive' | 'negative' | 'neutral';
}

export interface ConversationMetadata {
  userAgent?: string;
  ipAddress?: string;
  referrer?: string;
  location?: string;
  device?: string;
  integration: string;
  customData?: Record<string, any>;
}

export interface MessageAttachment {
  id: string;
  type: 'image' | 'file' | 'audio' | 'video';
  name: string;
  url: string;
  size: number;
  mimeType: string;
}

// Analytics Types
export interface ChatbotAnalytics {
  chatbotId: string;
  period: 'day' | 'week' | 'month' | 'year';
  startDate: string;
  endDate: string;
  metrics: AnalyticsMetrics;
  conversations: ConversationAnalytics[];
  topQuestions: QuestionAnalytics[];
  userSatisfaction: SatisfactionMetrics;
}

export interface AnalyticsMetrics {
  totalConversations: number;
  totalMessages: number;
  averageConversationLength: number;
  averageResponseTime: number;
  resolutionRate: number;
  escalationRate: number;
  userSatisfactionScore: number;
  activeUsers: number;
  returningUsers: number;
}

export interface ConversationAnalytics {
  date: string;
  count: number;
  averageLength: number;
  satisfactionScore: number;
}

export interface QuestionAnalytics {
  question: string;
  count: number;
  averageConfidence: number;
  satisfactionScore: number;
  category?: string;
}

export interface SatisfactionMetrics {
  totalRatings: number;
  averageRating: number;
  ratingDistribution: Record<number, number>;
  positivePercentage: number;
  negativePercentage: number;
}

// Training & Testing Types
export interface TrainingData {
  id: string;
  chatbotId: string;
  type: 'qa_pairs' | 'conversations' | 'documents';
  data: TrainingExample[];
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  processedAt?: string;
  error?: string;
}

export interface TrainingExample {
  id: string;
  input: string;
  expectedOutput: string;
  category?: string;
  metadata?: Record<string, any>;
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  input: string;
  expectedOutput: string;
  actualOutput?: string;
  passed?: boolean;
  confidence?: number;
  executedAt?: string;
}

export interface TestSuite {
  id: string;
  chatbotId: string;
  name: string;
  description: string;
  testCases: TestCase[];
  status: 'draft' | 'running' | 'completed' | 'failed';
  results?: TestResults;
  createdAt: string;
  lastRun?: string;
}

export interface TestResults {
  totalTests: number;
  passedTests: number;
  failedTests: number;
  averageConfidence: number;
  executionTime: number;
  passRate: number;
}

// Deployment Types
export interface ChatbotDeployment {
  id: string;
  chatbotId: string;
  environment: 'development' | 'staging' | 'production';
  version: string;
  status: 'deploying' | 'active' | 'inactive' | 'failed';
  url?: string;
  apiKey?: string;
  deployedAt: string;
  config: DeploymentConfig;
}

export interface DeploymentConfig {
  scaling: {
    minInstances: number;
    maxInstances: number;
    targetCPU: number;
  };
  security: {
    enableCORS: boolean;
    allowedOrigins: string[];
    enableRateLimit: boolean;
    enableAuth: boolean;
  };
  monitoring: {
    enableLogs: boolean;
    enableMetrics: boolean;
    enableAlerts: boolean;
  };
}

// Form Types for UI
export interface ChatbotFormData {
  name: string;
  description: string;
  personality: Partial<ChatbotPersonality>;
  llmSettings: Partial<LLMSettings>;
  conversationSettings: Partial<ConversationSettings>;
}

export interface KnowledgeUploadData {
  files: File[];
  urls: string[];
  sources: Partial<KnowledgeSource>[];
}

// State Types
export interface ChatbotBuilderState {
  currentChatbot: ChatbotConfig | null;
  chatbots: ChatbotConfig[];
  knowledgeBases: KnowledgeBase[];
  conversations: Conversation[];
  analytics: ChatbotAnalytics | null;
  testSuites: TestSuite[];
  deployments: ChatbotDeployment[];
  isLoading: boolean;
  error: string | null;
  currentStep: BuilderStep;
}

export type BuilderStep = 
  | 'basic-info'
  | 'personality'
  | 'knowledge-base'
  | 'llm-settings'
  | 'conversation-settings'
  | 'integrations'
  | 'testing'
  | 'deployment'
  | 'analytics';

// API Response Types
export interface ChatbotResponse {
  message: string;
  confidence: number;
  sources: string[];
  metadata: MessageMetadata;
}

export interface ChatCompletionRequest {
  message: string;
  conversationId?: string;
  sessionId: string;
  metadata?: Record<string, any>;
}
