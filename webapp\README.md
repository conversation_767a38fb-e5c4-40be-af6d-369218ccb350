# M-GAIF Console (Webapp)

A professional-grade React + TypeScript web console for configuring and orchestrating the Modular GenAI Framework without code.

## Features

- **Dashboard**: Overview and quick navigation to main features
- **Workflow Builder**: Visual YAML editor with templates and execution via MCP API
- **RAG Store Manager**: Document indexing and semantic search via MCP retriever
- **LLM Tester**: Dual-mode testing (Edge OpenAI-compatible and MCP LLM tool) with streaming support
- **Agent Designer**: Future agent configuration and management (coming soon)
- **Runs Monitor**: Execution history and performance tracking (placeholder)
- **Settings**: API base URL configuration and preferences

## Prerequisites

- **Node.js 18+** for frontend development
- **Backend Services** running locally (recommended):
  - MCP API: `uvicorn core.mcp.api:app --reload` (port 8000)
  - Edge API: `uvicorn core.edge.api:app --reload` (port 8000)
- **Modern Browser** with ES2020 support (Chrome 90+, Firefox 88+, Safari 14+)

## Install & run

```powershell
# from webapp/
npm install
npm run dev
```
- Open http://127.0.0.1:5173
- The dev server proxies `/mcp` and `/v1` to `http://127.0.0.1:8000` by default (configure with `VITE_API_BASE` or in-app Settings)

## Build

```powershell
npm run build
npm run preview
```

## Configuration

### API Backend Setup

The webapp communicates with the M-GAIF backend through two APIs:

1. **MCP API** (`/mcp/tools/*`): Tool-based interface for workflows, RAG, and utilities
2. **Edge API** (`/v1/chat/completions`): OpenAI-compatible chat interface

### Environment Configuration

- **Development**: Auto-detects backend via Vite proxy (no configuration needed)
- **Production**: Configure API base URL in Settings page (e.g., `http://127.0.0.1:8000`)
- **Custom Setup**: Set `VITE_API_BASE` environment variable or use Settings page

### Settings Page Configuration

1. Navigate to Settings in the sidebar
2. Enter your backend URL (e.g., `http://127.0.0.1:8000`)
3. Click "Save" to persist settings in `localStorage` under `mgaif.apiBase`
4. Use "Clear" to reset to proxy mode

## Feature Guide

### Dashboard
- Central hub with quick navigation cards
- Overview of framework capabilities
- Responsive design for all screen sizes

### Workflows
- **Templates**: Pre-built RAG and LLM workflow examples
- **YAML Editor**: Live editing with syntax highlighting
- **Execution**: Run workflows and view JSON state output
- **Handlers**: Support for set, retrieve, call_llm, noop operations

### RAG Stores
- **Document Indexing**: Bulk upload via text input (one doc per line)
- **Semantic Search**: Query with configurable top_k results
- **Real-time Results**: Instant search with similarity scores
- **Document Management**: View indexed documents with IDs

### LLMs
- **Dual Backend**: Switch between Edge and MCP APIs
- **Streaming Support**: Real-time response streaming (Edge API)
- **Model Configuration**: Specify model names for different adapters
- **Interactive Testing**: Multi-line prompts with formatted output

### Settings
- **API Configuration**: Set custom backend URLs
- **Persistent Storage**: Settings saved in browser localStorage
- **Proxy Support**: Automatic development proxy detection

## Technical Details

### Architecture
- **Frontend**: React 18 + TypeScript + Ant Design
- **Build Tool**: Vite with hot module replacement
- **API Client**: Axios with streaming support via Fetch API
- **Routing**: React Router for SPA navigation
- **State Management**: React hooks and local state

### API Integration
- **MCP Tools**: Workflow execution, RAG operations, LLM chat
- **Edge API**: OpenAI-compatible streaming chat completions
- **Error Handling**: User-friendly error messages and loading states
- **CORS Support**: Configurable for production deployments

### Performance
- **Code Splitting**: Automatic route-based code splitting
- **Tree Shaking**: Optimized bundle size
- **Lazy Loading**: Components loaded on demand
- **Caching**: API responses cached where appropriate

## Troubleshooting

### Common Issues

1. **Backend Connection Failed**:
   - Verify backend services are running on port 8000
   - Check API base URL in Settings
   - Ensure CORS is properly configured for production

2. **Workflow Execution Errors**:
   - Validate YAML syntax in the editor
   - Check that all node IDs are unique
   - Verify handler parameters match expected format

3. **RAG Search Returns No Results**:
   - Ensure documents are properly indexed first
   - Check query relevance and spelling
   - Try adjusting the top_k parameter

4. **LLM Streaming Not Working**:
   - Use Edge API mode for streaming support
   - Verify model name is correct (e.g., "llama3")
   - Check network connectivity and CORS settings

### Debug Mode
- Open browser developer tools
- Monitor network requests in Network tab
- Check console for JavaScript errors
- Inspect API responses for debugging

## Development

### Project Structure
```
webapp/
├── src/
│   ├── api/          # API client and TypeScript types
│   ├── pages/        # React page components
│   ├── App.tsx       # Main application with routing
│   └── main.tsx      # Entry point and React setup
├── package.json      # Dependencies and scripts
├── vite.config.ts    # Vite build configuration
└── tsconfig.json     # TypeScript configuration
```

### Adding New Features
1. Create new page component in `src/pages/`
2. Add route to `App.tsx` routes array
3. Update navigation menu items
4. Implement API integration in `src/api/client.ts`
5. Add proper TypeScript types and error handling

### Building for Production
```bash
npm run build
```
Output will be in `dist/` directory, ready for static hosting or CDN deployment.
