import json
from pathlib import Path

import pytest
import yaml

from core.workflow.schema import WorkflowSpec
from core.workflow.engine import WorkflowEngine


@pytest.mark.asyncio
async def test_workflow_engine_example_yaml(async_bench):
    cfg_path = Path("configs/workflows/example.yaml")
    with cfg_path.open("r", encoding="utf-8") as f:
        cfg = yaml.safe_load(f)
    spec = WorkflowSpec.model_validate(cfg)
    engine = WorkflowEngine(spec)

    state = await engine.run({})
    expected_text = "The rapid advancement of artificial intelligence has revolutionized numerous industries, creating unprecedented opportunities for innovation and efficiency."
    assert state.get("input") == expected_text
    assert state.get("echoed") == expected_text

    metrics = await async_bench(lambda: engine.run({}), iterations=20)
    assert metrics["p95_ms"] < 10.0


@pytest.mark.asyncio
async def test_workflow_branching(async_bench):
    # Build a branching workflow: start(set flag=true) -> (edge with condition flag) -> then end
    cfg = {
        "name": "branching",
        "nodes": [
            {"id": "start", "handler": "set", "params": {"flag": True}},
            {"id": "true_path", "handler": "set", "params": {"path": "true"}},
            {"id": "false_path", "handler": "set", "params": {"path": "false"}},
            {"id": "end", "handler": "noop"},
        ],
        "edges": [
            {"from": "start", "to": "true_path", "condition": "flag"},
            {"from": "start", "to": "false_path"},
            {"from": "true_path", "to": "end"},
            {"from": "false_path", "to": "end"},
        ],
    }
    spec = WorkflowSpec.model_validate(cfg)
    engine = WorkflowEngine(spec)
    st = await engine.run({})
    assert st.get("path") == "true"
    metrics = await async_bench(lambda: engine.run({}), iterations=10)
    assert metrics["qps"] > 50  # sanity


@pytest.mark.asyncio
async def test_workflow_cycle_detection():
    cfg = {
        "name": "cycle",
        "nodes": [
            {"id": "a", "handler": "noop"},
            {"id": "b", "handler": "noop"},
        ],
        "edges": [
            {"from": "a", "to": "b"},
            {"from": "b", "to": "a"},
        ],
    }
    spec = WorkflowSpec.model_validate(cfg)
    engine = WorkflowEngine(spec)
    with pytest.raises(RuntimeError, match="Cycle detected"):
        await engine.run({})
