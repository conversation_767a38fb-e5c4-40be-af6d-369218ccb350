# 🚀 M-GAIF: Modular Generative AI Framework

[![CI Status](https://github.com/your-org/mgaif/workflows/CI/badge.svg)](https://github.com/your-org/mgaif/actions)
[![Security Scan](https://github.com/your-org/mgaif/workflows/Security/badge.svg)](https://github.com/your-org/mgaif/actions)
[![Code Quality](https://img.shields.io/badge/code%20quality-A-green)](https://github.com/your-org/mgaif)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

A **production-ready, enterprise-grade** modular framework for building, deploying, and managing AI applications with a focus on **security**, **scalability**, and **modularity**.

## ✨ **Key Features**

### 🏗️ **Modular Architecture**
- **Plugin System**: Pluggable tokenizers, embedders, retrievers, and LLMs
- **Component Registry**: Dynamic component discovery and registration
- **Interface Contracts**: Well-defined APIs for seamless integration

### 🌐 **Multiple API Interfaces**
- **Edge API**: OpenAI-compatible chat completions with streaming
- **MCP API**: Tool-based endpoints for modular operations
- **Web UI**: Modern React-based frontend with agent management

### 🔒 **Enterprise Security**
- **Authentication & Authorization**: JWT-based security with role-based access
- **Threat Detection**: Real-time security monitoring and alerting
- **Input Validation**: Comprehensive input sanitization and validation
- **Audit Logging**: Complete audit trail for compliance

### 📊 **Production Observability**
- **Metrics**: Prometheus metrics collection and monitoring
- **Tracing**: OpenTelemetry distributed tracing
- **Logging**: Structured logging with correlation IDs
- **Health Checks**: Comprehensive health monitoring

### 🚀 **Deployment Ready**
- **Docker Support**: Multi-stage builds with security scanning
- **CI/CD Pipeline**: Automated testing, security scans, and deployment
- **Configuration Management**: Environment-based configuration
- **Scalability**: Async operations and resource optimization
- **Custom metrics**: Extensible framework for domain-specific evaluation
- **Batch processing**: Optimized for large-scale evaluation
- **Benchmarking**: Performance profiling and comparison tools

## Quick Start

### Prerequisites
- Python 3.11 or higher
- Virtual environment (recommended)

### Installation

1. **Create virtual environment**:
```bash
# Linux/Mac
python -m venv .venv
source .venv/bin/activate

# Windows PowerShell
py -3.11 -m venv .venv
.\.venv\Scripts\Activate.ps1
```

2. **Install dependencies**:
```bash
pip install -r requirements.txt
```

3. **Run example workflow**:
```bash
python run.py configs/workflows/example.yaml
```

### Basic Usage

```python
from core.plugins import PluginRegistry
from core.agent import Agent, ReActPlanner
from core.security.middleware import create_security_middleware

# Initialize plugin system
registry = PluginRegistry()
await registry.discover_plugins(Path("plugins/"))

# Setup security middleware
security_middleware, tool_access_middleware = create_security_middleware(
    strict_mode=True,
    rate_limit=100
)

# Create agent with planning
agent = Agent(
    planner=ReActPlanner(),
    tools=["search", "calculator"]
)

# Execute agent task
result = await agent.execute("Research AI safety and summarize findings")
print(f"Result: {result.result}")
```

## 💡 **Usage Examples**

### **Chat Completion (OpenAI-Compatible)**
```python
import httpx

# Non-streaming chat
response = httpx.post(
    "http://localhost:8000/v1/chat/completions",
    json={
        "messages": [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Explain quantum computing"}
        ],
        "model": "gpt-3.5-turbo",
        "stream": False
    }
)
print(response.json())
```

### **Tool-Based Operations (MCP API)**
```python
import httpx

# Text tokenization
response = httpx.post(
    "http://localhost:8001/mcp/tools/tokenize",
    json={"text": "Hello, world! This is a test."}
)
tokens = response.json()["tokens"]
print(f"Tokens: {tokens}")

# Text embedding
response = httpx.post(
    "http://localhost:8001/mcp/tools/embed",
    json={"text": "Machine learning is fascinating"}
)
embedding = response.json()["embedding"]
print(f"Embedding dimension: {len(embedding)}")
```

## 🔧 **Configuration**

### **Environment Variables**

```bash
# Security Configuration
MGAIF_SECURITY_STRICT=true          # Enable strict security mode
MGAIF_RATE_LIMIT=100                # Requests per minute per user

# LLM Integration
MGAIF_EDGE_ADAPTER=echo             # LLM adapter: echo, ollama
MGAIF_OLLAMA_BASE_URL=http://localhost:11434

# Observability
OTEL_EXPORTER_OTLP_ENDPOINT=http://otel-collector:4318
LOG_LEVEL=info                      # Logging level
```

## 🏗️ **Development**

### **Project Structure**
```
mgaif/
├── core/                   # Core framework
│   ├── adapters/          # LLM adapters
│   ├── agent/             # Agent system
│   ├── edge/              # Edge API
│   ├── mcp/               # MCP API
│   ├── rag/               # RAG pipeline
│   ├── security/          # Security middleware
│   └── workflow/          # Workflow engine
├── plugins/               # Plugin ecosystem
│   ├── tokenizers/        # Text tokenization
│   ├── embedders/         # Text embedding
│   └── retrievers/        # Document retrieval
├── webapp/                # React frontend
│   ├── src/components/    # UI components
│   ├── src/pages/         # Application pages
│   └── src/api/           # API client
├── tests/                 # Test suites
├── docs/                  # Documentation
├── deploy/                # Deployment configs
└── scripts/               # Utility scripts
```

### **Development Workflow**

#### **Code Quality Checks**
```bash
# Type checking
mypy core plugins

# Linting
flake8 core plugins --max-line-length=120

# Security scanning
bandit -r core/
```

#### **Testing**
```bash
# Backend tests
pytest tests/ -v --cov=core --cov=plugins

# Frontend tests
cd webapp && npm test
```

## 🐳 **Docker Deployment**

```bash
# Build and run with Docker Compose
cd deploy
docker-compose up -d

# View logs
docker-compose logs -f
```

## 📚 **Documentation**

- **[API Reference](docs/api_reference.md)** - Complete API documentation
- **[Architecture Guide](docs/COMPREHENSIVE_GUIDE.md)** - Detailed technical guide
- **[Code Review Guide](CODE_REVIEW_GUIDE.md)** - Code review checklist

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes and add tests
4. Ensure all tests pass (`pytest`)
5. Submit a pull request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Made with ❤️ by the M-GAIF Team**
