from plugins.tokenizers.simple_tokenizer import SimpleTokenizer
from tests.realistic_data import get_realistic_text


def test_tokenizer_round_trip(benchmarker):
    tok = SimpleTokenizer()
    # Use realistic text for testing
    text = get_realistic_text("medium", 0)
    ids = tok.encode(text)
    assert isinstance(ids, list) and all(isinstance(i, int) for i in ids)
    back = tok.decode(ids)
    # We allow minor spacing differences; ensure token_count stable and words preserved
    assert tok.token_count(text) == len(ids)
    # Check for key terms from realistic AI text
    for word in ["artificial", "intelligence", "advancement", "industries"]:
        assert word.lower() in back.lower()

    metrics = benchmarker(lambda: tok.token_count(text), iterations=200)
    assert metrics["p95_ms"] < 1.5  # very fast
