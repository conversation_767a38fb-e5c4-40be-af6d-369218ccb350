import React, { useEffect, useState } from 'react'
import { Button, Card, Form, Input, Space, Typography, message } from 'antd'
import { api } from '../api/client'

export default function Settings() {
  const [form] = Form.useForm()
  const [base, setBase] = useState<string | undefined>()

  useEffect(() => {
    const stored = localStorage.getItem('mgaif.apiBase') || ''
    setBase(stored || undefined)
    form.setFieldsValue({ apiBase: stored })
  }, [form])

  function onSave(values: any) {
    const val = (values.apiBase || '').trim() || undefined
    setBase(val)
    api.setBase(val)
    if (val) {
      message.success(`API base set to ${val}`)
    } else {
      message.success('API base cleared (using proxy)')
    }
  }

  function onClear() {
    form.setFieldsValue({ apiBase: '' })
    onSave({ apiBase: '' })
  }

  return (
    <Card title="Settings">
      <Space direction="vertical" style={{ width: '100%' }}>
        <Typography.Paragraph>
          Configure API base URL for the backend (e.g., http://127.0.0.1:8000). Leave empty to use the dev proxy.
        </Typography.Paragraph>
        <Form layout="vertical" form={form} onFinish={onSave}>
          <Form.Item label="API Base" name="apiBase">
            <Input placeholder="http://127.0.0.1:8000" />
          </Form.Item>
          <Space>
            <Button type="primary" htmlType="submit">Save</Button>
            <Button onClick={onClear}>Clear</Button>
          </Space>
        </Form>
      </Space>
    </Card>
  )
}
