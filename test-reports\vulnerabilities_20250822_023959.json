{"dependencies": [{"name": "<PERSON><PERSON><PERSON>", "version": "0.112.0", "vulns": []}, {"name": "pydantic", "version": "2.8.2", "vulns": []}, {"name": "u<PERSON><PERSON>", "version": "0.30.5", "vulns": []}, {"name": "numpy", "version": "1.26.4", "vulns": []}, {"name": "httpx", "version": "0.27.0", "vulns": []}, {"name": "tenacity", "version": "8.5.0", "vulns": []}, {"name": "opentelemetry-sdk", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp", "version": "1.25.0", "vulns": []}, {"name": "prometheus-client", "version": "0.20.0", "vulns": []}, {"name": "pytest", "version": "8.3.2", "vulns": []}, {"name": "pytest-asyncio", "version": "0.23.7", "vulns": []}, {"name": "pip-audit", "version": "2.7.3", "vulns": []}, {"name": "pyyaml", "version": "6.0.2", "vulns": []}, {"name": "cyclonedx-bom", "version": "4.4.0", "vulns": []}, {"name": "safety", "version": "3.2.7", "vulns": []}, {"name": "bandit", "version": "1.7.9", "vulns": []}, {"name": "python-multipart", "version": "0.0.6", "vulns": [{"id": "GHSA-2jv5-9r88-3w3p", "fix_versions": ["0.0.7"], "aliases": ["CVE-2024-24762"], "description": "### Summary  When using form data, `python-multipart` uses a Regular Expression to parse the HTTP `Content-Type` header, including options.  An attacker could send a custom-made `Content-Type` option that is very difficult for the RegEx to process, consuming CPU resources and stalling indefinitely (minutes or more) while holding the main event loop. This means that process can't handle any more requests.  This can create a ReDoS (Regular expression Denial of Service): https://owasp.org/www-community/attacks/Regular_expression_Denial_of_Service_-_ReDoS  This only applies when the app uses form data, parsed with `python-multipart`.  ### Details  A regular HTTP `Content-Type` header could look like:  ``` Content-Type: text/html; charset=utf-8 ```  `python-multipart` parses the option with this RegEx: https://github.com/andrew-d/python-multipart/blob/d3d16dae4b061c34fe9d3c9081d9800c49fc1f7a/multipart/multipart.py#L72-L74  A custom option could be made and sent to the server to break it with:  ``` Content-Type: application/x-www-form-urlencoded; !=\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\ ```  ### PoC  Create a simple WSGI application, that just parses the `Content-Type`, and run it with `python main.py`:  ```Python # main.py from wsgiref.simple_server import make_server from wsgiref.validate import validator  from multipart.multipart import parse_options_header   def simple_app(environ, start_response):     _, _ = parse_options_header(environ[\"CONTENT_TYPE\"])      start_response(\"200 OK\", [(\"Content-type\", \"text/plain\")])     return [b\"Ok\"]   httpd = make_server(\"\", 8123, validator(simple_app)) print(\"Serving on port 8123...\") httpd.serve_forever() ```  Then send the attacking request with:  ```console $ curl -v -X 'POST' -H $'Content-Type: application/x-www-form-urlencoded; !=\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\' --data-binary 'input=1' 'http://localhost:8123/' ```  ### Impact  This is a ReDoS, (Regular expression Denial of Service), so it only applies to those using python-multipart to read form data, such as Starlette and FastAPI.  ### Original Report  This was originally reported to FastAPI as an <NAME_EMAIL>, sent via https://huntr.com/, the original reporter is Marcello, https://github.com/byt3bl33d3r  <details> <summary>Original report to FastAPI</summary>  Hey Tiangolo!  My name's Marcello and I work on the ProtectAI/Huntr Threat Research team, a few months ago we got a report (from @nicecatch2000) of a ReDoS affecting another very popular Python web framework. After some internal research, I found that FastAPI is vulnerable to the same ReDoS under certain conditions (only when it parses Form data not JSON).  Here are the details: I'm using the latest version of FastAPI (0.109.0) and the following code:  ```Python from typing import Annotated from fastapi.responses import HTMLResponse from fastapi import FastAPI,Form from pydantic import BaseModel  class Item(BaseModel):     username: str  app = FastAPI()  @app.get(\"/\", response_class=HTMLResponse) async def index():     return HTMLResponse(\"Test\", status_code=200)  @app.post(\"/submit/\") async def submit(username: Annotated[str, Form()]):     return {\"username\": username}  @app.post(\"/submit_json/\") async def submit_json(item: Item):     return {\"username\": item.username} ```  I'm running the above with uvicorn with the following command:  ```console uvicorn server:app ```  Then run the following cUrl command:  ``` curl -v -X 'POST' -H $'Content-Type: application/x-www-form-urlencoded; !=\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\' --data-binary 'input=1' 'http://localhost:8000/submit/' ```  You'll see the server locks up, is unable to serve anymore requests and one CPU core is pegged to 100%  You can even start uvicorn with multiple workers with the --workers 4 argument and as long as you send (workers + 1) requests you'll completely DoS the FastApi server.  If you try submitting Json to the /submit_json endpoint with the malicious Content-Type header you'll see it isn't vulnerable. So this only affects FastAPI when it parses Form data.  Cheers  #### Impact  An attacker is able to cause a DoS on a FastApi server via a malicious Content-Type header if it parses Form data.  #### Occurrences  [params.py L586](https://github.com/tiangolo/fastapi/blob/d74b3b25659b42233a669f032529880de8bd6c2d/fastapi/params.py#L586)  </details>"}, {"id": "GHSA-59g5-xgcq-4qw3", "fix_versions": ["0.0.18"], "aliases": ["CVE-2024-53981"], "description": "### Summary  When parsing form data, `python-multipart` skips line breaks (CR `\\r` or LF `\\n`) in front of the first boundary and any tailing bytes after the last boundary. This happens one byte at a time and emits a log event each time, which may cause excessive logging for certain inputs.  An attacker could abuse this by sending a malicious request with lots of data before the first or after the last boundary, causing high CPU load and stalling the processing thread for a significant amount of time. In case of ASGI application, this could stall the event loop and prevent other requests from being processed, resulting in a denial of service (DoS).  ### Impact  Applications that use `python-multipart` to parse form data (or use frameworks that do so) are affected.   ### Original Report  This security issue was reported by: - GitHub security advisory in Starlette on October 30 by @Startr4ck - Email to `python-multipart` maintainer on October 3 by @mnqazi"}]}, {"name": "pyjwt", "version": "2.8.0", "vulns": []}, {"name": "sphinx", "version": "7.3.7", "vulns": []}, {"name": "myst-parser", "version": "3.0.1", "vulns": []}, {"name": "sphinx-autodoc-typehints", "version": "2.3.0", "vulns": []}, {"name": "sphinx-rtd-theme", "version": "2.0.0", "vulns": []}, {"name": "opentelemetry-api", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp-proto-grpc", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp-proto-common", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp-proto-http", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-proto", "version": "1.25.0", "vulns": []}, {"name": "opentelemetry-semantic-conventions", "version": "0.46b0", "vulns": []}, {"name": "pydantic-core", "version": "2.20.1", "vulns": []}, {"name": "alabaster", "version": "0.7.16", "vulns": []}, {"name": "chardet", "version": "5.2.0", "vulns": []}, {"name": "cyclonedx-python-lib", "version": "7.6.2", "vulns": []}, {"name": "docutils", "version": "0.20.1", "vulns": []}, {"name": "filelock", "version": "3.12.4", "vulns": []}, {"name": "googleapis-common-protos", "version": "1.70.0", "vulns": []}, {"name": "grpcio", "version": "1.74.0", "vulns": []}, {"name": "httpcore", "version": "1.0.9", "vulns": []}, {"name": "importlib-metadata", "version": "7.1.0", "vulns": []}, {"name": "jsonschema", "version": "4.25.1", "vulns": []}, {"name": "license-expression", "version": "30.4.4", "vulns": []}, {"name": "lxml", "version": "5.4.0", "vulns": []}, {"name": "markdown-it-py", "version": "3.0.0", "vulns": []}, {"name": "mdit-py-plugins", "version": "0.5.0", "vulns": []}, {"name": "mdurl", "version": "0.1.2", "vulns": []}, {"name": "packageurl-python", "version": "0.17.5", "vulns": []}, {"name": "packaging", "version": "24.2", "vulns": []}, {"name": "pip-requirements-parser", "version": "32.0.1", "vulns": []}, {"name": "pluggy", "version": "1.6.0", "vulns": []}, {"name": "protobuf", "version": "4.25.8", "vulns": []}, {"name": "psutil", "version": "6.0.0", "vulns": []}, {"name": "py-serializable", "version": "1.1.2", "vulns": []}, {"name": "defusedxml", "version": "0.7.1", "vulns": []}, {"name": "requests", "version": "2.32.5", "vulns": []}, {"name": "charset-normalizer", "version": "3.4.3", "vulns": []}, {"name": "idna", "version": "3.10", "vulns": []}, {"name": "sortedcontainers", "version": "2.4.0", "vulns": []}, {"name": "sphinxcontrib-j<PERSON>y", "version": "4.1", "vulns": []}, {"name": "starlette", "version": "0.37.2", "vulns": [{"id": "GHSA-f96h-pmfr-66vw", "fix_versions": ["0.40.0"], "aliases": ["CVE-2024-47874"], "description": "### Summary <PERSON>lette treats `multipart/form-data` parts without a `filename` as text form fields and buffers those in byte strings with no size limit. This allows an attacker to upload arbitrary large form fields and cause Starlette to both slow down significantly due to excessive memory allocations and copy operations, and also consume more and more memory until the server starts swapping and grinds to a halt, or the OS terminates the server process with an OOM error. Uploading multiple such requests in parallel may be enough to render a service practically unusable, even if reasonable request size limits are enforced by a reverse proxy in front of Starlette.  ### PoC  ```python from starlette.applications import Starlette from starlette.routing import Route  async def poc(request):     async with request.form():         pass  app = Starlette(routes=[     Route('/', poc, methods=[\"POST\"]), ]) ```  ```sh curl http://localhost:8000 -F 'big=</dev/urandom' ```  ### Impact This Denial of service (DoS) vulnerability affects all applications built with Starlette (or FastAPI) accepting form requests. "}, {"id": "GHSA-2c2j-9gv5-cj73", "fix_versions": ["0.47.2"], "aliases": ["CVE-2025-54121"], "description": "### Summary When parsing a multi-part form with large files (greater than the [default max spool size](https://github.com/encode/starlette/blob/fa5355442753f794965ae1af0f87f9fec1b9a3de/starlette/formparsers.py#L126)) `starlette` will block the main thread to roll the file over to disk. This blocks the event thread which means we can't accept new connections.  ### Details Please see this discussion for details: https://github.com/encode/starlette/discussions/2927#discussioncomment-********. In summary the following UploadFile code (copied from [here](https://github.com/encode/starlette/blob/fa5355442753f794965ae1af0f87f9fec1b9a3de/starlette/datastructures.py#L436C5-L447C14)) has a minor bug. Instead of just checking for `self._in_memory` we should also check if the additional bytes will cause a rollover.  ```python      @property     def _in_memory(self) -> bool:         # check for SpooledTemporaryFile._rolled         rolled_to_disk = getattr(self.file, \"_rolled\", True)         return not rolled_to_disk      async def write(self, data: bytes) -> None:         if self.size is not None:             self.size += len(data)          if self._in_memory:             self.file.write(data)         else:             await run_in_threadpool(self.file.write, data) ```  I have already created a PR which fixes the problem: https://github.com/encode/starlette/pull/2962   ### PoC See the discussion [here](https://github.com/encode/starlette/discussions/2927#discussioncomment-********) for steps on how to reproduce.  ### Impact To be honest, very low and not many users will be impacted. Parsing large forms is already CPU intensive so the additional IO block doesn't slow down `starlette` that much on systems with modern HDDs/SSDs. If someone is running on tape they might see a greater impact."}]}, {"name": "anyio", "version": "4.10.0", "vulns": []}, {"name": "to<PERSON>li", "version": "2.2.1", "vulns": []}, {"name": "urllib3", "version": "2.5.0", "vulns": []}, {"name": "annotated-types", "version": "0.7.0", "vulns": []}, {"name": "attrs", "version": "25.3.0", "vulns": []}, {"name": "authlib", "version": "1.6.1", "vulns": []}, {"name": "babel", "version": "2.17.0", "vulns": []}, {"name": "boolean-py", "version": "5.0", "vulns": []}, {"name": "cachecontrol", "version": "0.14.3", "vulns": []}, {"name": "msgpack", "version": "1.1.1", "vulns": []}, {"name": "certifi", "version": "2025.8.3", "vulns": []}, {"name": "click", "version": "8.2.1", "vulns": []}, {"name": "colorama", "version": "0.4.6", "vulns": []}, {"name": "deprecated", "version": "1.2.18", "vulns": []}, {"name": "wrapt", "version": "1.17.3", "vulns": []}, {"name": "dparse", "version": "0.6.4", "vulns": []}, {"name": "exceptiongroup", "version": "1.3.0", "vulns": []}, {"name": "h11", "version": "0.16.0", "vulns": []}, {"name": "html5lib", "version": "1.1", "vulns": []}, {"name": "httptools", "version": "0.6.4", "vulns": []}, {"name": "imagesize", "version": "1.4.1", "vulns": []}, {"name": "jinja2", "version": "3.1.6", "vulns": []}, {"name": "<PERSON><PERSON><PERSON>er", "version": "3.0.0", "vulns": []}, {"name": "jsonschema-specifications", "version": "2025.4.1", "vulns": []}, {"name": "markupsafe", "version": "3.0.2", "vulns": []}, {"name": "marshmallow", "version": "4.0.0", "vulns": []}, {"name": "pip-api", "version": "0.0.34", "vulns": []}, {"name": "pygments", "version": "2.19.2", "vulns": []}, {"name": "python-dotenv", "version": "1.1.1", "vulns": []}, {"name": "referencing", "version": "0.36.2", "vulns": []}, {"name": "rich", "version": "14.1.0", "vulns": []}, {"name": "rpds-py", "version": "0.27.0", "vulns": []}, {"name": "ruamel-yaml", "version": "0.18.15", "vulns": []}, {"name": "ruamel-yaml-clib", "version": "0.2.12", "vulns": []}, {"name": "safety-schemas", "version": "0.0.14", "vulns": []}, {"name": "six", "version": "1.17.0", "vulns": []}, {"name": "sniffio", "version": "1.3.1", "vulns": []}, {"name": "snowballstemmer", "version": "3.0.1", "vulns": []}, {"name": "sphinxcontrib-htmlhelp", "version": "2.1.0", "vulns": []}, {"name": "sphinxcontrib-serializinghtml", "version": "2.0.0", "vulns": []}, {"name": "s<PERSON><PERSON><PERSON>", "version": "5.4.1", "vulns": []}, {"name": "pbr", "version": "7.0.1", "vulns": []}, {"name": "toml", "version": "0.10.2", "vulns": []}, {"name": "typing-extensions", "version": "4.14.1", "vulns": []}, {"name": "watchfiles", "version": "1.1.0", "vulns": []}, {"name": "webcolors", "version": "24.11.1", "vulns": []}, {"name": "websockets", "version": "15.0.1", "vulns": []}, {"name": "zipp", "version": "3.23.0", "vulns": []}, {"name": "backports-datetime-fromisoformat", "version": "2.0.3", "vulns": []}, {"name": "cryptography", "version": "45.0.6", "vulns": []}, {"name": "cffi", "version": "1.17.1", "vulns": []}, {"name": "fqdn", "version": "1.5.1", "vulns": []}, {"name": "iniconfig", "version": "2.1.0", "vulns": []}, {"name": "isoduration", "version": "20.11.0", "vulns": []}, {"name": "arrow", "version": "1.3.0", "vulns": []}, {"name": "python-dateutil", "version": "2.9.0.post0", "vulns": []}, {"name": "types-python-dateutil", "version": "2.9.0.20250822", "vulns": []}, {"name": "pyc<PERSON><PERSON>", "version": "2.22", "vulns": []}, {"name": "pyparsing", "version": "3.2.3", "vulns": []}, {"name": "rfc3339-validator", "version": "0.1.4", "vulns": []}, {"name": "rfc3987", "version": "1.3.8", "vulns": []}, {"name": "sphinxcontrib-applehelp", "version": "2.0.0", "vulns": []}, {"name": "sphinxcontrib-devhelp", "version": "2.0.0", "vulns": []}, {"name": "sphinxcontrib-j<PERSON>th", "version": "1.0.1", "vulns": []}, {"name": "sphinxcontrib-qthelp", "version": "2.0.0", "vulns": []}, {"name": "typer", "version": "0.16.1", "vulns": []}, {"name": "shellingham", "version": "1.5.4", "vulns": []}, {"name": "uri-template", "version": "1.3.0", "vulns": []}, {"name": "webencodings", "version": "0.5.1", "vulns": []}], "fixes": []}