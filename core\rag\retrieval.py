"""Retrieval systems for RAG pipeline.

This module implements various retrieval strategies for the RAG system,
including vector-based, keyword-based, and hybrid approaches.

The retrieval system provides:
- Multiple retrieval strategies (vector, keyword, hybrid)
- Configurable similarity metrics and thresholds
- Batch retrieval for efficiency
- Result ranking and filtering
- Integration with vector stores and text search

Key Components:
- Retriever: Base retrieval interface
- VectorRetriever: Dense vector similarity search
- KeywordRetriever: Sparse keyword-based search (BM25)
- HybridRetriever: Combines vector and keyword approaches

Example:
    >>> from core.rag import HybridRetriever, Document
    >>> retriever = HybridRetriever(
    ...     vector_weight=0.7,
    ...     keyword_weight=0.3
    ... )
    >>> documents = [Document(content="AI is transforming industries")]
    >>> await retriever.index_documents(documents)
    >>> results = await retriever.retrieve("artificial intelligence", top_k=5)
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass, field
import numpy as np
from datetime import datetime

from .base import Document, Chunk, RAGResult, RAGError

logger = logging.getLogger(__name__)


@dataclass
class RetrievalResult:
    """Result from a retrieval operation."""
    chunk: Chunk
    score: float
    metadata: Dict[str, Any] = field(default_factory=dict)
    retrieval_method: str = "unknown"
    timestamp: datetime = field(default_factory=datetime.now)


class Retriever(ABC):
    """Base class for all retrieval systems."""
    
    def __init__(self, top_k: int = 10, score_threshold: float = 0.0):
        self.top_k = top_k
        self.score_threshold = score_threshold
        self.indexed_chunks: List[Chunk] = []
        self.metadata: Dict[str, Any] = {}
    
    @abstractmethod
    async def index_documents(self, documents: List[Document]) -> None:
        """Index documents for retrieval."""
        pass
    
    @abstractmethod
    async def retrieve(self, query: str, top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Retrieve relevant chunks for a query."""
        pass
    
    async def batch_retrieve(self, queries: List[str], top_k: Optional[int] = None) -> List[List[RetrievalResult]]:
        """Retrieve results for multiple queries."""
        tasks = [self.retrieve(query, top_k) for query in queries]
        return await asyncio.gather(*tasks)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get retrieval system statistics."""
        return {
            "indexed_chunks": len(self.indexed_chunks),
            "top_k": self.top_k,
            "score_threshold": self.score_threshold,
            "metadata": self.metadata
        }


class VectorRetriever(Retriever):
    """Dense vector similarity retrieval using embeddings."""
    
    def __init__(self, embedding_model: str = "text-embedding-ada-002", 
                 similarity_metric: str = "cosine", **kwargs):
        super().__init__(**kwargs)
        self.embedding_model = embedding_model
        self.similarity_metric = similarity_metric
        self.chunk_embeddings: Optional[np.ndarray] = None
        self.embedder = None
    
    async def _get_embedder(self):
        """Lazy load embedder to avoid circular imports."""
        if self.embedder is None:
            from core.text.embeddings import get_embedder
            self.embedder = get_embedder(self.embedding_model)
        return self.embedder
    
    async def index_documents(self, documents: List[Document]) -> None:
        """Index documents by computing embeddings for all chunks."""
        logger.info(f"Indexing {len(documents)} documents for vector retrieval")
        
        # Extract all chunks from documents
        self.indexed_chunks = []
        for doc in documents:
            if hasattr(doc, 'chunks') and doc.chunks:
                self.indexed_chunks.extend(doc.chunks)
            else:
                # Create a single chunk from the document
                chunk = Chunk(
                    id=f"{doc.id}_chunk_0",
                    content=doc.content,
                    document_id=doc.id,
                    metadata=doc.metadata
                )
                self.indexed_chunks.append(chunk)
        
        if not self.indexed_chunks:
            logger.warning("No chunks to index")
            return
        
        # Compute embeddings for all chunks
        embedder = await self._get_embedder()
        chunk_texts = [chunk.content for chunk in self.indexed_chunks]
        
        try:
            embeddings = await embedder.embed_batch(chunk_texts)
            self.chunk_embeddings = np.array(embeddings)
            logger.info(f"Computed embeddings for {len(self.indexed_chunks)} chunks")
        except Exception as e:
            logger.error(f"Failed to compute embeddings: {e}")
            raise RAGError(f"Embedding computation failed: {e}")
    
    async def retrieve(self, query: str, top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Retrieve chunks using vector similarity."""
        if not self.indexed_chunks or self.chunk_embeddings is None:
            logger.warning("No indexed chunks available for retrieval")
            return []
        
        top_k = top_k or self.top_k
        
        try:
            # Compute query embedding
            embedder = await self._get_embedder()
            query_embedding = await embedder.embed(query)
            query_vector = np.array(query_embedding).reshape(1, -1)
            
            # Compute similarities
            if self.similarity_metric == "cosine":
                # Normalize vectors for cosine similarity
                query_norm = query_vector / np.linalg.norm(query_vector)
                chunk_norms = self.chunk_embeddings / np.linalg.norm(self.chunk_embeddings, axis=1, keepdims=True)
                similarities = np.dot(chunk_norms, query_norm.T).flatten()
            elif self.similarity_metric == "dot":
                similarities = np.dot(self.chunk_embeddings, query_vector.T).flatten()
            else:
                raise RAGError(f"Unsupported similarity metric: {self.similarity_metric}")
            
            # Get top-k results above threshold
            valid_indices = similarities >= self.score_threshold
            valid_similarities = similarities[valid_indices]
            valid_chunks = [self.indexed_chunks[i] for i in range(len(self.indexed_chunks)) if valid_indices[i]]
            
            # Sort by similarity (descending)
            sorted_pairs = sorted(zip(valid_similarities, valid_chunks), key=lambda x: x[0], reverse=True)
            top_pairs = sorted_pairs[:top_k]
            
            # Create retrieval results
            results = []
            for score, chunk in top_pairs:
                result = RetrievalResult(
                    chunk=chunk,
                    score=float(score),
                    retrieval_method="vector",
                    metadata={
                        "similarity_metric": self.similarity_metric,
                        "embedding_model": self.embedding_model
                    }
                )
                results.append(result)
            
            logger.debug(f"Retrieved {len(results)} chunks for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Vector retrieval failed: {e}")
            raise RAGError(f"Vector retrieval failed: {e}")


class KeywordRetriever(Retriever):
    """Keyword-based retrieval using BM25 algorithm."""
    
    def __init__(self, k1: float = 1.2, b: float = 0.75, **kwargs):
        super().__init__(**kwargs)
        self.k1 = k1  # Term frequency saturation parameter
        self.b = b    # Length normalization parameter
        self.term_frequencies: Dict[str, Dict[int, int]] = {}
        self.document_frequencies: Dict[str, int] = {}
        self.document_lengths: List[int] = []
        self.avg_doc_length: float = 0.0
        self.vocabulary: set = set()
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization (can be enhanced with proper tokenizer)."""
        import re
        # Convert to lowercase and split on non-alphanumeric characters
        tokens = re.findall(r'\b\w+\b', text.lower())
        return tokens
    
    async def index_documents(self, documents: List[Document]) -> None:
        """Index documents for BM25 retrieval."""
        logger.info(f"Indexing {len(documents)} documents for keyword retrieval")
        
        # Extract all chunks from documents
        self.indexed_chunks = []
        for doc in documents:
            if hasattr(doc, 'chunks') and doc.chunks:
                self.indexed_chunks.extend(doc.chunks)
            else:
                # Create a single chunk from the document
                chunk = Chunk(
                    id=f"{doc.id}_chunk_0",
                    content=doc.content,
                    document_id=doc.id,
                    metadata=doc.metadata
                )
                self.indexed_chunks.append(chunk)
        
        if not self.indexed_chunks:
            logger.warning("No chunks to index")
            return
        
        # Build BM25 index
        self.term_frequencies = {}
        self.document_frequencies = {}
        self.document_lengths = []
        self.vocabulary = set()
        
        # First pass: collect term frequencies and document lengths
        for doc_idx, chunk in enumerate(self.indexed_chunks):
            tokens = self._tokenize(chunk.content)
            self.document_lengths.append(len(tokens))
            
            # Count term frequencies in this document
            term_freq = {}
            for token in tokens:
                self.vocabulary.add(token)
                term_freq[token] = term_freq.get(token, 0) + 1
            
            # Store term frequencies for this document
            for term, freq in term_freq.items():
                if term not in self.term_frequencies:
                    self.term_frequencies[term] = {}
                self.term_frequencies[term][doc_idx] = freq
        
        # Second pass: compute document frequencies
        for term in self.vocabulary:
            self.document_frequencies[term] = len(self.term_frequencies.get(term, {}))
        
        # Compute average document length
        self.avg_doc_length = sum(self.document_lengths) / len(self.document_lengths) if self.document_lengths else 0
        
        logger.info(f"Built BM25 index with {len(self.vocabulary)} unique terms")
    
    def _compute_bm25_score(self, query_terms: List[str], doc_idx: int) -> float:
        """Compute BM25 score for a document given query terms."""
        score = 0.0
        doc_length = self.document_lengths[doc_idx]
        
        for term in query_terms:
            if term not in self.term_frequencies:
                continue
            
            # Term frequency in document
            tf = self.term_frequencies[term].get(doc_idx, 0)
            if tf == 0:
                continue
            
            # Document frequency
            df = self.document_frequencies[term]
            
            # IDF component
            idf = np.log((len(self.indexed_chunks) - df + 0.5) / (df + 0.5))
            
            # BM25 formula
            numerator = tf * (self.k1 + 1)
            denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / self.avg_doc_length))
            
            score += idf * (numerator / denominator)
        
        return score
    
    async def retrieve(self, query: str, top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Retrieve chunks using BM25 keyword matching."""
        if not self.indexed_chunks:
            logger.warning("No indexed chunks available for retrieval")
            return []
        
        top_k = top_k or self.top_k
        query_terms = self._tokenize(query)
        
        if not query_terms:
            logger.warning("No valid query terms found")
            return []
        
        try:
            # Compute BM25 scores for all documents
            scores = []
            for doc_idx in range(len(self.indexed_chunks)):
                score = self._compute_bm25_score(query_terms, doc_idx)
                if score >= self.score_threshold:
                    scores.append((score, doc_idx))
            
            # Sort by score (descending) and take top-k
            scores.sort(key=lambda x: x[0], reverse=True)
            top_scores = scores[:top_k]
            
            # Create retrieval results
            results = []
            for score, doc_idx in top_scores:
                chunk = self.indexed_chunks[doc_idx]
                result = RetrievalResult(
                    chunk=chunk,
                    score=score,
                    retrieval_method="keyword",
                    metadata={
                        "bm25_k1": self.k1,
                        "bm25_b": self.b,
                        "query_terms": query_terms
                    }
                )
                results.append(result)
            
            logger.debug(f"Retrieved {len(results)} chunks for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Keyword retrieval failed: {e}")
            raise RAGError(f"Keyword retrieval failed: {e}")


class HybridRetriever(Retriever):
    """Hybrid retrieval combining vector and keyword approaches."""
    
    def __init__(self, vector_weight: float = 0.7, keyword_weight: float = 0.3, 
                 vector_config: Optional[Dict[str, Any]] = None,
                 keyword_config: Optional[Dict[str, Any]] = None, **kwargs):
        super().__init__(**kwargs)
        self.vector_weight = vector_weight
        self.keyword_weight = keyword_weight
        
        # Initialize sub-retrievers
        vector_config = vector_config or {}
        keyword_config = keyword_config or {}
        
        self.vector_retriever = VectorRetriever(
            top_k=kwargs.get('top_k', 10) * 2,  # Retrieve more for fusion
            score_threshold=0.0,  # Let hybrid scoring handle thresholds
            **vector_config
        )
        
        self.keyword_retriever = KeywordRetriever(
            top_k=kwargs.get('top_k', 10) * 2,  # Retrieve more for fusion
            score_threshold=0.0,  # Let hybrid scoring handle thresholds
            **keyword_config
        )
    
    async def index_documents(self, documents: List[Document]) -> None:
        """Index documents in both vector and keyword retrievers."""
        logger.info(f"Indexing {len(documents)} documents for hybrid retrieval")
        
        # Index in both retrievers
        await asyncio.gather(
            self.vector_retriever.index_documents(documents),
            self.keyword_retriever.index_documents(documents)
        )
        
        # Store chunks from vector retriever (they should be the same)
        self.indexed_chunks = self.vector_retriever.indexed_chunks
        
        logger.info("Hybrid indexing completed")
    
    def _normalize_scores(self, results: List[RetrievalResult]) -> List[RetrievalResult]:
        """Normalize scores to [0, 1] range using min-max normalization."""
        if not results:
            return results
        
        scores = [r.score for r in results]
        min_score = min(scores)
        max_score = max(scores)
        
        if max_score == min_score:
            # All scores are the same, set to 1.0
            for result in results:
                result.score = 1.0
        else:
            # Min-max normalization
            for result in results:
                result.score = (result.score - min_score) / (max_score - min_score)
        
        return results
    
    async def retrieve(self, query: str, top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Retrieve chunks using hybrid vector + keyword approach."""
        if not self.indexed_chunks:
            logger.warning("No indexed chunks available for retrieval")
            return []
        
        top_k = top_k or self.top_k
        
        try:
            # Get results from both retrievers
            vector_results, keyword_results = await asyncio.gather(
                self.vector_retriever.retrieve(query, top_k * 2),
                self.keyword_retriever.retrieve(query, top_k * 2)
            )
            
            # Normalize scores
            vector_results = self._normalize_scores(vector_results)
            keyword_results = self._normalize_scores(keyword_results)
            
            # Create combined results with hybrid scoring
            chunk_scores: Dict[str, Tuple[float, Chunk, Dict[str, Any]]] = {}
            
            # Add vector results
            for result in vector_results:
                chunk_id = result.chunk.id
                hybrid_score = result.score * self.vector_weight
                metadata = {
                    "vector_score": result.score,
                    "vector_weight": self.vector_weight,
                    **result.metadata
                }
                chunk_scores[chunk_id] = (hybrid_score, result.chunk, metadata)
            
            # Add keyword results (combine scores if chunk already exists)
            for result in keyword_results:
                chunk_id = result.chunk.id
                keyword_contribution = result.score * self.keyword_weight
                
                if chunk_id in chunk_scores:
                    # Combine scores
                    existing_score, chunk, existing_metadata = chunk_scores[chunk_id]
                    combined_score = existing_score + keyword_contribution
                    combined_metadata = {
                        **existing_metadata,
                        "keyword_score": result.score,
                        "keyword_weight": self.keyword_weight,
                        "combined_score": combined_score
                    }
                    chunk_scores[chunk_id] = (combined_score, chunk, combined_metadata)
                else:
                    # New chunk from keyword only
                    metadata = {
                        "keyword_score": result.score,
                        "keyword_weight": self.keyword_weight,
                        "vector_score": 0.0,
                        "vector_weight": self.vector_weight,
                        **result.metadata
                    }
                    chunk_scores[chunk_id] = (keyword_contribution, result.chunk, metadata)
            
            # Sort by combined score and filter by threshold
            sorted_results = sorted(chunk_scores.values(), key=lambda x: x[0], reverse=True)
            filtered_results = [(score, chunk, metadata) for score, chunk, metadata in sorted_results 
                              if score >= self.score_threshold]
            
            # Create final results
            results = []
            for score, chunk, metadata in filtered_results[:top_k]:
                result = RetrievalResult(
                    chunk=chunk,
                    score=score,
                    retrieval_method="hybrid",
                    metadata=metadata
                )
                results.append(result)
            
            logger.debug(f"Retrieved {len(results)} chunks using hybrid approach for query: {query[:50]}...")
            return results
            
        except Exception as e:
            logger.error(f"Hybrid retrieval failed: {e}")
            raise RAGError(f"Hybrid retrieval failed: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get hybrid retrieval system statistics."""
        base_stats = super().get_stats()
        return {
            **base_stats,
            "vector_weight": self.vector_weight,
            "keyword_weight": self.keyword_weight,
            "vector_stats": self.vector_retriever.get_stats(),
            "keyword_stats": self.keyword_retriever.get_stats()
        }
