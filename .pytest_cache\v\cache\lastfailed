{"tests/test_security_auth.py::test_auth_system_integration": true, "tests/test_mcp_placeholder.py::test_mcp_end_to_end_integration": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_create_user": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_valid_user": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_invalid_password": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_nonexistent_user": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_create_token": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_validate_token": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_validate_invalid_token": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_password_hashing": true, "tests/test_security_auth.py::TestJWTAuthProvider::test_role_permissions": true, "tests/test_security_auth.py::TestAuthManager::test_authenticate_user": true, "tests/test_security_auth.py::TestAuthManager::test_validate_token": true, "tests/test_security_auth.py::TestAuthManager::test_authorize_action": true, "tests/test_security_auth.py::TestAuthManager::test_logout_user": true, "tests/test_security_auth.py::TestAuthManager::test_get_active_sessions": true, "tests/test_security_auth.py::TestUser::test_user_creation": true, "tests/test_security_auth.py::TestUser::test_user_permissions": true, "tests/test_security_auth.py::TestAuthToken::test_token_creation": true, "tests/test_security_auth.py::TestAuthToken::test_token_expiration": true, "tests/test_security_auth.py::TestAuthToken::test_token_scopes": true, "tests/test_agent_system.py::TestPlanning::test_plan_validation": true, "tests/test_agent_system.py::TestAgentCoordination::test_agent_registration": true, "tests/test_agent_system.py::TestAgentCoordination::test_sequential_coordination": true, "tests/test_agent_system.py::TestAgentCoordination::test_parallel_coordination": true, "tests/test_agent_system.py::TestAgentCoordination::test_coordination_with_failure": true, "tests/test_agent_system.py::test_agent_system_integration": true, "tests/test_agent_system.py": true}