["tests/test_agent_system.py::TestAgentCoordination::test_agent_registration", "tests/test_agent_system.py::TestAgentCoordination::test_coordination_with_failure", "tests/test_agent_system.py::TestAgentCoordination::test_parallel_coordination", "tests/test_agent_system.py::TestAgentCoordination::test_sequential_coordination", "tests/test_agent_system.py::TestEpisodicMemory::test_memory_consolidation", "tests/test_agent_system.py::TestEpisodicMemory::test_memory_decay", "tests/test_agent_system.py::TestEpisodicMemory::test_retrieve_memories", "tests/test_agent_system.py::TestEpisodicMemory::test_store_memory", "tests/test_agent_system.py::TestPlanning::test_cot_planner", "tests/test_agent_system.py::TestPlanning::test_plan_validation", "tests/test_agent_system.py::TestPlanning::test_react_planner", "tests/test_agent_system.py::TestSemanticMemory::test_remove_fact", "tests/test_agent_system.py::TestSemanticMemory::test_retrieve_fact", "tests/test_agent_system.py::TestSemanticMemory::test_store_fact", "tests/test_agent_system.py::TestSemanticMemory::test_update_fact", "tests/test_agent_system.py::TestToolOrchestrator::test_parallel_tool_execution", "tests/test_agent_system.py::TestToolOrchestrator::test_tool_execution", "tests/test_agent_system.py::TestToolOrchestrator::test_tool_failure_handling", "tests/test_agent_system.py::TestToolOrchestrator::test_tool_registration", "tests/test_agent_system.py::TestToolOrchestrator::test_tool_selection", "tests/test_agent_system.py::test_agent_system_integration", "tests/test_basic_functionality.py::TestClass::test_class_method", "tests/test_basic_functionality.py::TestClass::test_class_setup", "tests/test_basic_functionality.py::test_async_function", "tests/test_basic_functionality.py::test_basic_math", "tests/test_basic_functionality.py::test_boolean_operations", "tests/test_basic_functionality.py::test_datetime_operations", "tests/test_basic_functionality.py::test_dict_operations", "tests/test_basic_functionality.py::test_exception_handling", "tests/test_basic_functionality.py::test_fixture_usage", "tests/test_basic_functionality.py::test_list_operations", "tests/test_basic_functionality.py::test_parametrized[1-2]", "tests/test_basic_functionality.py::test_parametrized[2-4]", "tests/test_basic_functionality.py::test_parametrized[3-6]", "tests/test_basic_functionality.py::test_parametrized[4-8]", "tests/test_basic_functionality.py::test_string_operations", "tests/test_basic_functionality.py::test_type_checking", "tests/test_bench_thresholds.py::test_benchmarks_meet_thresholds", "tests/test_edge_api.py::test_chat_completions_non_stream", "tests/test_edge_api.py::test_chat_completions_stream_sse", "tests/test_edge_api.py::test_chat_completions_throughput_concurrency", "tests/test_embedder.py::test_embedder_determinism_and_shape", "tests/test_evaluation_basic.py::TestAccuracyMetricBasic::test_accuracy_metric_creation", "tests/test_evaluation_basic.py::TestAccuracyMetricBasic::test_accuracy_partial_match", "tests/test_evaluation_basic.py::TestAccuracyMetricBasic::test_accuracy_perfect_match", "tests/test_evaluation_basic.py::TestBERTScoreMetricBasic::test_bert_score_computation", "tests/test_evaluation_basic.py::TestBERTScoreMetricBasic::test_bert_score_creation", "tests/test_evaluation_basic.py::TestBLEUMetricBasic::test_bleu_metric_creation", "tests/test_evaluation_basic.py::TestBLEUMetricBasic::test_bleu_no_match", "tests/test_evaluation_basic.py::TestBLEUMetricBasic::test_bleu_perfect_match", "tests/test_evaluation_basic.py::TestEvaluationError::test_evaluation_error_creation", "tests/test_evaluation_basic.py::TestEvaluationError::test_evaluation_error_minimal", "tests/test_evaluation_basic.py::TestEvaluationResult::test_evaluation_result_creation", "tests/test_evaluation_basic.py::TestEvaluationResult::test_evaluation_result_to_dict", "tests/test_evaluation_basic.py::TestMetricBase::test_mock_metric_computation", "tests/test_evaluation_basic.py::TestMetricBase::test_mock_metric_creation", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_consistency[AccuracyMetric-kwargs2]", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_consistency[BERTScoreMetric-kwargs3]", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_consistency[BLEUMetric-kwargs0]", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_consistency[ROUGEMetric-kwargs1]", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_empty_inputs", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_error_handling", "tests/test_evaluation_basic.py::TestMetricIntegration::test_metric_result_structure", "tests/test_evaluation_basic.py::TestROUGEMetricBasic::test_rouge_metric_creation", "tests/test_evaluation_basic.py::TestROUGEMetricBasic::test_rouge_perfect_match", "tests/test_evaluation_basic.py::test_basic_async_functionality", "tests/test_evaluation_framework.py::TestCostTracker::test_cost_report_generation", "tests/test_evaluation_framework.py::TestCostTracker::test_cost_reset", "tests/test_evaluation_framework.py::TestCostTracker::test_cost_tracker_creation", "tests/test_evaluation_framework.py::TestCostTracker::test_multiple_cost_tracking", "tests/test_evaluation_framework.py::TestCostTracker::test_track_custom_cost", "tests/test_evaluation_framework.py::TestCostTracker::test_track_embedding_cost", "tests/test_evaluation_framework.py::TestCostTracker::test_track_llm_cost", "tests/test_evaluation_framework.py::TestEvaluationIntegration::test_async_evaluation", "tests/test_evaluation_framework.py::TestEvaluationIntegration::test_evaluation_config_validation", "tests/test_evaluation_framework.py::TestEvaluationIntegration::test_evaluator_with_cost_tracking", "tests/test_evaluation_framework.py::TestEvaluationIntegration::test_evaluator_with_performance_profiling", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_context_manager_timing", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_manual_timing", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_multiple_measurements", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_performance_report_generation", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_profiler_creation", "tests/test_evaluation_framework.py::TestPerformanceProfiler::test_profiler_statistics", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_case_sensitivity", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_metric_creation", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_mismatched_lengths", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_no_match", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_partial_match", "tests/test_evaluation_metrics.py::TestAccuracyMetric::test_accuracy_perfect_match", "tests/test_evaluation_metrics.py::TestBERTScoreMetric::test_bert_score_computation", "tests/test_evaluation_metrics.py::TestBERTScoreMetric::test_bert_score_creation", "tests/test_evaluation_metrics.py::TestBERTScoreMetric::test_bert_score_identical_texts", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_empty_prediction", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_metric_creation", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_multiple_predictions", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_no_match", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_partial_match", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_perfect_match", "tests/test_evaluation_metrics.py::TestBLEUMetric::test_bleu_smoothing", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_consistency[AccuracyMetric-kwargs2]", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_consistency[BERTScoreMetric-kwargs3]", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_consistency[BLEUMetric-kwargs0]", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_consistency[ROUGEMetric-kwargs1]", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_empty_inputs", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_error_handling", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_performance", "tests/test_evaluation_metrics.py::TestMetricIntegration::test_metric_result_structure", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_1_partial_match", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_1_perfect_match", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_2_bigrams", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_l_lcs", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_metric_creation", "tests/test_evaluation_metrics.py::TestROUGEMetric::test_rouge_multiple_predictions", "tests/test_evaluation_metrics.py::test_async_metric_computation", "tests/test_import_policy.py::test_core_contracts_importable", "tests/test_import_policy.py::test_module_structure", "tests/test_import_policy.py::test_no_circular_imports", "tests/test_import_policy.py::test_no_print_statements", "tests/test_import_policy.py::test_no_star_imports", "tests/test_import_policy.py::test_plugins_can_import_contracts", "tests/test_llm_adapter_echo.py::test_echo_adapter_chat_and_stream", "tests/test_mcp_api.py::test_mcp_embed", "tests/test_mcp_api.py::test_mcp_llm_chat", "tests/test_mcp_api.py::test_mcp_retriever_index_search", "tests/test_mcp_api.py::test_mcp_tokenize", "tests/test_mcp_api.py::test_mcp_workflow_run", "tests/test_mcp_placeholder.py::test_mcp_end_to_end_integration", "tests/test_ollama_adapter.py::test_ollama_chat_basic", "tests/test_ollama_adapter.py::test_ollama_chat_stream", "tests/test_plugin_system.py::TestComponentManager::test_backup_component", "tests/test_plugin_system.py::TestComponentManager::test_canary_deployment", "tests/test_plugin_system.py::TestComponentManager::test_component_metrics", "tests/test_plugin_system.py::TestComponentManager::test_health_monitoring", "tests/test_plugin_system.py::TestPluginBase::test_plugin_health_check_failure", "tests/test_plugin_system.py::TestPluginBase::test_plugin_initialization_failure", "tests/test_plugin_system.py::TestPluginBase::test_plugin_lifecycle", "tests/test_plugin_system.py::TestPluginBase::test_plugin_metrics", "tests/test_plugin_system.py::TestPluginFactory::test_configurable_factory", "tests/test_plugin_system.py::TestPluginFactory::test_factory_validation", "tests/test_plugin_system.py::TestPluginFactory::test_reflection_factory", "tests/test_plugin_system.py::TestPluginRegistry::test_hot_swap", "tests/test_plugin_system.py::TestPluginRegistry::test_plugin_discovery", "tests/test_plugin_system.py::TestPluginRegistry::test_plugin_registration", "tests/test_plugin_system.py::TestPluginRegistry::test_plugin_unregistration", "tests/test_plugin_system.py::test_plugin_system_integration", "tests/test_rag_base.py::TestChunk::test_chunk_char_count", "tests/test_rag_base.py::TestChunk::test_chunk_creation", "tests/test_rag_base.py::TestChunk::test_chunk_from_document", "tests/test_rag_base.py::TestChunk::test_chunk_to_dict", "tests/test_rag_base.py::TestChunk::test_chunk_word_count", "tests/test_rag_base.py::TestDocument::test_document_char_count", "tests/test_rag_base.py::TestDocument::test_document_creation", "tests/test_rag_base.py::TestDocument::test_document_defaults", "tests/test_rag_base.py::TestDocument::test_document_empty_content", "tests/test_rag_base.py::TestDocument::test_document_from_dict", "tests/test_rag_base.py::TestDocument::test_document_to_dict", "tests/test_rag_base.py::TestDocument::test_document_with_source", "tests/test_rag_base.py::TestDocument::test_document_word_count", "tests/test_rag_base.py::TestRAGExceptions::test_chunking_error", "tests/test_rag_base.py::TestRAGExceptions::test_rag_error", "tests/test_rag_base.py::TestRAGExceptions::test_retrieval_error", "tests/test_rag_base.py::TestRAGResult::test_rag_result_creation", "tests/test_rag_base.py::TestRAGResult::test_rag_result_to_dict", "tests/test_rag_base.py::TestRetrievalResult::test_retrieval_result_creation", "tests/test_rag_base.py::TestRetrievalResult::test_retrieval_result_to_dict", "tests/test_rag_base.py::test_document_processing_workflow", "tests/test_rag_chunking.py::TestChunkingIntegration::test_chunker_consistency[FixedSizeChunker-kwargs0]", "tests/test_rag_chunking.py::TestChunkingIntegration::test_chunker_consistency[RecursiveChunker-kwargs2]", "tests/test_rag_chunking.py::TestChunkingIntegration::test_chunker_consistency[SemanticChunker-kwargs1]", "tests/test_rag_chunking.py::TestChunkingIntegration::test_chunking_metadata_preservation", "tests/test_rag_chunking.py::TestChunkingIntegration::test_chunking_preserves_content", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_creation", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_empty_document", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_short_document", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_simple_text", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_with_overlap", "tests/test_rag_chunking.py::TestFixedSizeChunker::test_fixed_size_chunker_word_boundaries", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_creation", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_default_separators", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_fallback_chain", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_hierarchy", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_section_headers", "tests/test_rag_chunking.py::TestRecursiveChunker::test_recursive_chunker_with_overlap", "tests/test_rag_chunking.py::TestSemanticChunker::test_semantic_chunker_creation", "tests/test_rag_chunking.py::TestSemanticChunker::test_semantic_chunker_fallback_to_fixed", "tests/test_rag_chunking.py::TestSemanticChunker::test_semantic_chunker_paragraph_boundaries", "tests/test_rag_chunking.py::TestSemanticChunker::test_semantic_chunker_section_headers", "tests/test_rag_chunking.py::TestSemanticChunker::test_semantic_chunker_sentence_boundaries", "tests/test_rag_chunking.py::test_chunking_performance", "tests/test_retriever.py::test_retriever_index_and_search", "tests/test_security.py::TestCapabilityManager::test_get_allowed_tools", "tests/test_security.py::TestCapabilityManager::test_tool_allowlist", "tests/test_security.py::TestInputValidator::test_pii_detection", "tests/test_security.py::TestInputValidator::test_prompt_injection_detection", "tests/test_security.py::TestInputValidator::test_safe_input_passes", "tests/test_security.py::TestInputValidator::test_workflow_validation", "tests/test_security.py::test_security_integration", "tests/test_security.py::test_security_violation_exception", "tests/test_security_auth.py::TestAuthManager::test_authenticate_user", "tests/test_security_auth.py::TestAuthManager::test_authorize_action", "tests/test_security_auth.py::TestAuthManager::test_get_active_sessions", "tests/test_security_auth.py::TestAuthManager::test_logout_user", "tests/test_security_auth.py::TestAuthManager::test_validate_token", "tests/test_security_auth.py::TestAuthToken::test_token_creation", "tests/test_security_auth.py::TestAuthToken::test_token_expiration", "tests/test_security_auth.py::TestAuthToken::test_token_scopes", "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_invalid_password", "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_nonexistent_user", "tests/test_security_auth.py::TestJWTAuthProvider::test_authenticate_valid_user", "tests/test_security_auth.py::TestJWTAuthProvider::test_create_token", "tests/test_security_auth.py::TestJWTAuthProvider::test_create_user", "tests/test_security_auth.py::TestJWTAuthProvider::test_password_hashing", "tests/test_security_auth.py::TestJWTAuthProvider::test_role_permissions", "tests/test_security_auth.py::TestJWTAuthProvider::test_validate_invalid_token", "tests/test_security_auth.py::TestJWTAuthProvider::test_validate_token", "tests/test_security_auth.py::TestUser::test_user_creation", "tests/test_security_auth.py::TestUser::test_user_permissions", "tests/test_security_auth.py::test_auth_system_integration", "tests/test_tokenizer.py::test_tokenizer_round_trip", "tests/test_vector_store.py::test_vector_store_add_and_search", "tests/test_workflow_engine.py::test_workflow_branching", "tests/test_workflow_engine.py::test_workflow_cycle_detection", "tests/test_workflow_engine.py::test_workflow_engine_example_yaml", "tests/test_workflow_llm.py::test_workflow_call_llm_echo_basic", "tests/test_workflow_llm.py::test_workflow_call_llm_ollama_smoke", "tests/test_workflow_llm.py::test_workflow_rag_with_llm_echo", "tests/test_workflow_rag.py::test_workflow_rag_retrieve_and_echo"]