import React, { use<PERSON><PERSON>back, useMemo } from 'react';
import React<PERSON><PERSON>, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  Edge,
  Node,
  ReactFlowProvider,
} from 'reactflow';
import 'reactflow/dist/style.css';

import { useWorkflowStore } from '../../stores/workflowStore';
import { LLMNode, ToolNode, InputNode, OutputNode, ConditionNode } from './nodes';
import type { WorkflowNode, WorkflowEdge } from '../../types/workflow';

const nodeTypes = {
  llm: LLMNode,
  tool: ToolNode,
  input: InputNode,
  output: OutputNode,
  condition: ConditionNode,
};

interface WorkflowCanvasProps {
  onNodeClick?: (node: WorkflowNode) => void;
  onEdgeClick?: (edge: WorkflowEdge) => void;
  onCanvasClick?: () => void;
}

export const WorkflowCanvas: React.FC<WorkflowCanvasProps> = ({
  onNodeClick,
  onEdgeClick,
  onCanvasClick,
}) => {
  const {
    workflow,
    onNodesChange,
    onEdgesChange,
    onConnect,
    deleteNode,
    selectNode,
    selectEdge,
    validateConnection,
  } = useWorkflowStore();

  const nodes = workflow?.nodes || [];
  const edges = workflow?.edges || [];

  const handleNodeClick = useCallback(
    (event: React.MouseEvent, node: Node) => {
      event.stopPropagation();
      const workflowNode = node as WorkflowNode;
      selectNode(workflowNode);
      onNodeClick?.(workflowNode);
    },
    [selectNode, onNodeClick]
  );

  const handleEdgeClick = useCallback(
    (event: React.MouseEvent, edge: Edge) => {
      event.stopPropagation();
      const workflowEdge = edge as WorkflowEdge;
      selectEdge(workflowEdge);
      onEdgeClick?.(workflowEdge);
    },
    [selectEdge, onEdgeClick]
  );

  const handlePaneClick = useCallback(() => {
    selectNode(null);
    selectEdge(null);
    onCanvasClick?.();
  }, [selectNode, selectEdge, onCanvasClick]);

  const handleConnect = useCallback(
    (connection: Connection) => {
      if (validateConnection(connection)) {
        onConnect(connection);
      }
    },
    [onConnect, validateConnection]
  );

  const handleNodeDelete = useCallback(
    (nodeId: string) => {
      deleteNode(nodeId);
    },
    [deleteNode]
  );

  // Add node-specific props
  const nodesWithProps = useMemo(
    () =>
      nodes.map((node) => ({
        ...node,
        data: {
          ...node.data,
          onDelete: handleNodeDelete,
          onConfigure: (nodeId: string) => {
            const nodeToSelect = nodes.find((n) => n.id === nodeId);
            if (nodeToSelect) {
              selectNode(nodeToSelect);
              onNodeClick?.(nodeToSelect);
            }
          },
        },
      })),
    [nodes, handleNodeDelete, selectNode, onNodeClick]
  );

  const defaultEdgeOptions = {
    animated: true,
    style: { stroke: '#1890ff', strokeWidth: 2 },
  };

  return (
    <div style={{ width: '100%', height: '100%' }}>
      <ReactFlow
        nodes={nodesWithProps}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={handleConnect}
        onNodeClick={handleNodeClick}
        onEdgeClick={handleEdgeClick}
        onPaneClick={handlePaneClick}
        nodeTypes={nodeTypes}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionLineStyle={{ stroke: '#1890ff', strokeWidth: 2 }}
        connectionLineType="smoothstep"
        fitView
        attributionPosition="bottom-left"
      >
        <Background color="#f0f0f0" gap={20} />
        <Controls />
        <MiniMap
          nodeColor={(node) => {
            switch (node.type) {
              case 'input':
                return '#52c41a';
              case 'llm':
                return '#722ed1';
              case 'tool':
                return '#13c2c2';
              case 'condition':
                return '#eb2f96';
              case 'output':
                return '#fa8c16';
              default:
                return '#1890ff';
            }
          }}
          nodeStrokeWidth={3}
          zoomable
          pannable
        />
      </ReactFlow>
    </div>
  );
};

// Wrapper component with ReactFlowProvider
export const WorkflowCanvasProvider: React.FC<WorkflowCanvasProps> = (props) => {
  return (
    <ReactFlowProvider>
      <WorkflowCanvas {...props} />
    </ReactFlowProvider>
  );
};
