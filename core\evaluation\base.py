"""Base classes for evaluation framework."""

from __future__ import annotations

import asyncio
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Union


@dataclass
class EvaluationResult:
    """Result of a single evaluation."""
    metric_name: str
    score: float
    details: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "metric_name": self.metric_name,
            "score": self.score,
            "details": self.details,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat()
        }


class EvaluationError(Exception):
    """Base exception for evaluation-related errors."""
    
    def __init__(self, message: str, metric_name: str = None, details: Dict[str, Any] = None):
        super().__init__(message)
        self.metric_name = metric_name
        self.details = details or {}


class Metric(ABC):
    """Abstract base class for evaluation metrics.
    
    Metrics compute quantitative scores for model outputs
    compared to reference data or based on intrinsic quality.
    
    Types of metrics:
    - Reference-based: Compare output to ground truth (BLEU, ROUGE)
    - Reference-free: Evaluate output quality directly (Perplexity, Fluency)
    - Task-specific: Domain-specific evaluation (Code correctness, Math accuracy)
    
    Example:
        >>> class CustomMetric(Metric):
        ...     def compute(self, predictions: List[str], references: List[str]) -> EvaluationResult:
        ...         score = calculate_custom_score(predictions, references)
        ...         return EvaluationResult(
        ...             metric_name=self.name,
        ...             score=score,
        ...             details={"sample_count": len(predictions)},
        ...             metadata={"version": "1.0"},
        ...             timestamp=datetime.now()
        ...         )
    """
    
    def __init__(self, name: str, description: str = ""):
        """Initialize metric with name and description.
        
        Args:
            name: Unique metric name
            description: Human-readable description
        """
        self.name = name
        self.description = description
    
    @abstractmethod
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute metric score.
        
        Args:
            predictions: Model predictions/outputs
            references: Reference/ground truth data (optional for reference-free metrics)
            **kwargs: Additional metric-specific parameters
            
        Returns:
            EvaluationResult with score and details
            
        Raises:
            EvaluationError: If computation fails
        """
        pass
    
    def validate_inputs(self, predictions: List[str], references: List[str] = None) -> None:
        """Validate input data.
        
        Args:
            predictions: Model predictions
            references: Reference data
            
        Raises:
            EvaluationError: If inputs are invalid
        """
        if not predictions:
            raise EvaluationError("Predictions cannot be empty", self.name)
        
        if references is not None and len(predictions) != len(references):
            raise EvaluationError(
                f"Predictions ({len(predictions)}) and references ({len(references)}) must have same length",
                self.name
            )
    
    def get_config(self) -> Dict[str, Any]:
        """Get metric configuration.
        
        Returns:
            Dictionary with metric configuration
        """
        return {
            "name": self.name,
            "description": self.description,
            "type": self.__class__.__name__
        }


class AsyncMetric(Metric):
    """Base class for asynchronous metrics.
    
    For metrics that require async operations like API calls
    or heavy computations that benefit from async execution.
    """
    
    @abstractmethod
    async def compute_async(self, predictions: List[str], references: List[str] = None,
                           **kwargs) -> EvaluationResult:
        """Compute metric score asynchronously.
        
        Args:
            predictions: Model predictions/outputs
            references: Reference/ground truth data
            **kwargs: Additional metric-specific parameters
            
        Returns:
            EvaluationResult with score and details
        """
        pass
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Synchronous wrapper for async computation."""
        return asyncio.run(self.compute_async(predictions, references, **kwargs))


class BatchMetric(Metric):
    """Base class for metrics that support batch processing.
    
    Optimizes computation by processing multiple samples
    simultaneously rather than one at a time.
    """
    
    def __init__(self, name: str, description: str = "", batch_size: int = 32):
        """Initialize batch metric.
        
        Args:
            name: Metric name
            description: Metric description
            batch_size: Batch size for processing
        """
        super().__init__(name, description)
        self.batch_size = batch_size
    
    @abstractmethod
    def compute_batch(self, prediction_batches: List[List[str]], 
                     reference_batches: List[List[str]] = None,
                     **kwargs) -> List[EvaluationResult]:
        """Compute metric for multiple batches.
        
        Args:
            prediction_batches: Batches of predictions
            reference_batches: Batches of references
            **kwargs: Additional parameters
            
        Returns:
            List of EvaluationResult objects, one per batch
        """
        pass
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute metric with automatic batching."""
        # Split into batches
        prediction_batches = [
            predictions[i:i + self.batch_size]
            for i in range(0, len(predictions), self.batch_size)
        ]
        
        reference_batches = None
        if references:
            reference_batches = [
                references[i:i + self.batch_size]
                for i in range(0, len(references), self.batch_size)
            ]
        
        # Compute batch results
        batch_results = self.compute_batch(prediction_batches, reference_batches, **kwargs)
        
        # Aggregate results
        total_score = sum(result.score for result in batch_results)
        avg_score = total_score / len(batch_results)
        
        # Combine details
        combined_details = {
            "batch_count": len(batch_results),
            "batch_size": self.batch_size,
            "total_samples": len(predictions),
            "batch_scores": [result.score for result in batch_results]
        }
        
        return EvaluationResult(
            metric_name=self.name,
            score=avg_score,
            details=combined_details,
            metadata={"aggregated": True},
            timestamp=datetime.now()
        )


class CompositeMetric(Metric):
    """Composite metric that combines multiple sub-metrics.
    
    Useful for creating aggregate scores or multi-faceted
    evaluations that consider multiple aspects of quality.
    
    Example:
        >>> composite = CompositeMetric("overall_quality", [
        ...     (BLEUMetric(), 0.4),
        ...     (ROUGEMetric(), 0.3),
        ...     (FluencyMetric(), 0.3)
        ... ])
    """
    
    def __init__(self, name: str, metrics: List[tuple[Metric, float]], 
                 description: str = ""):
        """Initialize composite metric.
        
        Args:
            name: Composite metric name
            metrics: List of (metric, weight) tuples
            description: Metric description
        """
        super().__init__(name, description)
        self.metrics = metrics
        
        # Validate weights sum to 1.0
        total_weight = sum(weight for _, weight in metrics)
        if abs(total_weight - 1.0) > 1e-6:
            raise ValueError(f"Metric weights must sum to 1.0, got {total_weight}")
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute weighted combination of sub-metrics."""
        sub_results = []
        weighted_score = 0.0
        
        for metric, weight in self.metrics:
            try:
                result = metric.compute(predictions, references, **kwargs)
                sub_results.append({
                    "metric": metric.name,
                    "score": result.score,
                    "weight": weight,
                    "weighted_score": result.score * weight
                })
                weighted_score += result.score * weight
                
            except Exception as e:
                raise EvaluationError(
                    f"Sub-metric {metric.name} failed: {e}",
                    self.name,
                    {"failed_metric": metric.name}
                )
        
        return EvaluationResult(
            metric_name=self.name,
            score=weighted_score,
            details={
                "sub_results": sub_results,
                "total_weight": sum(weight for _, weight in self.metrics)
            },
            metadata={"composite": True, "sub_metric_count": len(self.metrics)},
            timestamp=datetime.now()
        )
    
    def add_metric(self, metric: Metric, weight: float) -> None:
        """Add a sub-metric with weight.
        
        Args:
            metric: Metric to add
            weight: Weight for this metric
        """
        self.metrics.append((metric, weight))
        
        # Renormalize weights
        total_weight = sum(w for _, w in self.metrics)
        self.metrics = [(m, w / total_weight) for m, w in self.metrics]
    
    def remove_metric(self, metric_name: str) -> bool:
        """Remove a sub-metric by name.
        
        Args:
            metric_name: Name of metric to remove
            
        Returns:
            True if metric was removed
        """
        original_count = len(self.metrics)
        self.metrics = [(m, w) for m, w in self.metrics if m.name != metric_name]
        
        if len(self.metrics) < original_count:
            # Renormalize weights
            if self.metrics:
                total_weight = sum(w for _, w in self.metrics)
                self.metrics = [(m, w / total_weight) for m, w in self.metrics]
            return True
        
        return False
