# M-GAIF Frontend Technical Specifications

## 📋 **Document Information**

| Field | Value |
|-------|-------|
| **Document Type** | Technical Specification |
| **Related PRD** | MGAIF_FRONTEND_PRD.md |
| **Version** | 1.0 |
| **Date** | January 2025 |
| **Owner** | Engineering Team |

## 🏗️ **System Architecture**

### **Frontend Architecture Diagram**
```
┌─────────────────────────────────────────────────────────────┐
│                    Browser (Client)                         │
├─────────────────────────────────────────────────────────────┤
│  React Application Layer                                    │
│  ├─ Pages (Dashboard, Builders, Templates)                 │
│  ├─ Components (UI, Business Logic)                        │
│  ├─ Hooks (State, API, Utilities)                          │
│  └─ Context (Auth, Theme, Settings)                        │
├─────────────────────────────────────────────────────────────┤
│  State Management Layer                                     │
│  ├─ Zustand (Global State)                                 │
│  ├─ React Query (Server State)                             │
│  ├─ React Hook Form (Form State)                           │
│  └─ Local Storage (Persistence)                            │
├─────────────────────────────────────────────────────────────┤
│  API & Communication Layer                                  │
│  ├─ Axios (HTTP Client)                                    │
│  ├─ WebSocket (Real-time)                                  │
│  ├─ File Upload (Multipart)                                │
│  └─ Authentication (JWT)                                   │
├─────────────────────────────────────────────────────────────┤
│  Build & Development Layer                                  │
│  ├─ Vite (Build Tool)                                      │
│  ├─ TypeScript (Type Safety)                               │
│  ├─ ESLint + Prettier (Code Quality)                       │
│  └─ Jest + RTL (Testing)                                   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    M-GAIF Backend                           │
│  ├─ Authentication API                                      │
│  ├─ Project Management API                                  │
│  ├─ Workflow Execution API                                  │
│  ├─ File Processing API                                     │
│  └─ Agent Management API                                    │
└─────────────────────────────────────────────────────────────┘
```

### **Component Architecture**
```typescript
// Component Hierarchy
App
├─ AuthProvider
├─ ThemeProvider
├─ QueryProvider
└─ Router
    ├─ Layout
    │   ├─ Header
    │   ├─ Sidebar
    │   └─ Content
    └─ Pages
        ├─ Dashboard
        ├─ WorkflowBuilder
        ├─ ChatbotBuilder
        ├─ AgentBuilder
        ├─ TemplateGallery
        └─ Settings

// Shared Components
├─ UI Components (Button, Input, Modal, etc.)
├─ Business Components (ProjectCard, WorkflowNode, etc.)
├─ Layout Components (Grid, Container, Spacer, etc.)
└─ Utility Components (ErrorBoundary, Loading, etc.)
```

## 🔧 **Technology Stack Details**

### **Core Technologies**
```json
{
  "runtime": "Node.js 18+",
  "framework": "React 18.2+",
  "language": "TypeScript 5.0+",
  "bundler": "Vite 5.0+",
  "packageManager": "npm 9+"
}
```

### **UI & Styling**
```json
{
  "uiLibrary": "Ant Design 5.0+",
  "styling": "CSS-in-JS (Emotion)",
  "icons": "Lucide React",
  "charts": "Recharts",
  "visualEditor": "React Flow"
}
```

### **State Management**
```json
{
  "globalState": "Zustand 4.0+",
  "serverState": "React Query 4.0+",
  "formState": "React Hook Form 7.0+",
  "routing": "React Router 6.0+"
}
```

### **Development Tools**
```json
{
  "linting": "ESLint 8.0+",
  "formatting": "Prettier 3.0+",
  "testing": "Jest + React Testing Library",
  "e2e": "Playwright",
  "typeChecking": "TypeScript Compiler"
}
```

## 📁 **Project Structure**

```
src/
├─ components/           # Reusable UI components
│  ├─ ui/               # Basic UI components
│  ├─ business/         # Business logic components
│  ├─ layout/           # Layout components
│  └─ forms/            # Form components
├─ pages/               # Page components
│  ├─ Dashboard/
│  ├─ WorkflowBuilder/
│  ├─ ChatbotBuilder/
│  ├─ AgentBuilder/
│  └─ Templates/
├─ hooks/               # Custom React hooks
│  ├─ useAuth.ts
│  ├─ useProjects.ts
│  ├─ useWorkflow.ts
│  └─ useApi.ts
├─ stores/              # Zustand stores
│  ├─ authStore.ts
│  ├─ projectStore.ts
│  └─ uiStore.ts
├─ services/            # API services
│  ├─ api.ts
│  ├─ auth.ts
│  ├─ websocket.ts
│  └─ upload.ts
├─ types/               # TypeScript type definitions
│  ├─ api.ts
│  ├─ project.ts
│  ├─ workflow.ts
│  └─ user.ts
├─ utils/               # Utility functions
│  ├─ validation.ts
│  ├─ formatting.ts
│  └─ constants.ts
├─ styles/              # Global styles
│  ├─ globals.css
│  ├─ variables.css
│  └─ components.css
└─ assets/              # Static assets
   ├─ images/
   ├─ icons/
   └─ fonts/
```

## 🎨 **Design System Implementation**

### **Theme Configuration**
```typescript
// theme.ts
export const theme = {
  colors: {
    primary: {
      50: '#eff6ff',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
    },
    secondary: {
      50: '#f0fdf4',
      500: '#22c55e',
      600: '#16a34a',
      700: '#15803d',
    },
    neutral: {
      50: '#f9fafb',
      100: '#f3f4f6',
      500: '#6b7280',
      900: '#111827',
    }
  },
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    '2xl': '48px',
  },
  typography: {
    fontFamily: 'Inter, system-ui, sans-serif',
    fontSize: {
      xs: '12px',
      sm: '14px',
      base: '16px',
      lg: '18px',
      xl: '20px',
      '2xl': '24px',
    }
  }
}
```

### **Component Styling Standards**
```typescript
// Styled components using Emotion
import styled from '@emotion/styled'

export const Button = styled.button<ButtonProps>`
  padding: ${props => props.theme.spacing.md};
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  
  ${props => props.variant === 'primary' && `
    background: ${props.theme.colors.primary[500]};
    color: white;
    
    &:hover {
      background: ${props.theme.colors.primary[600]};
    }
  `}
`
```

## 🔌 **API Integration Specifications**

### **HTTP Client Configuration**
```typescript
// api/client.ts
import axios from 'axios'

const apiClient = axios.create({
  baseURL: process.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

### **WebSocket Integration**
```typescript
// services/websocket.ts
class WebSocketService {
  private ws: WebSocket | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5

  connect(url: string) {
    this.ws = new WebSocket(url)
    
    this.ws.onopen = () => {
      console.log('WebSocket connected')
      this.reconnectAttempts = 0
    }
    
    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data)
      this.handleMessage(data)
    }
    
    this.ws.onclose = () => {
      this.handleReconnect()
    }
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        this.reconnectAttempts++
        this.connect(this.url)
      }, 1000 * Math.pow(2, this.reconnectAttempts))
    }
  }
}
```

### **File Upload Implementation**
```typescript
// services/upload.ts
export class FileUploadService {
  async uploadFile(file: File, onProgress?: (progress: number) => void) {
    const formData = new FormData()
    formData.append('file', file)
    
    return apiClient.post('/api/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = (progressEvent.loaded / progressEvent.total) * 100
          onProgress(progress)
        }
      },
    })
  }

  async uploadMultipleFiles(files: File[]) {
    const uploads = files.map(file => this.uploadFile(file))
    return Promise.all(uploads)
  }
}
```

## 🧪 **Testing Implementation**

### **Unit Testing Setup**
```typescript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/main.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
}
```

### **Component Testing Example**
```typescript
// components/__tests__/Button.test.tsx
import { render, screen, fireEvent } from '@testing-library/react'
import { Button } from '../Button'

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>)
    expect(screen.getByText('Click me')).toBeInTheDocument()
  })

  it('calls onClick handler when clicked', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    fireEvent.click(screen.getByText('Click me'))
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('applies correct variant styles', () => {
    render(<Button variant="primary">Primary Button</Button>)
    const button = screen.getByText('Primary Button')
    expect(button).toHaveClass('btn-primary')
  })
})
```

### **Integration Testing Example**
```typescript
// pages/__tests__/Dashboard.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from 'react-query'
import { Dashboard } from '../Dashboard'
import { mockApiResponse } from '../../__mocks__/api'

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: { queries: { retry: false } },
  })
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('Dashboard Page', () => {
  it('displays user projects', async () => {
    mockApiResponse('/api/projects', {
      projects: [
        { id: '1', name: 'Test Project', type: 'chatbot' }
      ]
    })

    render(<Dashboard />, { wrapper: createWrapper() })
    
    await waitFor(() => {
      expect(screen.getByText('Test Project')).toBeInTheDocument()
    })
  })
})
```

## 🚀 **Build & Deployment**

### **Build Configuration**
```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src'),
    },
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['antd'],
          editor: ['react-flow-renderer', 'monaco-editor'],
        },
      },
    },
  },
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
      },
    },
  },
})
```

### **Docker Configuration**
```dockerfile
# Dockerfile
FROM node:18-alpine as builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

### **CI/CD Pipeline**
```yaml
# .github/workflows/deploy.yml
name: Deploy Frontend

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test
      - run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Deploy to production
        run: |
          docker build -t mgaif-frontend .
          docker push ${{ secrets.REGISTRY_URL }}/mgaif-frontend
```

## 📊 **Performance Optimization**

### **Code Splitting Strategy**
```typescript
// Lazy loading for pages
const Dashboard = lazy(() => import('./pages/Dashboard'))
const WorkflowBuilder = lazy(() => import('./pages/WorkflowBuilder'))
const ChatbotBuilder = lazy(() => import('./pages/ChatbotBuilder'))

// Route-based code splitting
<Routes>
  <Route path="/" element={
    <Suspense fallback={<Loading />}>
      <Dashboard />
    </Suspense>
  } />
</Routes>
```

### **Bundle Optimization**
```typescript
// webpack-bundle-analyzer integration
import { defineConfig } from 'vite'
import { visualizer } from 'rollup-plugin-visualizer'

export default defineConfig({
  plugins: [
    visualizer({
      filename: 'dist/stats.html',
      open: true,
    }),
  ],
})
```

### **Performance Monitoring**
```typescript
// Performance tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric: any) {
  // Send to analytics service
  gtag('event', metric.name, {
    value: Math.round(metric.value),
    event_category: 'Web Vitals',
  })
}

getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

---

**Document Version**: 1.0  
**Last Updated**: January 2025  
**Next Review**: February 2025
