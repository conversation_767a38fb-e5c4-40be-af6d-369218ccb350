import React, { useState } from 'react';
import { 
  Card, 
  Upload, 
  Button, 
  Typography, 
  List, 
  Progress, 
  Space, 
  Input, 
  Form,
  Tag,
  Divider,
  Al<PERSON>,
  Modal
} from 'antd';
import { 
  UploadOutlined, 
  FileTextOutlined, 
  LinkOutlined, 
  DeleteOutlined,
  ReloadOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useChatbotStore } from '../../../stores/chatbotStore';
import type { KnowledgeDocument, KnowledgeSource } from '../../../types/chatbot';

const { Dragger } = Upload;
const { TextArea } = Input;

interface KnowledgeBaseStepProps {
  chatbotId?: string;
}

export const KnowledgeBaseStep: React.FC<KnowledgeBaseStepProps> = ({ chatbotId }) => {
  const { 
    currentChatbot, 
    uploadDocuments, 
    deleteDocument, 
    reindexKnowledgeBase,
    isLoading 
  } = useChatbotStore();
  
  const [urlModalVisible, setUrlModalVisible] = useState(false);
  const [urlForm] = Form.useForm();

  const knowledgeBase = currentChatbot?.knowledgeBase;

  const handleFileUpload = async (info: any) => {
    const { fileList } = info;
    if (fileList.length > 0 && chatbotId) {
      try {
        await uploadDocuments(chatbotId, {
          files: fileList.map((file: any) => file.originFileObj),
          urls: [],
          sources: [],
        });
      } catch (error) {
        console.error('Upload failed:', error);
      }
    }
  };

  const handleAddUrl = async () => {
    try {
      const values = await urlForm.validateFields();
      if (chatbotId) {
        await uploadDocuments(chatbotId, {
          files: [],
          urls: [values.url],
          sources: [],
        });
      }
      setUrlModalVisible(false);
      urlForm.resetFields();
    } catch (error) {
      console.error('URL add failed:', error);
    }
  };

  const handleDeleteDocument = async (documentId: string) => {
    if (chatbotId) {
      await deleteDocument(chatbotId, documentId);
    }
  };

  const handleReindex = async () => {
    if (chatbotId) {
      await reindexKnowledgeBase(chatbotId);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
      case 'indexed':
        return 'success';
      case 'processing':
        return 'processing';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getFileIcon = (type: string) => {
    switch (type) {
      case 'pdf':
        return '📄';
      case 'docx':
        return '📝';
      case 'txt':
        return '📃';
      case 'md':
        return '📋';
      case 'url':
        return '🔗';
      default:
        return '📄';
    }
  };

  return (
    <Card title="Knowledge Base" style={{ maxWidth: 800, margin: '0 auto' }}>
      <Typography.Paragraph type="secondary" style={{ marginBottom: '24px' }}>
        Upload documents, add websites, or connect data sources to train your chatbot. 
        The more relevant information you provide, the better your chatbot will perform.
      </Typography.Paragraph>

      {/* Knowledge Base Status */}
      {knowledgeBase && (
        <Card size="small" style={{ marginBottom: '24px', backgroundColor: '#f8f9fa' }}>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <Typography.Text strong>Knowledge Base Status:</Typography.Text>
              <Tag color={getStatusColor(knowledgeBase.indexingStatus)}>
                {knowledgeBase.indexingStatus.toUpperCase()}
              </Tag>
            </Space>
            <Button 
              icon={<ReloadOutlined />} 
              size="small"
              onClick={handleReindex}
              loading={knowledgeBase.indexingStatus === 'processing'}
            >
              Reindex
            </Button>
          </div>
          <div style={{ marginTop: '12px', display: 'flex', gap: '24px' }}>
            <Typography.Text type="secondary">
              Documents: <strong>{knowledgeBase.totalDocuments}</strong>
            </Typography.Text>
            <Typography.Text type="secondary">
              Chunks: <strong>{knowledgeBase.totalChunks}</strong>
            </Typography.Text>
            <Typography.Text type="secondary">
              Last Updated: <strong>{new Date(knowledgeBase.lastUpdated).toLocaleDateString()}</strong>
            </Typography.Text>
          </div>
        </Card>
      )}

      {/* Upload Methods */}
      <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '16px', marginBottom: '24px' }}>
        {/* File Upload */}
        <Card size="small" title="Upload Documents">
          <Dragger
            multiple
            accept=".pdf,.txt,.docx,.md"
            beforeUpload={() => false}
            onChange={handleFileUpload}
            style={{ marginBottom: '12px' }}
          >
            <p className="ant-upload-drag-icon">
              <FileTextOutlined />
            </p>
            <p className="ant-upload-text">Click or drag files to upload</p>
            <p className="ant-upload-hint">
              Supports PDF, TXT, DOCX, MD files
            </p>
          </Dragger>
          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
            Maximum file size: 10MB per file
          </Typography.Text>
        </Card>

        {/* URL Input */}
        <Card size="small" title="Add Website">
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <LinkOutlined style={{ fontSize: '32px', color: '#1890ff', marginBottom: '12px' }} />
            <Typography.Paragraph>
              Add websites or web pages to your knowledge base
            </Typography.Paragraph>
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={() => setUrlModalVisible(true)}
            >
              Add Website
            </Button>
          </div>
        </Card>
      </div>

      {/* Document List */}
      {knowledgeBase && knowledgeBase.documents.length > 0 && (
        <>
          <Divider />
          <Typography.Title level={5}>Uploaded Documents</Typography.Title>
          <List
            dataSource={knowledgeBase.documents}
            renderItem={(document: KnowledgeDocument) => (
              <List.Item
                actions={[
                  <Button
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDeleteDocument(document.id)}
                    size="small"
                  >
                    Delete
                  </Button>,
                ]}
              >
                <List.Item.Meta
                  avatar={<span style={{ fontSize: '20px' }}>{getFileIcon(document.type)}</span>}
                  title={
                    <Space>
                      <Typography.Text strong>{document.name}</Typography.Text>
                      <Tag color={getStatusColor(document.status)} size="small">
                        {document.status}
                      </Tag>
                    </Space>
                  }
                  description={
                    <Space direction="vertical" size="small" style={{ width: '100%' }}>
                      <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                        Size: {(document.size / 1024).toFixed(1)} KB • 
                        Uploaded: {new Date(document.uploadedAt).toLocaleDateString()} •
                        Chunks: {document.chunks}
                      </Typography.Text>
                      {document.status === 'processing' && (
                        <Progress percent={60} size="small" status="active" />
                      )}
                      {document.error && (
                        <Typography.Text type="danger" style={{ fontSize: '12px' }}>
                          Error: {document.error}
                        </Typography.Text>
                      )}
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        </>
      )}

      {/* Empty State */}
      {(!knowledgeBase || knowledgeBase.documents.length === 0) && (
        <Alert
          message="No documents uploaded yet"
          description="Upload documents or add websites to start building your chatbot's knowledge base. Your chatbot will use this information to answer user questions."
          type="info"
          showIcon
          style={{ marginTop: '24px' }}
        />
      )}

      {/* URL Modal */}
      <Modal
        title="Add Website"
        open={urlModalVisible}
        onOk={handleAddUrl}
        onCancel={() => setUrlModalVisible(false)}
        okText="Add Website"
      >
        <Form form={urlForm} layout="vertical">
          <Form.Item
            label="Website URL"
            name="url"
            rules={[
              { required: true, message: 'Please enter a URL' },
              { type: 'url', message: 'Please enter a valid URL' },
            ]}
          >
            <Input placeholder="https://example.com" />
          </Form.Item>
          <Form.Item
            label="Description (Optional)"
            name="description"
          >
            <TextArea 
              rows={2} 
              placeholder="Brief description of the website content..."
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* Tips */}
      <div style={{ marginTop: '32px' }}>
        <Typography.Title level={5}>Tips for Better Performance</Typography.Title>
        <ul style={{ fontSize: '14px', color: '#666' }}>
          <li>Upload documents that are directly relevant to your chatbot's purpose</li>
          <li>Use clear, well-structured documents with proper headings</li>
          <li>Include FAQs, product documentation, and common procedures</li>
          <li>Keep documents up-to-date and remove outdated information</li>
          <li>Test your chatbot with sample questions after uploading</li>
        </ul>
      </div>
    </Card>
  );
};
