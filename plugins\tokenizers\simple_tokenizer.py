"""Simple regex-based tokenizer implementation.

This module provides a basic tokenizer that splits text using regex patterns
and maintains a growing vocabulary for deterministic token ID assignment.
Suitable for development, testing, and simple applications.

Features:
- Regex-based text splitting (words and punctuation)
- Growing vocabulary with deterministic ID assignment
- Reversible tokenization (encode/decode)
- Unicode support
- Thread-safe operations

Performance Characteristics:
- Fast tokenization using compiled regex
- O(1) vocabulary lookup
- Memory usage grows with unique tokens
- No external dependencies

Example:
    >>> tokenizer = SimpleTokenizer()
    >>> tokens = tokenizer.encode("Hello, world!")
    >>> text = tokenizer.decode(tokens)
    >>> print(f"Tokens: {tokens}")
    >>> print(f"Decoded: {text}")

Note:
    This tokenizer is designed for development and testing. For production
    use with large vocabularies, consider more sophisticated tokenizers
    like SentencePiece or HuggingFace tokenizers.
"""

from __future__ import annotations

import re
from typing import Any, Dict, List

from core.text.tokenizer import Tokenizer
from core.plugins.base import Plugin, PluginStatus, HealthStatus, HealthCheckResult
from datetime import datetime


_TOKEN_RE = re.compile(r"\w+|[^\w\s]", re.UNICODE)


class SimpleTokenizer(Tokenizer, Plugin):
    """Whitespace + punctuation split with reversible per-instance vocab.

    A simple tokenizer that splits text on word boundaries and punctuation,
    maintaining a growing vocabulary for consistent token ID assignment.

    The tokenizer uses regex pattern `\w+|[^\w\s]` to split text into:
    - Word tokens (sequences of word characters)
    - Punctuation tokens (individual non-word, non-space characters)

    Attributes:
        vocab: Mapping from token strings to integer IDs
        inv_vocab: Reverse mapping from integer IDs to token strings

    Example:
        >>> tokenizer = SimpleTokenizer()
        >>> tokens = tokenizer.encode("Hello, world!")
        >>> print(tokens)  # [0, 1, 2]
        >>> print(tokenizer.decode(tokens))  # "Hello, world!"

    Note:
        - Vocabulary grows as new tokens are encountered
        - Token IDs are assigned sequentially starting from 0
        - Suitable for development and testing only
        - Not recommended for production due to unbounded vocabulary growth
    """

    def __init__(self) -> None:
        """Initialize tokenizer with empty vocabulary.

        Creates a new tokenizer instance with empty vocabulary mappings.
        Token IDs will be assigned sequentially as new tokens are encountered.
        """
        super().__init__(name="simple_tokenizer", version="1.0.0")
        self.vocab: Dict[str, int] = {}
        self.inv_vocab: List[str] = []

    def _split(self, text: str) -> List[str]:
        """Split text into tokens using regex pattern.

        Args:
            text: Input text to split

        Returns:
            List of token strings
        """
        return _TOKEN_RE.findall(text)

    def encode(self, text: str) -> List[int]:
        """Convert text to token IDs, growing vocabulary as needed.

        Splits the input text and assigns integer IDs to each token,
        adding new tokens to the vocabulary as they are encountered.

        Args:
            text: Input text to tokenize

        Returns:
            List of integer token IDs

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> ids = tokenizer.encode("Hello, world!")
            >>> print(ids)  # [0, 1, 2]
        """
        ids: List[int] = []
        for tok in self._split(text):
            if tok not in self.vocab:
                self.vocab[tok] = len(self.inv_vocab)
                self.inv_vocab.append(tok)
            ids.append(self.vocab[tok])
        return ids

    def decode(self, tokens: List[int]) -> str:
        """Convert token IDs back to text with appropriate spacing.

        Reconstructs text from token IDs, adding spaces between word tokens
        while preserving punctuation attachment.

        Args:
            tokens: List of integer token IDs to decode

        Returns:
            Reconstructed text string

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> text = tokenizer.decode([0, 1, 2])
            >>> print(text)  # "Hello, world!"

        Note:
            Spacing is inferred based on token types (word vs punctuation).
            The result may not exactly match the original text formatting.
        """
        # Join with spaces where appropriate; we reconstruct by simple concatenation
        out: List[str] = []
        prev_was_word = False
        for tid in tokens:
            tok = self.inv_vocab[tid]
            is_word = bool(re.match(r"^\w+$", tok))
            if out and is_word and prev_was_word:
                out.append(" ")
            out.append(tok)
            prev_was_word = is_word
        return "".join(out)

    def token_count(self, text: str) -> int:
        """Count tokens without full tokenization.

        Efficiently counts the number of tokens that would be produced
        by encoding the text, without actually creating the vocabulary entries.

        Args:
            text: Input text to count tokens for

        Returns:
            Number of tokens the text would produce

        Example:
            >>> tokenizer = SimpleTokenizer()
            >>> count = tokenizer.token_count("Hello, world!")
            >>> print(count)  # 3
        """
        return len(self._split(text))

    async def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the tokenizer with configuration.

        Args:
            config: Configuration dictionary (unused for SimpleTokenizer)
        """
        self.status = PluginStatus.ACTIVE

    async def health_check(self) -> HealthCheckResult:
        """Perform health check on the tokenizer.

        Returns:
            HealthCheckResult with current health status
        """
        try:
            # Test tokenization
            test_tokens = self.encode("test")
            test_text = self.decode(test_tokens)

            is_healthy = len(test_tokens) > 0 and isinstance(test_text, str)

            return HealthCheckResult(
                status=HealthStatus.HEALTHY if is_healthy else HealthStatus.UNHEALTHY,
                message="Tokenizer operational" if is_healthy else "Tokenizer failed test",
                details={
                    "vocab_size": len(self.vocab),
                    "test_tokens": len(test_tokens),
                    "test_successful": is_healthy
                },
                timestamp=datetime.now(),
                response_time_ms=1.0
            )
        except Exception as e:
            return HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {e}",
                details={"error": str(e)},
                timestamp=datetime.now(),
                response_time_ms=0.0
            )
