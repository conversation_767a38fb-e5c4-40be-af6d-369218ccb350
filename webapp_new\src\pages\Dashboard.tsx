import React from 'react'
import { Card, Col, Row, Statistic, Typography } from 'antd'
import { Link } from 'react-router-dom'

export default function Dashboard() {
  return (
    <div>
      <Typography.Title level={3}>Welcome</Typography.Title>
      <Typography.Paragraph>
        Configure LLMs, build RAG stores, create agents, and compose workflows without writing code.
      </Typography.Paragraph>
      <Row gutter={[16, 16]}>
        <Col xs={24} md={12} lg={8}>
          <Link to="/workflows">
            <Card hoverable>
              <Statistic title="Workflows" value="Compose & Run"/>
            </Card>
          </Link>
        </Col>
        <Col xs={24} md={12} lg={8}>
          <Link to="/rag">
            <Card hoverable>
              <Statistic title="RAG Stores" value="Index & Search"/>
            </Card>
          </Link>
        </Col>
        <Col xs={24} md={12} lg={8}>
          <Link to="/llms">
            <Card hoverable>
              <Statistic title="LLMs" value="Test & Stream"/>
            </Card>
          </Link>
        </Col>
      </Row>
    </div>
  )
}
