# syntax=docker/dockerfile:1
FROM node:18-alpine AS deps
WORKDIR /app
COPY package.json package-lock.json* ./
RUN npm ci || npm install

FROM deps AS build
WORKDIR /app
COPY . .
# Run unit tests (no cache bust for source changes here intentionally)
RUN npm run test

# Optionally build to ensure it compiles (uncomment if desired)
# RUN npm run build

# Final minimal image that just prints success (optional)
FROM alpine:3.19
CMD ["/bin/sh", "-c", "echo 'Webapp tests passed inside Docker.'"]
