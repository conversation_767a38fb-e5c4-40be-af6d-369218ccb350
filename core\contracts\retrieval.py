"""Document retrieval and vector search contracts.

This module defines the standardized interfaces for document retrieval and vector
search operations within the M-GAIF framework. It provides data structures for
vector records, search results, and retrieval operations.

The contracts support:
- Vector-based document storage and retrieval
- Similarity search with scoring
- Metadata-rich document records
- Flexible search result formatting
- Integration with various vector databases

Example:
    >>> from core.contracts.retrieval import VectorRecord, SearchResult
    >>> record = VectorRecord(
    ...     id="doc1",
    ...     vector=[0.1, 0.2, 0.3],
    ...     metadata={"title": "AI Overview", "category": "tech"}
    ... )
    >>> # Use with retriever implementations
    >>> results = retriever.search(query_vector, top_k=5)

Note:
    Vector dimensions should be consistent within a single retrieval system.
    Metadata can contain arbitrary JSON-serializable data for filtering and display.
"""

from __future__ import annotations

from typing import Any, Dict, List, Optional
from pydantic import BaseModel, Field


class VectorRecord(BaseModel):
    """A document record with vector embedding and metadata.

    Represents a document or text chunk that has been converted to a vector
    embedding for similarity search and retrieval operations.

    Args:
        id: Unique identifier for the document
        vector: Dense vector representation of the document content
        metadata: Additional information about the document (title, category, etc.)

    Example:
        >>> record = VectorRecord(
        ...     id="doc_123",
        ...     vector=[0.1, 0.2, 0.3, 0.4],
        ...     metadata={
        ...         "title": "AI Overview",
        ...         "category": "technology",
        ...         "author": "Jane Doe"
        ...     }
        ... )

    Note:
        Vector dimensions should be consistent across all records in a collection.
        Metadata can contain any JSON-serializable data for filtering and display.
    """

    id: str
    vector: List[float]
    metadata: Dict[str, Any] = Field(default_factory=dict)


class SearchResult(BaseModel):
    """A single search result from a retrieval operation.

    Represents a document that was found during a similarity search,
    including its relevance score and associated metadata.

    Args:
        id: Unique identifier of the matching document
        score: Similarity score (higher = more relevant)
        metadata: Document metadata (title, content, category, etc.)

    Example:
        >>> result = SearchResult(
        ...     id="doc_456",
        ...     score=0.95,
        ...     metadata={
        ...         "title": "Machine Learning Basics",
        ...         "content": "Introduction to ML concepts...",
        ...         "category": "education"
        ...     }
        ... )

    Note:
        Score interpretation depends on the similarity metric used.
        Common ranges are [0,1] for cosine similarity or [-1,1] for other metrics.
    """

    id: str
    score: float
    metadata: Dict[str, Any] = Field(default_factory=dict)
    # Optional full record for convenience
    record: Optional[VectorRecord] = None

# Contracts to be implemented in Phase 2
