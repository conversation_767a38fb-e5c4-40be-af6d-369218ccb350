# M-GAIF MCP Modular Architecture Guide

## Overview

The Modular Generative AI Framework (M-GAIF) implements a **Model Context Protocol (MCP) modular architecture** that enables hot-swappable, upgradeable components for AI applications. This architecture allows developers to seamlessly upgrade and replace core components without system downtime.

## Core Modular Components

### 1. 🔤 Tokenizer Module
**Purpose**: Text tokenization with pluggable strategies
**Interface**: `core.text.tokenizer.Tokenizer`
**Current Implementations**:
- `SimpleTokenizer`: Regex-based whitespace + punctuation splitting
- **Upgradeable to**: HuggingFace tokenizers, SentencePiece, custom tokenizers

```python
# Abstract interface
class Tokenizer(ABC):
    @abstractmethod
    def encode(self, text: str) -> List[int]: ...
    
    @abstractmethod  
    def decode(self, tokens: List[int]) -> str: ...
```

**Hot-Swap Example**:
```python
# Runtime component replacement
from plugins.tokenizers.huggingface_tokenizer import HuggingFaceTokenizer

# Upgrade tokenizer without restart
new_tokenizer = HuggingFaceTokenizer(model="gpt2")
app.replace_component("tokenizer", new_tokenizer)
```

### 2. 🧠 Embedder Module
**Purpose**: Text-to-vector conversion with multiple embedding strategies
**Interface**: `core.text.embeddings.Embedder`
**Current Implementations**:
- `SimpleEmbedder`: Deterministic hash-based embeddings
- **Upgradeable to**: OpenAI embeddings, Sentence-BERT, custom models

```python
# Abstract interface
class Embedder(ABC):
    @abstractmethod
    def embed(self, texts: List[str]) -> List[List[float]]: ...
```

**Hot-Swap Example**:
```python
# Upgrade to production embeddings
from plugins.embedders.openai_embedder import OpenAIEmbedder

new_embedder = OpenAIEmbedder(model="text-embedding-3-large")
app.replace_component("embedder", new_embedder)
```

### 3. 🔍 Retriever Module
**Purpose**: Document search and retrieval with pluggable backends
**Interface**: `core.contracts.retrieval` + custom retriever contracts
**Current Implementations**:
- `InMemoryRetriever`: In-memory vector search
- **Upgradeable to**: Pinecone, Weaviate, Chroma, FAISS, Elasticsearch

```python
# Retriever interface
class Retriever(ABC):
    @abstractmethod
    def index(self, documents: List[Tuple[str, str]]) -> None: ...
    
    @abstractmethod
    def search(self, query: str, top_k: int) -> List[SearchResult]: ...
```

**Hot-Swap Example**:
```python
# Upgrade to production vector database
from plugins.retrievers.pinecone_retriever import PineconeRetriever

new_retriever = PineconeRetriever(
    api_key=os.getenv("PINECONE_API_KEY"),
    environment="production"
)
app.replace_component("retriever", new_retriever)
```

### 4. 🤖 Language Model Module
**Purpose**: LLM integration with multiple provider support
**Interface**: `core.contracts.llm.LLMAdapter`
**Current Implementations**:
- `EchoAdapter`: Deterministic testing adapter
- `OllamaAdapter`: Local Ollama integration
- **Upgradeable to**: OpenAI, Anthropic, Google, Azure OpenAI, custom models

```python
# LLM interface
class LLMAdapter(ABC):
    @abstractmethod
    async def chat(self, request: ChatCompletionRequest) -> ChatCompletionResponse: ...
    
    @abstractmethod
    async def chat_stream(self, request: ChatCompletionRequest) -> AsyncIterator[ChatCompletionChunk]: ...
```

**Hot-Swap Example**:
```python
# Upgrade to production LLM
from plugins.llm_adapters.openai_adapter import OpenAIAdapter

new_llm = OpenAIAdapter(
    api_key=os.getenv("OPENAI_API_KEY"),
    model="gpt-4-turbo"
)
app.replace_component("llm", new_llm)
```

### 5. 🗄️ RAG Database Module
**Purpose**: Knowledge base storage and retrieval
**Interface**: `core.stores` + vector store contracts
**Current Implementations**:
- `InMemoryVectorStore`: In-memory vector storage
- **Upgradeable to**: PostgreSQL+pgvector, Redis, MongoDB Atlas, Neo4j

```python
# Vector store interface
class VectorStore(ABC):
    @abstractmethod
    def add(self, records: List[VectorRecord]) -> None: ...
    
    @abstractmethod
    def search(self, query_vector: List[float], top_k: int) -> List[SearchResult]: ...
```

**Hot-Swap Example**:
```python
# Upgrade to production database
from plugins.stores.postgresql_vector_store import PostgreSQLVectorStore

new_store = PostgreSQLVectorStore(
    connection_string=os.getenv("DATABASE_URL"),
    table_name="embeddings"
)
app.replace_component("vector_store", new_store)
```

## MCP Tool-Based Architecture

### Tool Registration System
M-GAIF exposes all components as **MCP tools** that can be dynamically registered and upgraded:

```python
# MCP tool registration
@app.post("/mcp/tools/tokenize")
async def tokenize_tool(request: TokenizeRequest):
    return await current_tokenizer.encode(request.text)

@app.post("/mcp/tools/embed") 
async def embed_tool(request: EmbedRequest):
    return await current_embedder.embed(request.texts)

@app.post("/mcp/tools/retriever/search")
async def search_tool(request: SearchRequest):
    return await current_retriever.search(request.query, request.top_k)
```

### Dynamic Component Management

```python
class ComponentManager:
    """Manages hot-swappable components"""
    
    def __init__(self):
        self.components = {}
        self.component_locks = {}
    
    async def replace_component(self, name: str, new_component: Any):
        """Hot-swap component without downtime"""
        async with self.component_locks[name]:
            # Graceful shutdown of old component
            if hasattr(self.components[name], 'shutdown'):
                await self.components[name].shutdown()
            
            # Initialize new component
            if hasattr(new_component, 'initialize'):
                await new_component.initialize()
            
            # Atomic replacement
            self.components[name] = new_component
            
    def get_component(self, name: str):
        return self.components[name]
```

## Upgrade Strategies

### 1. Blue-Green Deployment
```python
# Deploy new component alongside existing
new_tokenizer = HuggingFaceTokenizer("gpt2")
await component_manager.stage_component("tokenizer", new_tokenizer)

# Test new component
test_results = await run_component_tests("tokenizer")

# Switch traffic to new component
if test_results.passed:
    await component_manager.promote_component("tokenizer")
```

### 2. Canary Deployment
```python
# Route percentage of traffic to new component
await component_manager.canary_deploy(
    component="embedder",
    new_implementation=OpenAIEmbedder(),
    traffic_percentage=10
)
```

### 3. A/B Testing
```python
# Compare component performance
await component_manager.ab_test(
    component="retriever", 
    variant_a=InMemoryRetriever(),
    variant_b=PineconeRetriever(),
    metrics=["latency", "accuracy", "cost"]
)
```

## Configuration-Driven Upgrades

### Environment-Based Component Selection
```bash
# Development
MGAIF_TOKENIZER=simple
MGAIF_EMBEDDER=simple  
MGAIF_RETRIEVER=memory
MGAIF_LLM=echo

# Staging
MGAIF_TOKENIZER=huggingface
MGAIF_EMBEDDER=sentence_bert
MGAIF_RETRIEVER=chroma
MGAIF_LLM=ollama

# Production
MGAIF_TOKENIZER=custom_optimized
MGAIF_EMBEDDER=openai
MGAIF_RETRIEVER=pinecone
MGAIF_LLM=openai_gpt4
```

### YAML-Based Component Configuration
```yaml
# config/components.yaml
components:
  tokenizer:
    type: "huggingface"
    config:
      model: "gpt2"
      max_length: 512
      
  embedder:
    type: "openai"
    config:
      model: "text-embedding-3-large"
      dimensions: 1536
      
  retriever:
    type: "pinecone"
    config:
      index_name: "production-docs"
      namespace: "default"
      
  llm:
    type: "openai"
    config:
      model: "gpt-4-turbo"
      temperature: 0.1
      max_tokens: 2048
```

## Monitoring and Observability

### Component Health Monitoring
```python
@app.get("/health/components")
async def component_health():
    return {
        "tokenizer": await health_check(current_tokenizer),
        "embedder": await health_check(current_embedder), 
        "retriever": await health_check(current_retriever),
        "llm": await health_check(current_llm)
    }
```

### Performance Metrics
```python
# Component-specific metrics
TOKENIZER_LATENCY = Histogram("tokenizer_latency_seconds")
EMBEDDER_THROUGHPUT = Counter("embedder_requests_total")
RETRIEVER_ACCURACY = Gauge("retriever_accuracy_score")
LLM_COST = Counter("llm_cost_dollars")
```

## Migration Guides

### Upgrading from Simple to Production Components

1. **Tokenizer Migration**:
   ```python
   # 1. Install new tokenizer
   pip install transformers
   
   # 2. Update configuration
   export MGAIF_TOKENIZER=huggingface
   
   # 3. Restart service (or hot-swap)
   await app.replace_component("tokenizer", HuggingFaceTokenizer())
   ```

2. **Embedder Migration**:
   ```python
   # 1. Prepare new embedder
   new_embedder = OpenAIEmbedder(api_key=api_key)
   
   # 2. Re-index existing documents
   await migrate_embeddings(old_embedder, new_embedder)
   
   # 3. Switch to new embedder
   await app.replace_component("embedder", new_embedder)
   ```

3. **Database Migration**:
   ```python
   # 1. Set up new vector database
   new_store = PineconeVectorStore(index_name="production")
   
   # 2. Migrate existing vectors
   await migrate_vectors(old_store, new_store)
   
   # 3. Update retriever to use new store
   new_retriever = PineconeRetriever(store=new_store)
   await app.replace_component("retriever", new_retriever)
   ```

## Best Practices

### 1. Component Versioning
- Use semantic versioning for component implementations
- Maintain backward compatibility in interfaces
- Document breaking changes clearly

### 2. Testing Strategy
- Unit tests for each component implementation
- Integration tests for component interactions
- Performance benchmarks for upgrade validation

### 3. Rollback Strategy
- Always maintain previous component version
- Implement automatic rollback on failure
- Monitor key metrics during upgrades

### 4. Documentation
- Document component interfaces clearly
- Provide migration guides for upgrades
- Maintain compatibility matrices

This MCP modular architecture ensures that M-GAIF can evolve and scale with changing requirements while maintaining system stability and performance.
