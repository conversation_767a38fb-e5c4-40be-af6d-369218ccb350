"""Common evaluation metrics implementation."""

from __future__ import annotations

import re
import math
from collections import Counter
from datetime import datetime
from typing import List, Dict, Any, Set

from .base import Metric, EvaluationResult, EvaluationError


class BLEUMetric(Metric):
    """BLEU (Bilingual Evaluation Understudy) metric.
    
    Measures n-gram overlap between predictions and references,
    commonly used for machine translation and text generation.
    
    Features:
    - Configurable n-gram orders (1-4)
    - Brevity penalty for short predictions
    - Smoothing for zero n-gram matches
    
    Example:
        >>> bleu = BLEUMetric(max_order=4)
        >>> result = bleu.compute(
        ...     predictions=["the cat sat on the mat"],
        ...     references=["a cat was sitting on the mat"]
        ... )
        >>> print(f"BLEU-4: {result.score:.3f}")
    """
    
    def __init__(self, max_order: int = 4, smooth: bool = True):
        """Initialize BLEU metric.
        
        Args:
            max_order: Maximum n-gram order to consider
            smooth: Whether to apply smoothing for zero counts
        """
        super().__init__(
            name=f"bleu_{max_order}",
            description=f"BLEU score with {max_order}-gram precision"
        )
        self.max_order = max_order
        self.smooth = smooth
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute BLEU score."""
        self.validate_inputs(predictions, references)
        
        if not references:
            raise EvaluationError("BLEU requires reference texts", self.name)
        
        total_matches = [0] * self.max_order
        total_possible = [0] * self.max_order
        total_pred_len = 0
        total_ref_len = 0
        
        for pred, ref in zip(predictions, references):
            pred_tokens = self._tokenize(pred)
            ref_tokens = self._tokenize(ref)
            
            total_pred_len += len(pred_tokens)
            total_ref_len += len(ref_tokens)
            
            # Calculate n-gram matches
            for order in range(1, self.max_order + 1):
                pred_ngrams = self._get_ngrams(pred_tokens, order)
                ref_ngrams = self._get_ngrams(ref_tokens, order)
                
                matches = sum((pred_ngrams & ref_ngrams).values())
                possible = max(len(pred_tokens) - order + 1, 0)
                
                total_matches[order - 1] += matches
                total_possible[order - 1] += possible
        
        # Calculate precision for each n-gram order
        precisions = []
        for order in range(self.max_order):
            if total_possible[order] == 0:
                precision = 0.0
            elif total_matches[order] == 0:
                precision = 1e-10 if self.smooth else 0.0
            else:
                precision = total_matches[order] / total_possible[order]
            
            precisions.append(precision)
        
        # Calculate geometric mean of precisions
        if all(p > 0 for p in precisions):
            geo_mean = math.exp(sum(math.log(p) for p in precisions) / len(precisions))
        else:
            geo_mean = 0.0
        
        # Apply brevity penalty
        if total_pred_len < total_ref_len:
            brevity_penalty = math.exp(1 - total_ref_len / total_pred_len)
        else:
            brevity_penalty = 1.0
        
        bleu_score = geo_mean * brevity_penalty
        
        return EvaluationResult(
            metric_name=self.name,
            score=bleu_score,
            details={
                "precisions": precisions,
                "brevity_penalty": brevity_penalty,
                "prediction_length": total_pred_len,
                "reference_length": total_ref_len,
                "n_gram_matches": total_matches,
                "n_gram_possible": total_possible
            },
            metadata={
                "max_order": self.max_order,
                "smooth": self.smooth,
                "sample_count": len(predictions)
            },
            timestamp=datetime.now()
        )
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization by whitespace and punctuation."""
        return re.findall(r'\w+|[^\w\s]', text.lower())
    
    def _get_ngrams(self, tokens: List[str], n: int) -> Counter:
        """Extract n-grams from tokens."""
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngrams.append(tuple(tokens[i:i + n]))
        return Counter(ngrams)


class ROUGEMetric(Metric):
    """ROUGE (Recall-Oriented Understudy for Gisting Evaluation) metric.
    
    Measures recall-based overlap between predictions and references,
    commonly used for summarization evaluation.
    
    Variants:
    - ROUGE-N: N-gram recall
    - ROUGE-L: Longest common subsequence
    - ROUGE-W: Weighted longest common subsequence
    
    Example:
        >>> rouge = ROUGEMetric(variant="rouge_l")
        >>> result = rouge.compute(
        ...     predictions=["the quick brown fox"],
        ...     references=["a quick brown fox jumps"]
        ... )
    """
    
    def __init__(self, variant: str = "rouge_1"):
        """Initialize ROUGE metric.
        
        Args:
            variant: ROUGE variant ("rouge_1", "rouge_2", "rouge_l")
        """
        super().__init__(
            name=variant,
            description=f"ROUGE {variant.split('_')[1].upper()} score"
        )
        self.variant = variant
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute ROUGE score."""
        self.validate_inputs(predictions, references)
        
        if not references:
            raise EvaluationError("ROUGE requires reference texts", self.name)
        
        if self.variant == "rouge_1":
            scores = [self._rouge_n(pred, ref, 1) for pred, ref in zip(predictions, references)]
        elif self.variant == "rouge_2":
            scores = [self._rouge_n(pred, ref, 2) for pred, ref in zip(predictions, references)]
        elif self.variant == "rouge_l":
            scores = [self._rouge_l(pred, ref) for pred, ref in zip(predictions, references)]
        else:
            raise EvaluationError(f"Unknown ROUGE variant: {self.variant}", self.name)
        
        avg_score = sum(scores) / len(scores)
        
        return EvaluationResult(
            metric_name=self.name,
            score=avg_score,
            details={
                "individual_scores": scores,
                "min_score": min(scores),
                "max_score": max(scores),
                "std_dev": self._std_dev(scores)
            },
            metadata={
                "variant": self.variant,
                "sample_count": len(predictions)
            },
            timestamp=datetime.now()
        )
    
    def _rouge_n(self, prediction: str, reference: str, n: int) -> float:
        """Calculate ROUGE-N score."""
        pred_tokens = self._tokenize(prediction)
        ref_tokens = self._tokenize(reference)
        
        pred_ngrams = self._get_ngrams(pred_tokens, n)
        ref_ngrams = self._get_ngrams(ref_tokens, n)
        
        if not ref_ngrams:
            return 0.0
        
        overlap = sum((pred_ngrams & ref_ngrams).values())
        total_ref_ngrams = sum(ref_ngrams.values())
        
        return overlap / total_ref_ngrams
    
    def _rouge_l(self, prediction: str, reference: str) -> float:
        """Calculate ROUGE-L score using longest common subsequence."""
        pred_tokens = self._tokenize(prediction)
        ref_tokens = self._tokenize(reference)
        
        lcs_length = self._lcs_length(pred_tokens, ref_tokens)
        
        if len(ref_tokens) == 0:
            return 0.0
        
        return lcs_length / len(ref_tokens)
    
    def _lcs_length(self, seq1: List[str], seq2: List[str]) -> int:
        """Calculate longest common subsequence length."""
        m, n = len(seq1), len(seq2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]
        
        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if seq1[i - 1] == seq2[j - 1]:
                    dp[i][j] = dp[i - 1][j - 1] + 1
                else:
                    dp[i][j] = max(dp[i - 1][j], dp[i][j - 1])
        
        return dp[m][n]
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization."""
        return re.findall(r'\w+', text.lower())
    
    def _get_ngrams(self, tokens: List[str], n: int) -> Counter:
        """Extract n-grams."""
        ngrams = []
        for i in range(len(tokens) - n + 1):
            ngrams.append(tuple(tokens[i:i + n]))
        return Counter(ngrams)
    
    def _std_dev(self, values: List[float]) -> float:
        """Calculate standard deviation."""
        if len(values) <= 1:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return math.sqrt(variance)


class AccuracyMetric(Metric):
    """Simple accuracy metric for classification tasks.
    
    Calculates the percentage of predictions that exactly
    match the reference labels.
    
    Example:
        >>> accuracy = AccuracyMetric()
        >>> result = accuracy.compute(
        ...     predictions=["A", "B", "C", "A"],
        ...     references=["A", "B", "B", "A"]
        ... )
        >>> print(f"Accuracy: {result.score:.1%}")  # 75.0%
    """
    
    def __init__(self, case_sensitive: bool = True):
        """Initialize accuracy metric.
        
        Args:
            case_sensitive: Whether to consider case in comparisons
        """
        super().__init__(
            name="accuracy",
            description="Exact match accuracy"
        )
        self.case_sensitive = case_sensitive
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute accuracy score."""
        self.validate_inputs(predictions, references)
        
        if not references:
            raise EvaluationError("Accuracy requires reference labels", self.name)
        
        correct = 0
        total = len(predictions)
        
        for pred, ref in zip(predictions, references):
            if not self.case_sensitive:
                pred = pred.lower()
                ref = ref.lower()
            
            if pred.strip() == ref.strip():
                correct += 1
        
        accuracy = correct / total if total > 0 else 0.0
        
        return EvaluationResult(
            metric_name=self.name,
            score=accuracy,
            details={
                "correct": correct,
                "total": total,
                "incorrect": total - correct,
                "error_rate": 1.0 - accuracy
            },
            metadata={
                "case_sensitive": self.case_sensitive,
                "sample_count": total
            },
            timestamp=datetime.now()
        )


class BERTScoreMetric(Metric):
    """BERTScore metric using contextual embeddings.
    
    Note: This is a simplified implementation. In production,
    use the official BERTScore library with pre-trained models.
    
    Example:
        >>> bert_score = BERTScoreMetric()
        >>> result = bert_score.compute(
        ...     predictions=["the cat is sleeping"],
        ...     references=["a cat is resting"]
        ... )
    """
    
    def __init__(self):
        """Initialize BERTScore metric."""
        super().__init__(
            name="bert_score",
            description="BERTScore using contextual embeddings"
        )
        # In production, initialize BERT model here
    
    def compute(self, predictions: List[str], references: List[str] = None,
               **kwargs) -> EvaluationResult:
        """Compute BERTScore (simplified implementation)."""
        self.validate_inputs(predictions, references)
        
        if not references:
            raise EvaluationError("BERTScore requires reference texts", self.name)
        
        # Simplified implementation using token overlap
        # In production, use actual BERT embeddings
        scores = []
        
        for pred, ref in zip(predictions, references):
            pred_tokens = set(self._tokenize(pred))
            ref_tokens = set(self._tokenize(ref))
            
            if not ref_tokens:
                scores.append(0.0)
                continue
            
            # Jaccard similarity as proxy for semantic similarity
            intersection = len(pred_tokens & ref_tokens)
            union = len(pred_tokens | ref_tokens)
            
            similarity = intersection / union if union > 0 else 0.0
            scores.append(similarity)
        
        avg_score = sum(scores) / len(scores)
        
        return EvaluationResult(
            metric_name=self.name,
            score=avg_score,
            details={
                "individual_scores": scores,
                "precision": avg_score,  # Simplified
                "recall": avg_score,     # Simplified
                "f1": avg_score          # Simplified
            },
            metadata={
                "implementation": "simplified",
                "sample_count": len(predictions)
            },
            timestamp=datetime.now()
        )
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization."""
        return re.findall(r'\w+', text.lower())
