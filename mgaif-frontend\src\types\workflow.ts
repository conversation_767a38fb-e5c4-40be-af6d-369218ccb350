import type { Node, Edge, Connection } from 'reactflow';

// Base Node Types
export interface BaseNodeData {
  label: string;
  description?: string;
  config: Record<string, any>;
  isValid?: boolean;
  errors?: string[];
}

// Specific Node Data Types
export interface LLMNodeData extends BaseNodeData {
  model: string;
  temperature: number;
  maxTokens: number;
  systemPrompt?: string;
  userPrompt: string;
}

export interface ToolNodeData extends BaseNodeData {
  toolType: 'web-search' | 'calculator' | 'file-reader' | 'api-call' | 'database-query';
  parameters: Record<string, any>;
}

export interface ConditionNodeData extends BaseNodeData {
  condition: string;
  operator: 'equals' | 'contains' | 'greater' | 'less' | 'exists';
  value: any;
}

export interface InputNodeData extends BaseNodeData {
  inputType: 'text' | 'file' | 'json' | 'number';
  required: boolean;
  defaultValue?: any;
  validation?: {
    minLength?: number;
    maxLength?: number;
    pattern?: string;
  };
}

export interface OutputNodeData extends BaseNodeData {
  outputType: 'text' | 'json' | 'file';
  format?: string;
}

// Union type for all node data
export type WorkflowNodeData = 
  | LLMNodeData 
  | ToolNodeData 
  | ConditionNodeData 
  | InputNodeData 
  | OutputNodeData;

// Extended Node type with our data
export interface WorkflowNode extends Node {
  data: WorkflowNodeData;
  type: 'llm' | 'tool' | 'condition' | 'input' | 'output';
}

// Extended Edge type
export interface WorkflowEdge extends Edge {
  animated?: boolean;
  style?: React.CSSProperties;
}

// Workflow Definition
export interface Workflow {
  id: string;
  name: string;
  description: string;
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  variables: WorkflowVariable[];
  settings: WorkflowSettings;
  createdAt: string;
  updatedAt: string;
  version: number;
  status: 'draft' | 'published' | 'archived';
}

// Workflow Variables
export interface WorkflowVariable {
  id: string;
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  value: any;
  description?: string;
  required: boolean;
}

// Workflow Settings
export interface WorkflowSettings {
  timeout: number; // in seconds
  retryAttempts: number;
  parallelExecution: boolean;
  errorHandling: 'stop' | 'continue' | 'retry';
  logging: boolean;
}

// Execution Types
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  startTime: string;
  endTime?: string;
  duration?: number;
  input: Record<string, any>;
  output?: Record<string, any>;
  logs: ExecutionLog[];
  error?: string;
}

export interface ExecutionLog {
  id: string;
  timestamp: string;
  nodeId: string;
  level: 'info' | 'warn' | 'error';
  message: string;
  data?: any;
}

// Node Templates
export interface NodeTemplate {
  id: string;
  type: WorkflowNode['type'];
  name: string;
  description: string;
  icon: string;
  category: 'input' | 'processing' | 'output' | 'logic' | 'tools';
  defaultData: Partial<WorkflowNodeData>;
  configSchema: NodeConfigSchema;
}

export interface NodeConfigSchema {
  properties: Record<string, {
    type: 'string' | 'number' | 'boolean' | 'select' | 'textarea';
    label: string;
    description?: string;
    required?: boolean;
    options?: Array<{ label: string; value: any }>;
    validation?: {
      min?: number;
      max?: number;
      pattern?: string;
    };
  }>;
}

// Workflow Builder State
export interface WorkflowBuilderState {
  workflow: Workflow | null;
  selectedNode: WorkflowNode | null;
  selectedEdge: WorkflowEdge | null;
  isExecuting: boolean;
  executionResult: WorkflowExecution | null;
  nodeTemplates: NodeTemplate[];
  zoom: number;
  viewport: { x: number; y: number };
}

// Connection Validation
export interface ConnectionRule {
  sourceType: WorkflowNode['type'];
  targetType: WorkflowNode['type'];
  allowed: boolean;
  message?: string;
}

// Export/Import Types
export interface WorkflowExport {
  workflow: Workflow;
  metadata: {
    exportedAt: string;
    version: string;
    platform: string;
  };
}

export interface WorkflowImport {
  file: File;
  validation: {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  };
}
