"""Advanced threat detection for M-GAIF security."""

from __future__ import annotations

import asyncio
import hashlib
import re
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set, Tuple

from .audit import AuditLogger


class ThreatLevel(Enum):
    """Threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ThreatType(Enum):
    """Types of security threats."""
    PROMPT_INJECTION = "prompt_injection"
    MODEL_EXTRACTION = "model_extraction"
    DATA_POISONING = "data_poisoning"
    ADVERSARIAL_INPUT = "adversarial_input"
    PII_EXPOSURE = "pii_exposure"
    RATE_LIMIT_VIOLATION = "rate_limit_violation"
    SUSPICIOUS_PATTERN = "suspicious_pattern"
    PRIVILEGE_ESCALATION = "privilege_escalation"


@dataclass
class ThreatDetection:
    """Result of threat detection analysis."""
    threat_type: ThreatType
    threat_level: ThreatLevel
    confidence: float
    description: str
    evidence: Dict[str, Any]
    mitigation_suggestions: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for logging."""
        return {
            "threat_type": self.threat_type.value,
            "threat_level": self.threat_level.value,
            "confidence": self.confidence,
            "description": self.description,
            "evidence": self.evidence,
            "mitigation_suggestions": self.mitigation_suggestions,
            "timestamp": self.timestamp.isoformat()
        }


class ThreatDetector(ABC):
    """Abstract base class for threat detectors."""
    
    def __init__(self, name: str, enabled: bool = True):
        """Initialize threat detector.
        
        Args:
            name: Detector name
            enabled: Whether detector is enabled
        """
        self.name = name
        self.enabled = enabled
        self.detection_count = 0
        self.last_detection = None
    
    @abstractmethod
    async def detect(self, input_data: Any, context: Dict[str, Any] = None) -> List[ThreatDetection]:
        """Detect threats in input data.
        
        Args:
            input_data: Data to analyze for threats
            context: Additional context for analysis
            
        Returns:
            List of detected threats
        """
        pass
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get detector performance metrics."""
        return {
            "name": self.name,
            "enabled": self.enabled,
            "detection_count": self.detection_count,
            "last_detection": self.last_detection.isoformat() if self.last_detection else None
        }


class PromptInjectionDetector(ThreatDetector):
    """Detects prompt injection attacks."""
    
    def __init__(self):
        """Initialize prompt injection detector."""
        super().__init__("prompt_injection")
        
        # Patterns for prompt injection detection
        self.injection_patterns = [
            # Direct instruction overrides
            r"(?i)ignore (all|any|previous) instructions?",
            r"(?i)forget (all|everything) (above|before)",
            r"(?i)disregard (all|any|previous) (instructions?|prompts?)",
            r"(?i)override (all|any|previous) (instructions?|commands?)",
            
            # Role manipulation
            r"(?i)you are now (a|an) (different|new)",
            r"(?i)act as (if you are|a|an)",
            r"(?i)pretend (to be|you are)",
            r"(?i)roleplay as",
            
            # System prompt extraction
            r"(?i)show me (your|the) (system|initial) (prompt|instructions?)",
            r"(?i)what (are|were) your (original|initial) instructions?",
            r"(?i)repeat (your|the) (system|initial) (prompt|instructions?)",
            
            # Jailbreaking attempts
            r"(?i)developer mode",
            r"(?i)jailbreak",
            r"(?i)unrestricted mode",
            r"(?i)bypass (all|any) (restrictions?|limitations?)",
            
            # Delimiter confusion
            r"[\"'`]{3,}",  # Triple quotes or backticks
            r"---+",        # Multiple dashes
            r"===+",        # Multiple equals
            
            # Encoding attempts
            r"(?i)base64",
            r"(?i)hex(adecimal)?",
            r"(?i)url.?encod",
            r"(?i)rot13",
        ]
        
        self.compiled_patterns = [re.compile(pattern) for pattern in self.injection_patterns]
    
    async def detect(self, input_data: Any, context: Dict[str, Any] = None) -> List[ThreatDetection]:
        """Detect prompt injection attempts."""
        if not self.enabled or not isinstance(input_data, str):
            return []
        
        detections = []
        text = str(input_data).lower()
        
        # Check for injection patterns
        for i, pattern in enumerate(self.compiled_patterns):
            matches = pattern.findall(text)
            if matches:
                confidence = min(0.9, 0.3 + len(matches) * 0.2)
                
                detection = ThreatDetection(
                    threat_type=ThreatType.PROMPT_INJECTION,
                    threat_level=ThreatLevel.HIGH if confidence > 0.7 else ThreatLevel.MEDIUM,
                    confidence=confidence,
                    description=f"Potential prompt injection detected: pattern {i+1}",
                    evidence={
                        "pattern_index": i,
                        "pattern": self.injection_patterns[i],
                        "matches": matches[:5],  # Limit matches for logging
                        "match_count": len(matches)
                    },
                    mitigation_suggestions=[
                        "Sanitize user input",
                        "Use input validation",
                        "Implement prompt templates",
                        "Add content filtering"
                    ],
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)
        
        # Check for suspicious character patterns
        suspicious_chars = len(re.findall(r'[^\w\s\.,!?;:\-\(\)]', input_data))
        if suspicious_chars > len(input_data) * 0.1:  # More than 10% suspicious chars
            detection = ThreatDetection(
                threat_type=ThreatType.ADVERSARIAL_INPUT,
                threat_level=ThreatLevel.MEDIUM,
                confidence=0.6,
                description="High concentration of suspicious characters detected",
                evidence={
                    "suspicious_char_count": suspicious_chars,
                    "total_chars": len(input_data),
                    "suspicious_ratio": suspicious_chars / len(input_data)
                },
                mitigation_suggestions=[
                    "Character filtering",
                    "Input normalization",
                    "Encoding validation"
                ],
                timestamp=datetime.utcnow()
            )
            detections.append(detection)
        
        if detections:
            self.detection_count += len(detections)
            self.last_detection = datetime.utcnow()
        
        return detections


class PIIDetector(ThreatDetector):
    """Detects personally identifiable information."""
    
    def __init__(self):
        """Initialize PII detector."""
        super().__init__("pii_detection")
        
        # PII detection patterns
        self.pii_patterns = {
            "email": r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b',
            "phone": r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})',
            "ssn": r'\b\d{3}-?\d{2}-?\d{4}\b',
            "credit_card": r'\b(?:\d{4}[-\s]?){3}\d{4}\b',
            "ip_address": r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b',
            "url": r'https?://(?:[-\w.])+(?:[:\d]+)?(?:/(?:[\w/_.])*(?:\?(?:[\w&=%.])*)?(?:#(?:\w*))?)?'
        }
        
        self.compiled_pii_patterns = {
            name: re.compile(pattern, re.IGNORECASE)
            for name, pattern in self.pii_patterns.items()
        }
    
    async def detect(self, input_data: Any, context: Dict[str, Any] = None) -> List[ThreatDetection]:
        """Detect PII in input data."""
        if not self.enabled or not isinstance(input_data, str):
            return []
        
        detections = []
        
        for pii_type, pattern in self.compiled_pii_patterns.items():
            matches = pattern.findall(input_data)
            if matches:
                # Flatten matches if they're tuples (from groups)
                if matches and isinstance(matches[0], tuple):
                    matches = [''.join(match) for match in matches]
                
                confidence = min(0.95, 0.7 + len(matches) * 0.1)
                threat_level = ThreatLevel.HIGH if pii_type in ["ssn", "credit_card"] else ThreatLevel.MEDIUM
                
                detection = ThreatDetection(
                    threat_type=ThreatType.PII_EXPOSURE,
                    threat_level=threat_level,
                    confidence=confidence,
                    description=f"Potential {pii_type.upper()} detected in input",
                    evidence={
                        "pii_type": pii_type,
                        "match_count": len(matches),
                        "matches": ["***REDACTED***"] * len(matches)  # Don't log actual PII
                    },
                    mitigation_suggestions=[
                        "Redact PII from logs",
                        "Implement data anonymization",
                        "Add PII filtering",
                        "Review data handling policies"
                    ],
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)
        
        if detections:
            self.detection_count += len(detections)
            self.last_detection = datetime.utcnow()
        
        return detections


class ModelExtractionDetector(ThreatDetector):
    """Detects model extraction attempts."""
    
    def __init__(self):
        """Initialize model extraction detector."""
        super().__init__("model_extraction")
        self.query_patterns = {}
        self.user_query_counts = {}
        self.suspicious_patterns = [
            r"(?i)what (is|are) your (parameters?|weights?)",
            r"(?i)show me your (model|architecture)",
            r"(?i)how (many|much) parameters?",
            r"(?i)what (model|architecture) are you",
            r"(?i)(training|fine.?tun|gradient)",
            r"(?i)(logits?|probabilities|confidence scores?)"
        ]
        self.compiled_patterns = [re.compile(pattern) for pattern in self.suspicious_patterns]
    
    async def detect(self, input_data: Any, context: Dict[str, Any] = None) -> List[ThreatDetection]:
        """Detect model extraction attempts."""
        if not self.enabled or not isinstance(input_data, str):
            return []
        
        detections = []
        user_id = context.get("user_id", "anonymous") if context else "anonymous"
        
        # Check for suspicious patterns
        for pattern in self.compiled_patterns:
            if pattern.search(input_data):
                detection = ThreatDetection(
                    threat_type=ThreatType.MODEL_EXTRACTION,
                    threat_level=ThreatLevel.HIGH,
                    confidence=0.8,
                    description="Potential model extraction attempt detected",
                    evidence={
                        "user_id": user_id,
                        "suspicious_query": True,
                        "pattern_matched": True
                    },
                    mitigation_suggestions=[
                        "Block model introspection queries",
                        "Monitor user behavior",
                        "Implement query filtering",
                        "Add rate limiting"
                    ],
                    timestamp=datetime.utcnow()
                )
                detections.append(detection)
                break
        
        # Track query patterns for behavioral analysis
        query_hash = hashlib.md5(input_data.encode()).hexdigest()
        if user_id not in self.query_patterns:
            self.query_patterns[user_id] = {}
            self.user_query_counts[user_id] = 0
        
        self.user_query_counts[user_id] += 1
        self.query_patterns[user_id][query_hash] = self.query_patterns[user_id].get(query_hash, 0) + 1
        
        # Check for repeated queries (potential automated extraction)
        if self.query_patterns[user_id][query_hash] > 5:
            detection = ThreatDetection(
                threat_type=ThreatType.MODEL_EXTRACTION,
                threat_level=ThreatLevel.MEDIUM,
                confidence=0.7,
                description="Repeated identical queries detected",
                evidence={
                    "user_id": user_id,
                    "query_repetitions": self.query_patterns[user_id][query_hash],
                    "total_queries": self.user_query_counts[user_id]
                },
                mitigation_suggestions=[
                    "Implement query deduplication",
                    "Add rate limiting",
                    "Monitor user behavior patterns"
                ],
                timestamp=datetime.utcnow()
            )
            detections.append(detection)
        
        if detections:
            self.detection_count += len(detections)
            self.last_detection = datetime.utcnow()
        
        return detections


class ThreatDetectionEngine:
    """Central threat detection engine."""
    
    def __init__(self):
        """Initialize threat detection engine."""
        self.detectors: List[ThreatDetector] = []
        self.audit_logger = AuditLogger()
        self.detection_history: List[ThreatDetection] = []
        self.max_history = 10000
        
        # Initialize default detectors
        self.add_detector(PromptInjectionDetector())
        self.add_detector(PIIDetector())
        self.add_detector(ModelExtractionDetector())
    
    def add_detector(self, detector: ThreatDetector) -> None:
        """Add a threat detector."""
        self.detectors.append(detector)
    
    def remove_detector(self, detector_name: str) -> bool:
        """Remove a threat detector by name."""
        for i, detector in enumerate(self.detectors):
            if detector.name == detector_name:
                del self.detectors[i]
                return True
        return False
    
    async def analyze_threats(self, input_data: Any, context: Dict[str, Any] = None) -> List[ThreatDetection]:
        """Analyze input for all types of threats."""
        all_detections = []
        
        # Run all enabled detectors
        for detector in self.detectors:
            if detector.enabled:
                try:
                    detections = await detector.detect(input_data, context)
                    all_detections.extend(detections)
                except Exception as e:
                    await self.audit_logger.log_security_event(
                        "detector_error",
                        context.get("user_id", "unknown") if context else "unknown",
                        {
                            "detector": detector.name,
                            "error": str(e)
                        }
                    )
        
        # Log all detections
        for detection in all_detections:
            await self.audit_logger.log_security_event(
                "threat_detected",
                context.get("user_id", "unknown") if context else "unknown",
                detection.to_dict()
            )
        
        # Store in history
        self.detection_history.extend(all_detections)
        if len(self.detection_history) > self.max_history:
            self.detection_history = self.detection_history[-self.max_history:]
        
        return all_detections
    
    def get_detector_metrics(self) -> Dict[str, Dict[str, Any]]:
        """Get metrics for all detectors."""
        return {detector.name: detector.get_metrics() for detector in self.detectors}
    
    def get_threat_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get threat detection summary for specified time period."""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        recent_detections = [
            d for d in self.detection_history
            if d.timestamp >= cutoff_time
        ]
        
        # Group by threat type
        threat_counts = {}
        threat_levels = {}
        
        for detection in recent_detections:
            threat_type = detection.threat_type.value
            threat_level = detection.threat_level.value
            
            threat_counts[threat_type] = threat_counts.get(threat_type, 0) + 1
            if threat_type not in threat_levels:
                threat_levels[threat_type] = {}
            threat_levels[threat_type][threat_level] = threat_levels[threat_type].get(threat_level, 0) + 1
        
        return {
            "time_period_hours": hours,
            "total_detections": len(recent_detections),
            "threat_counts": threat_counts,
            "threat_levels": threat_levels,
            "detector_metrics": self.get_detector_metrics()
        }
