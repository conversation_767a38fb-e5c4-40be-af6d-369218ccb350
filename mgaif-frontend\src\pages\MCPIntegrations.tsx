import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Steps,
  Row,
  Col,
  Divider,
  message,
  Tag,
  Tooltip,
  Tabs,
  Switch,
  Alert,
  Badge,
  List,
  Avatar,
} from 'antd';
import {
  PlusOutlined,
  ApiOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  StopOutlined,
  DeleteOutlined,
  EyeOutlined,
  EditOutlined,
  LinkOutlined,
  DatabaseOutlined,
  CloudOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { api } from '../services/api';
import type { MCPIntegration, MCPConfiguration, MCPParameter } from '../types';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Step } = Steps;

export const MCPIntegrations: React.FC = () => {
  const [integrations, setIntegrations] = useState<MCPIntegration[]>([]);
  const [currentIntegration, setCurrentIntegration] = useState<MCPIntegration | null>(null);
  const [loading, setLoading] = useState(false);
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [configureModalVisible, setConfigureModalVisible] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [activeTab, setActiveTab] = useState('integrations');
  
  const [form] = Form.useForm();
  const [configForm] = Form.useForm();
  const [testForm] = Form.useForm();

  // Load data on component mount
  useEffect(() => {
    loadIntegrations();
  }, []);

  const loadIntegrations = async () => {
    try {
      setLoading(true);
      // Mock data for development
      setIntegrations([
        {
          id: '1',
          name: 'Slack Bot Integration',
          description: 'Connect to Slack workspace for automated responses and notifications',
          toolType: 'external-service',
          configuration: {
            endpoint: 'https://hooks.slack.com/services/...',
            authentication: {
              type: 'bearer',
              credentials: { token: 'xoxb-...' }
            },
            parameters: [
              {
                name: 'channel',
                type: 'string',
                required: true,
                description: 'Slack channel to send message to',
                defaultValue: '#general'
              },
              {
                name: 'message',
                type: 'string',
                required: true,
                description: 'Message content to send'
              }
            ],
            responseMapping: {
              success: 'ok',
              message: 'text'
            },
            errorHandling: {
              retries: 3,
              timeout: 5000,
              fallback: 'Failed to send Slack message'
            }
          },
          status: 'active',
          createdAt: '2024-01-15T10:30:00Z',
          updatedAt: '2024-01-16T14:20:00Z'
        },
        {
          id: '2',
          name: 'Database Query Tool',
          description: 'Execute SQL queries against PostgreSQL database',
          toolType: 'database',
          configuration: {
            endpoint: 'postgresql://localhost:5432/mydb',
            authentication: {
              type: 'basic',
              credentials: { username: 'user', password: '***' }
            },
            parameters: [
              {
                name: 'query',
                type: 'string',
                required: true,
                description: 'SQL query to execute'
              },
              {
                name: 'limit',
                type: 'number',
                required: false,
                description: 'Maximum number of rows to return',
                defaultValue: 100
              }
            ],
            errorHandling: {
              retries: 2,
              timeout: 10000,
              fallback: 'Database query failed'
            }
          },
          status: 'testing',
          createdAt: '2024-01-14T15:45:00Z',
          updatedAt: '2024-01-16T09:20:00Z'
        },
        {
          id: '3',
          name: 'File System Browser',
          description: 'Browse and manipulate files on the local file system',
          toolType: 'file-system',
          configuration: {
            parameters: [
              {
                name: 'path',
                type: 'string',
                required: true,
                description: 'File or directory path'
              },
              {
                name: 'operation',
                type: 'string',
                required: true,
                description: 'Operation to perform (read, write, list, delete)'
              }
            ],
            errorHandling: {
              retries: 1,
              timeout: 3000,
              fallback: 'File operation failed'
            }
          },
          status: 'draft',
          createdAt: '2024-01-13T12:00:00Z',
          updatedAt: '2024-01-13T12:00:00Z'
        }
      ]);
    } catch (error) {
      console.error('Failed to load MCP integrations:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateIntegration = async () => {
    try {
      const values = await form.validateFields();
      
      const newIntegration: Partial<MCPIntegration> = {
        name: values.name,
        description: values.description,
        toolType: values.toolType,
        configuration: {
          parameters: [],
          errorHandling: {
            retries: 3,
            timeout: 5000,
            fallback: 'Integration failed'
          }
        },
        status: 'draft'
      };

      // In real implementation, call API
      // await api.mcp.createIntegration(newIntegration);
      
      message.success('MCP integration created successfully!');
      setCreateModalVisible(false);
      form.resetFields();
      loadIntegrations();
    } catch (error) {
      message.error('Failed to create MCP integration');
    }
  };

  const handleConfigureIntegration = async () => {
    try {
      const values = await configForm.validateFields();
      
      if (!currentIntegration) return;

      const updatedConfig: MCPConfiguration = {
        endpoint: values.endpoint,
        authentication: {
          type: values.authType,
          credentials: values.credentials ? JSON.parse(values.credentials) : {}
        },
        parameters: values.parameters || [],
        responseMapping: values.responseMapping ? JSON.parse(values.responseMapping) : {},
        errorHandling: {
          retries: values.retries || 3,
          timeout: values.timeout || 5000,
          fallback: values.fallback || 'Integration failed'
        }
      };

      // In real implementation, call API
      // await api.mcp.updateConfiguration(currentIntegration.id, updatedConfig);
      
      message.success('Integration configured successfully!');
      setConfigureModalVisible(false);
      configForm.resetFields();
      loadIntegrations();
    } catch (error) {
      message.error('Failed to configure integration');
    }
  };

  const handleTestIntegration = async () => {
    try {
      const values = await testForm.validateFields();
      
      if (!currentIntegration) return;

      // In real implementation, call API
      // const result = await api.mcp.testIntegration(currentIntegration.id, values);
      
      message.success('Integration test completed successfully!');
      // Show test results in modal or separate panel
    } catch (error) {
      message.error('Integration test failed');
    }
  };

  const handleToggleStatus = async (integration: MCPIntegration) => {
    try {
      const newStatus = integration.status === 'active' ? 'inactive' : 'active';
      
      // In real implementation, call API
      // await api.mcp.updateStatus(integration.id, newStatus);
      
      message.success(`Integration ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`);
      loadIntegrations();
    } catch (error) {
      message.error('Failed to update integration status');
    }
  };

  const integrationColumns: TableColumnsType<MCPIntegration> = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <Space>
          <Avatar icon={getToolIcon(record.toolType)} />
          <div>
            <Text strong>{name}</Text>
            <br />
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {record.description}
            </Text>
          </div>
        </Space>
      ),
    },
    {
      title: 'Type',
      dataIndex: 'toolType',
      key: 'toolType',
      render: (type) => {
        const colors = {
          'web-api': 'blue',
          'database': 'green',
          'file-system': 'orange',
          'external-service': 'purple'
        };
        return <Tag color={colors[type as keyof typeof colors]}>{type.replace('-', ' ').toUpperCase()}</Tag>;
      },
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        const colors = {
          draft: 'default',
          testing: 'processing',
          active: 'success',
          inactive: 'error'
        };
        
        return (
          <div>
            <Badge 
              status={status === 'active' ? 'success' : status === 'testing' ? 'processing' : 'default'} 
              text={status.toUpperCase()} 
            />
            {status === 'active' && (
              <div style={{ marginTop: '4px' }}>
                <Text type="secondary" style={{ fontSize: '11px' }}>
                  Last used: 2 hours ago
                </Text>
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Parameters',
      key: 'parameters',
      render: (_, record) => `${record.configuration.parameters?.length || 0} params`,
    },
    {
      title: 'Updated',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (date) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Configure">
            <Button 
              icon={<SettingOutlined />} 
              size="small"
              onClick={() => {
                setCurrentIntegration(record);
                setConfigureModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Test">
            <Button 
              icon={<PlayCircleOutlined />} 
              size="small"
              onClick={() => {
                setCurrentIntegration(record);
                setTestModalVisible(true);
              }}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? 'Deactivate' : 'Activate'}>
            <Button 
              icon={record.status === 'active' ? <StopOutlined /> : <CheckCircleOutlined />}
              size="small"
              type={record.status === 'active' ? 'default' : 'primary'}
              onClick={() => handleToggleStatus(record)}
              disabled={record.status === 'draft'}
            />
          </Tooltip>
          <Tooltip title="Delete">
            <Button 
              icon={<DeleteOutlined />} 
              size="small" 
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  function getToolIcon(toolType: string) {
    switch (toolType) {
      case 'web-api': return <ApiOutlined />;
      case 'database': return <DatabaseOutlined />;
      case 'file-system': return <CloudOutlined />;
      case 'external-service': return <LinkOutlined />;
      default: return <ToolOutlined />;
    }
  }

  const toolTemplates = [
    {
      id: 'slack',
      name: 'Slack Integration',
      description: 'Send messages and notifications to Slack channels',
      toolType: 'external-service',
      icon: <LinkOutlined />,
      popular: true
    },
    {
      id: 'database',
      name: 'Database Query',
      description: 'Execute SQL queries against various databases',
      toolType: 'database',
      icon: <DatabaseOutlined />,
      popular: true
    },
    {
      id: 'webhook',
      name: 'Webhook Trigger',
      description: 'Send HTTP requests to external webhooks',
      toolType: 'web-api',
      icon: <ApiOutlined />,
      popular: false
    },
    {
      id: 'file-system',
      name: 'File Operations',
      description: 'Read, write, and manage files on the system',
      toolType: 'file-system',
      icon: <CloudOutlined />,
      popular: false
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* Header */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>MCP Integrations</Title>
        <Paragraph type="secondary">
          Create Model Context Protocol integrations to connect external tools and services with your AI agents
        </Paragraph>
      </div>

      {/* Statistics */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#1890ff' }}>
                {integrations.length}
              </Title>
              <Text type="secondary">Total Integrations</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
                {integrations.filter(i => i.status === 'active').length}
              </Title>
              <Text type="secondary">Active</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#faad14' }}>
                {integrations.filter(i => i.status === 'testing').length}
              </Title>
              <Text type="secondary">Testing</Text>
            </div>
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Title level={3} style={{ margin: 0, color: '#f5222d' }}>
                {integrations.filter(i => i.status === 'draft').length}
              </Title>
              <Text type="secondary">Draft</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Main Content */}
      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="My Integrations" key="integrations">
            <div style={{ marginBottom: '16px', display: 'flex', justifyContent: 'space-between' }}>
              <Title level={4}>Your MCP Integrations</Title>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setCreateModalVisible(true)}
              >
                Create Integration
              </Button>
            </div>

            <Table
              columns={integrationColumns}
              dataSource={integrations}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          
          <TabPane tab="Templates" key="templates">
            <div style={{ marginBottom: '16px' }}>
              <Title level={4}>Integration Templates</Title>
              <Paragraph type="secondary">
                Start with pre-built templates for common integrations
              </Paragraph>
            </div>

            <Row gutter={16}>
              {toolTemplates.map(template => (
                <Col span={6} key={template.id} style={{ marginBottom: '16px' }}>
                  <Card
                    hoverable
                    actions={[
                      <Button 
                        type="primary" 
                        size="small"
                        onClick={() => {
                          form.setFieldsValue({
                            name: template.name,
                            description: template.description,
                            toolType: template.toolType
                          });
                          setCreateModalVisible(true);
                        }}
                      >
                        Use Template
                      </Button>
                    ]}
                  >
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '32px', marginBottom: '12px' }}>
                        {template.icon}
                      </div>
                      <Title level={5} style={{ marginBottom: '8px' }}>
                        {template.name}
                        {template.popular && (
                          <Badge count="Popular" style={{ marginLeft: '8px' }} />
                        )}
                      </Title>
                      <Paragraph type="secondary" style={{ fontSize: '12px', margin: 0 }}>
                        {template.description}
                      </Paragraph>
                    </div>
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>
          
          <TabPane tab="Documentation" key="documentation">
            <div>
              <Title level={4}>MCP Integration Guide</Title>
              
              <Alert
                message="Model Context Protocol (MCP)"
                description="MCP enables AI agents to securely connect to external tools and data sources. Create integrations to extend your agents' capabilities."
                type="info"
                showIcon
                style={{ marginBottom: '24px' }}
              />

              <Row gutter={16}>
                <Col span={12}>
                  <Card title="Getting Started" size="small">
                    <List
                      size="small"
                      dataSource={[
                        'Choose an integration template or create from scratch',
                        'Configure connection parameters and authentication',
                        'Test the integration to ensure it works correctly',
                        'Activate the integration for use in workflows',
                        'Monitor usage and performance metrics'
                      ]}
                      renderItem={(item, index) => (
                        <List.Item>
                          <Text>{index + 1}. {item}</Text>
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
                
                <Col span={12}>
                  <Card title="Best Practices" size="small">
                    <List
                      size="small"
                      dataSource={[
                        'Use descriptive names and clear documentation',
                        'Implement proper error handling and retries',
                        'Set appropriate timeouts for external calls',
                        'Test integrations thoroughly before activation',
                        'Monitor integration performance and logs'
                      ]}
                      renderItem={(item, index) => (
                        <List.Item>
                          <Text>• {item}</Text>
                        </List.Item>
                      )}
                    />
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* Create Integration Modal */}
      <Modal
        title="Create New MCP Integration"
        open={createModalVisible}
        onOk={handleCreateIntegration}
        onCancel={() => setCreateModalVisible(false)}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            label="Integration Name"
            name="name"
            rules={[{ required: true, message: 'Please enter integration name' }]}
          >
            <Input placeholder="Enter integration name" />
          </Form.Item>
          
          <Form.Item
            label="Description"
            name="description"
            rules={[{ required: true, message: 'Please enter description' }]}
          >
            <TextArea rows={3} placeholder="Describe what this integration does" />
          </Form.Item>
          
          <Form.Item
            label="Tool Type"
            name="toolType"
            rules={[{ required: true, message: 'Please select tool type' }]}
          >
            <Select placeholder="Select tool type">
              <Select.Option value="web-api">Web API</Select.Option>
              <Select.Option value="database">Database</Select.Option>
              <Select.Option value="file-system">File System</Select.Option>
              <Select.Option value="external-service">External Service</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* Configure Integration Modal */}
      <Modal
        title={`Configure ${currentIntegration?.name}`}
        open={configureModalVisible}
        onOk={handleConfigureIntegration}
        onCancel={() => setConfigureModalVisible(false)}
        width={800}
      >
        <Steps current={currentStep} style={{ marginBottom: '24px' }}>
          <Step title="Connection" />
          <Step title="Authentication" />
          <Step title="Parameters" />
          <Step title="Error Handling" />
        </Steps>
        
        <Form form={configForm} layout="vertical">
          {currentStep === 0 && (
            <>
              <Form.Item label="Endpoint URL" name="endpoint">
                <Input placeholder="https://api.example.com/endpoint" />
              </Form.Item>
            </>
          )}
          
          {currentStep === 1 && (
            <>
              <Form.Item label="Authentication Type" name="authType">
                <Select defaultValue="none">
                  <Select.Option value="none">None</Select.Option>
                  <Select.Option value="api-key">API Key</Select.Option>
                  <Select.Option value="bearer">Bearer Token</Select.Option>
                  <Select.Option value="basic">Basic Auth</Select.Option>
                </Select>
              </Form.Item>
              
              <Form.Item label="Credentials (JSON)" name="credentials">
                <TextArea 
                  rows={3} 
                  placeholder='{"token": "your-token", "key": "your-key"}'
                />
              </Form.Item>
            </>
          )}
          
          {currentStep === 2 && (
            <>
              <Form.Item label="Response Mapping (JSON)" name="responseMapping">
                <TextArea 
                  rows={4} 
                  placeholder='{"success": "ok", "data": "result", "error": "message"}'
                />
              </Form.Item>
            </>
          )}
          
          {currentStep === 3 && (
            <>
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item label="Retries" name="retries">
                    <Input type="number" defaultValue={3} min={0} max={10} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Timeout (ms)" name="timeout">
                    <Input type="number" defaultValue={5000} min={1000} max={30000} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item label="Fallback Message" name="fallback">
                    <Input placeholder="Integration failed" />
                  </Form.Item>
                </Col>
              </Row>
            </>
          )}
        </Form>
        
        <div style={{ marginTop: '24px', textAlign: 'right' }}>
          <Space>
            <Button 
              disabled={currentStep === 0}
              onClick={() => setCurrentStep(currentStep - 1)}
            >
              Previous
            </Button>
            <Button 
              type="primary"
              onClick={() => {
                if (currentStep < 3) {
                  setCurrentStep(currentStep + 1);
                } else {
                  handleConfigureIntegration();
                }
              }}
            >
              {currentStep < 3 ? 'Next' : 'Save Configuration'}
            </Button>
          </Space>
        </div>
      </Modal>

      {/* Test Integration Modal */}
      <Modal
        title={`Test ${currentIntegration?.name}`}
        open={testModalVisible}
        onOk={handleTestIntegration}
        onCancel={() => setTestModalVisible(false)}
        width={600}
      >
        <Form form={testForm} layout="vertical">
          <Form.Item label="Test Parameters (JSON)">
            <TextArea 
              rows={6} 
              placeholder='{"param1": "value1", "param2": "value2"}'
            />
          </Form.Item>
          
          <Alert
            message="Test Mode"
            description="This will execute the integration with the provided parameters. Make sure the parameters are valid and safe to execute."
            type="warning"
            showIcon
          />
        </Form>
      </Modal>
    </div>
  );
};
