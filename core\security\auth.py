"""Authentication and authorization system for M-GAIF."""

from __future__ import annotations

import asyncio
import hashlib
import hmac
import jwt
import secrets
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from .audit import AuditLogger


class UserRole(Enum):
    """User roles with hierarchical permissions."""
    ADMIN = "admin"
    DEVELOPER = "developer"
    ANALYST = "analyst"
    VIEWER = "viewer"
    API_USER = "api_user"


class Permission(Enum):
    """System permissions."""
    # Component management
    PLUGIN_MANAGE = "plugin:manage"
    PLUGIN_VIEW = "plugin:view"
    COMPONENT_SWAP = "component:swap"
    
    # Agent operations
    AGENT_CREATE = "agent:create"
    AGENT_EXECUTE = "agent:execute"
    AGENT_VIEW = "agent:view"
    
    # Workflow operations
    WORKFLOW_CREATE = "workflow:create"
    WORKFLOW_EXECUTE = "workflow:execute"
    WORKFLOW_VIEW = "workflow:view"
    
    # Evaluation operations
    EVALUATION_RUN = "evaluation:run"
    EVALUATION_VIEW = "evaluation:view"
    
    # System operations
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    AUDIT_VIEW = "audit:view"


@dataclass
class User:
    """User account information."""
    id: str
    username: str
    email: str
    roles: List[UserRole]
    permissions: Set[Permission]
    created_at: datetime
    last_login: Optional[datetime] = None
    is_active: bool = True
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if user has specific permission."""
        return permission in self.permissions
    
    def has_role(self, role: UserRole) -> bool:
        """Check if user has specific role."""
        return role in self.roles


@dataclass
class AuthToken:
    """Authentication token information."""
    token: str
    user_id: str
    expires_at: datetime
    scopes: List[str]
    token_type: str = "bearer"
    
    def is_expired(self) -> bool:
        """Check if token is expired."""
        return datetime.utcnow() > self.expires_at
    
    def is_valid_for_scope(self, scope: str) -> bool:
        """Check if token is valid for specific scope."""
        return not self.is_expired() and (not self.scopes or scope in self.scopes)


class AuthenticationError(Exception):
    """Authentication-related errors."""
    pass


class AuthorizationError(Exception):
    """Authorization-related errors."""
    pass


class AuthProvider(ABC):
    """Abstract authentication provider."""
    
    @abstractmethod
    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[User]:
        """Authenticate user with credentials."""
        pass
    
    @abstractmethod
    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        pass
    
    @abstractmethod
    async def create_token(self, user: User, scopes: List[str] = None) -> AuthToken:
        """Create authentication token for user."""
        pass
    
    @abstractmethod
    async def validate_token(self, token: str) -> Optional[AuthToken]:
        """Validate authentication token."""
        pass


class JWTAuthProvider(AuthProvider):
    """JWT-based authentication provider."""
    
    def __init__(self, secret_key: str, algorithm: str = "HS256", 
                 token_expiry_hours: int = 24):
        """Initialize JWT auth provider.
        
        Args:
            secret_key: Secret key for JWT signing
            algorithm: JWT algorithm to use
            token_expiry_hours: Token expiry time in hours
        """
        self.secret_key = secret_key
        self.algorithm = algorithm
        self.token_expiry_hours = token_expiry_hours
        self.users: Dict[str, User] = {}
        self.user_credentials: Dict[str, str] = {}  # username -> password_hash
        self.audit_logger = AuditLogger()
        
        # Default role permissions
        self.role_permissions = {
            UserRole.ADMIN: {
                Permission.PLUGIN_MANAGE, Permission.PLUGIN_VIEW,
                Permission.COMPONENT_SWAP, Permission.AGENT_CREATE,
                Permission.AGENT_EXECUTE, Permission.AGENT_VIEW,
                Permission.WORKFLOW_CREATE, Permission.WORKFLOW_EXECUTE,
                Permission.WORKFLOW_VIEW, Permission.EVALUATION_RUN,
                Permission.EVALUATION_VIEW, Permission.SYSTEM_CONFIG,
                Permission.SYSTEM_MONITOR, Permission.AUDIT_VIEW
            },
            UserRole.DEVELOPER: {
                Permission.PLUGIN_VIEW, Permission.AGENT_CREATE,
                Permission.AGENT_EXECUTE, Permission.AGENT_VIEW,
                Permission.WORKFLOW_CREATE, Permission.WORKFLOW_EXECUTE,
                Permission.WORKFLOW_VIEW, Permission.EVALUATION_RUN,
                Permission.EVALUATION_VIEW, Permission.SYSTEM_MONITOR
            },
            UserRole.ANALYST: {
                Permission.AGENT_EXECUTE, Permission.AGENT_VIEW,
                Permission.WORKFLOW_EXECUTE, Permission.WORKFLOW_VIEW,
                Permission.EVALUATION_RUN, Permission.EVALUATION_VIEW
            },
            UserRole.VIEWER: {
                Permission.PLUGIN_VIEW, Permission.AGENT_VIEW,
                Permission.WORKFLOW_VIEW, Permission.EVALUATION_VIEW
            },
            UserRole.API_USER: {
                Permission.AGENT_EXECUTE, Permission.WORKFLOW_EXECUTE,
                Permission.EVALUATION_RUN
            }
        }
    
    async def authenticate(self, credentials: Dict[str, Any]) -> Optional[User]:
        """Authenticate user with username/password."""
        username = credentials.get("username")
        password = credentials.get("password")
        
        if not username or not password:
            await self.audit_logger.log_auth_event(
                "authentication_failed",
                username or "unknown",
                {"reason": "missing_credentials"}
            )
            return None
        
        # Check if user exists and password is correct
        if username not in self.user_credentials:
            await self.audit_logger.log_auth_event(
                "authentication_failed",
                username,
                {"reason": "user_not_found"}
            )
            return None
        
        stored_hash = self.user_credentials[username]
        if not self._verify_password(password, stored_hash):
            await self.audit_logger.log_auth_event(
                "authentication_failed",
                username,
                {"reason": "invalid_password"}
            )
            return None
        
        # Find user by username
        user = None
        for u in self.users.values():
            if u.username == username:
                user = u
                break
        
        if user and user.is_active:
            user.last_login = datetime.utcnow()
            await self.audit_logger.log_auth_event(
                "authentication_success",
                username,
                {"user_id": user.id}
            )
            return user
        
        await self.audit_logger.log_auth_event(
            "authentication_failed",
            username,
            {"reason": "user_inactive"}
        )
        return None
    
    async def get_user(self, user_id: str) -> Optional[User]:
        """Get user by ID."""
        return self.users.get(user_id)
    
    async def create_token(self, user: User, scopes: List[str] = None) -> AuthToken:
        """Create JWT token for user."""
        expires_at = datetime.utcnow() + timedelta(hours=self.token_expiry_hours)
        
        payload = {
            "user_id": user.id,
            "username": user.username,
            "roles": [role.value for role in user.roles],
            "scopes": scopes or [],
            "exp": expires_at.timestamp(),
            "iat": datetime.utcnow().timestamp(),
            "jti": secrets.token_urlsafe(16)  # JWT ID
        }
        
        token = jwt.encode(payload, self.secret_key, algorithm=self.algorithm)
        
        auth_token = AuthToken(
            token=token,
            user_id=user.id,
            expires_at=expires_at,
            scopes=scopes or []
        )
        
        await self.audit_logger.log_auth_event(
            "token_created",
            user.username,
            {
                "user_id": user.id,
                "expires_at": expires_at.isoformat(),
                "scopes": scopes or []
            }
        )
        
        return auth_token
    
    async def validate_token(self, token: str) -> Optional[AuthToken]:
        """Validate JWT token."""
        try:
            payload = jwt.decode(token, self.secret_key, algorithms=[self.algorithm])
            
            user_id = payload.get("user_id")
            expires_at = datetime.fromtimestamp(payload.get("exp", 0))
            scopes = payload.get("scopes", [])
            
            if datetime.utcnow() > expires_at:
                return None
            
            return AuthToken(
                token=token,
                user_id=user_id,
                expires_at=expires_at,
                scopes=scopes
            )
            
        except jwt.InvalidTokenError:
            return None
    
    async def create_user(self, username: str, email: str, password: str,
                         roles: List[UserRole]) -> User:
        """Create a new user account."""
        user_id = secrets.token_urlsafe(16)
        
        # Calculate permissions from roles
        permissions = set()
        for role in roles:
            permissions.update(self.role_permissions.get(role, set()))
        
        user = User(
            id=user_id,
            username=username,
            email=email,
            roles=roles,
            permissions=permissions,
            created_at=datetime.utcnow()
        )
        
        # Store user and password hash
        self.users[user_id] = user
        self.user_credentials[username] = self._hash_password(password)
        
        await self.audit_logger.log_auth_event(
            "user_created",
            username,
            {
                "user_id": user_id,
                "roles": [role.value for role in roles]
            }
        )
        
        return user
    
    def _hash_password(self, password: str) -> str:
        """Hash password with salt."""
        salt = secrets.token_bytes(32)
        pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
        return salt.hex() + pwdhash.hex()
    
    def _verify_password(self, password: str, stored_hash: str) -> bool:
        """Verify password against stored hash."""
        try:
            salt = bytes.fromhex(stored_hash[:64])
            stored_pwdhash = stored_hash[64:]
            pwdhash = hashlib.pbkdf2_hmac('sha256', password.encode('utf-8'), salt, 100000)
            return hmac.compare_digest(pwdhash.hex(), stored_pwdhash)
        except Exception:
            return False


class AuthManager:
    """Central authentication and authorization manager."""
    
    def __init__(self, auth_provider: AuthProvider):
        """Initialize auth manager with provider."""
        self.auth_provider = auth_provider
        self.active_sessions: Dict[str, AuthToken] = {}
        self.audit_logger = AuditLogger()
    
    async def authenticate_user(self, credentials: Dict[str, Any]) -> Optional[AuthToken]:
        """Authenticate user and create session token."""
        user = await self.auth_provider.authenticate(credentials)
        if not user:
            return None
        
        token = await self.auth_provider.create_token(user)
        self.active_sessions[token.token] = token
        
        return token
    
    async def validate_token(self, token: str) -> Optional[User]:
        """Validate token and return user."""
        # Check active sessions first
        if token in self.active_sessions:
            auth_token = self.active_sessions[token]
            if not auth_token.is_expired():
                return await self.auth_provider.get_user(auth_token.user_id)
            else:
                # Remove expired token
                del self.active_sessions[token]
        
        # Validate with provider
        auth_token = await self.auth_provider.validate_token(token)
        if auth_token:
            user = await self.auth_provider.get_user(auth_token.user_id)
            if user:
                self.active_sessions[token] = auth_token
                return user
        
        return None
    
    async def authorize_action(self, user: User, permission: Permission,
                              resource: str = None) -> bool:
        """Check if user is authorized for action."""
        if not user.is_active:
            await self.audit_logger.log_auth_event(
                "authorization_failed",
                user.username,
                {
                    "reason": "user_inactive",
                    "permission": permission.value,
                    "resource": resource
                }
            )
            return False
        
        has_permission = user.has_permission(permission)
        
        await self.audit_logger.log_auth_event(
            "authorization_check",
            user.username,
            {
                "permission": permission.value,
                "resource": resource,
                "granted": has_permission
            }
        )
        
        return has_permission
    
    async def logout_user(self, token: str) -> bool:
        """Logout user and invalidate token."""
        if token in self.active_sessions:
            auth_token = self.active_sessions[token]
            user = await self.auth_provider.get_user(auth_token.user_id)
            
            del self.active_sessions[token]
            
            if user:
                await self.audit_logger.log_auth_event(
                    "user_logout",
                    user.username,
                    {"user_id": user.id}
                )
            
            return True
        
        return False
    
    def get_active_sessions(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active sessions."""
        sessions = {}
        current_time = datetime.utcnow()
        
        for token, auth_token in self.active_sessions.items():
            if not auth_token.is_expired():
                sessions[token] = {
                    "user_id": auth_token.user_id,
                    "expires_at": auth_token.expires_at.isoformat(),
                    "scopes": auth_token.scopes,
                    "time_remaining": (auth_token.expires_at - current_time).total_seconds()
                }
        
        return sessions
