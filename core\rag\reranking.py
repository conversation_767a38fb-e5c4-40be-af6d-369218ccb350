"""Re-ranking systems for RAG pipeline.

This module implements various re-ranking strategies to improve the quality
of retrieved results by reordering them based on relevance to the query.

The re-ranking system provides:
- Multiple re-ranking algorithms (BM25, cross-encoder, LLM-based)
- Configurable scoring and fusion strategies
- Batch processing for efficiency
- Integration with retrieval results

Key Components:
- Reranker: Base re-ranking interface
- BM25Reranker: Statistical re-ranking using BM25
- CrossEncoderReranker: Neural re-ranking using cross-encoders
- LLMReranker: LLM-based relevance scoring

Example:
    >>> from core.rag import HybridRetriever, CrossEncoderReranker
    >>> retriever = HybridRetriever()
    >>> reranker = CrossEncoderReranker()
    >>> 
    >>> # Retrieve and re-rank
    >>> results = await retriever.retrieve("AI applications", top_k=20)
    >>> reranked = await reranker.rerank("AI applications", results, top_k=5)
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import numpy as np

from .retrieval import RetrievalResult
from .base import RAGError

logger = logging.getLogger(__name__)


class Reranker(ABC):
    """Base class for all re-ranking systems."""
    
    def __init__(self, top_k: int = 10):
        self.top_k = top_k
    
    @abstractmethod
    async def rerank(self, query: str, results: List[RetrievalResult], 
                    top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Re-rank retrieval results based on query relevance."""
        pass
    
    async def batch_rerank(self, queries: List[str], 
                          results_list: List[List[RetrievalResult]], 
                          top_k: Optional[int] = None) -> List[List[RetrievalResult]]:
        """Re-rank results for multiple queries."""
        tasks = [self.rerank(query, results, top_k) 
                for query, results in zip(queries, results_list)]
        return await asyncio.gather(*tasks)


class BM25Reranker(Reranker):
    """Re-ranking using BM25 algorithm for improved relevance scoring."""
    
    def __init__(self, k1: float = 1.2, b: float = 0.75, **kwargs):
        super().__init__(**kwargs)
        self.k1 = k1
        self.b = b
    
    def _tokenize(self, text: str) -> List[str]:
        """Simple tokenization."""
        import re
        tokens = re.findall(r'\b\w+\b', text.lower())
        return tokens
    
    def _compute_bm25_score(self, query_terms: List[str], document: str, 
                           corpus_stats: Dict[str, Any]) -> float:
        """Compute BM25 score for a document given query terms."""
        doc_tokens = self._tokenize(document)
        doc_length = len(doc_tokens)
        
        # Count term frequencies in document
        term_freq = {}
        for token in doc_tokens:
            term_freq[token] = term_freq.get(token, 0) + 1
        
        score = 0.0
        for term in query_terms:
            if term not in term_freq:
                continue
            
            tf = term_freq[term]
            df = corpus_stats['document_frequencies'].get(term, 1)
            corpus_size = corpus_stats['corpus_size']
            avg_doc_length = corpus_stats['avg_doc_length']
            
            # IDF component
            idf = np.log((corpus_size - df + 0.5) / (df + 0.5))
            
            # BM25 formula
            numerator = tf * (self.k1 + 1)
            denominator = tf + self.k1 * (1 - self.b + self.b * (doc_length / avg_doc_length))
            
            score += idf * (numerator / denominator)
        
        return score
    
    async def rerank(self, query: str, results: List[RetrievalResult], 
                    top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Re-rank results using BM25 scoring."""
        if not results:
            return results
        
        top_k = top_k or self.top_k
        query_terms = self._tokenize(query)
        
        if not query_terms:
            logger.warning("No valid query terms for re-ranking")
            return results[:top_k]
        
        try:
            # Compute corpus statistics
            documents = [result.chunk.content for result in results]
            all_tokens = []
            doc_lengths = []
            
            for doc in documents:
                tokens = self._tokenize(doc)
                all_tokens.extend(tokens)
                doc_lengths.append(len(tokens))
            
            # Document frequencies
            vocabulary = set(all_tokens)
            document_frequencies = {}
            for term in vocabulary:
                df = sum(1 for doc in documents if term in self._tokenize(doc))
                document_frequencies[term] = df
            
            corpus_stats = {
                'corpus_size': len(documents),
                'avg_doc_length': sum(doc_lengths) / len(doc_lengths) if doc_lengths else 0,
                'document_frequencies': document_frequencies
            }
            
            # Compute BM25 scores
            scored_results = []
            for result in results:
                bm25_score = self._compute_bm25_score(
                    query_terms, result.chunk.content, corpus_stats
                )
                
                # Create new result with BM25 score
                new_result = RetrievalResult(
                    chunk=result.chunk,
                    score=bm25_score,
                    retrieval_method="bm25_reranked",
                    metadata={
                        **result.metadata,
                        "original_score": result.score,
                        "original_method": result.retrieval_method,
                        "bm25_k1": self.k1,
                        "bm25_b": self.b
                    }
                )
                scored_results.append(new_result)
            
            # Sort by BM25 score and return top-k
            scored_results.sort(key=lambda x: x.score, reverse=True)
            reranked_results = scored_results[:top_k]
            
            logger.debug(f"Re-ranked {len(results)} results to top {len(reranked_results)}")
            return reranked_results
            
        except Exception as e:
            logger.error(f"BM25 re-ranking failed: {e}")
            # Fallback to original results
            return results[:top_k]


class CrossEncoderReranker(Reranker):
    """Re-ranking using cross-encoder models for semantic relevance."""
    
    def __init__(self, model_name: str = "cross-encoder/ms-marco-MiniLM-L-6-v2", 
                 batch_size: int = 32, **kwargs):
        super().__init__(**kwargs)
        self.model_name = model_name
        self.batch_size = batch_size
        self.model = None
    
    async def _load_model(self):
        """Lazy load the cross-encoder model."""
        if self.model is None:
            try:
                from sentence_transformers import CrossEncoder
                self.model = CrossEncoder(self.model_name)
                logger.info(f"Loaded cross-encoder model: {self.model_name}")
            except ImportError:
                logger.error("sentence-transformers not available for CrossEncoderReranker")
                raise RAGError("sentence-transformers required for CrossEncoderReranker")
            except Exception as e:
                logger.error(f"Failed to load cross-encoder model: {e}")
                raise RAGError(f"Model loading failed: {e}")
    
    async def rerank(self, query: str, results: List[RetrievalResult], 
                    top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Re-rank results using cross-encoder scoring."""
        if not results:
            return results
        
        top_k = top_k or self.top_k
        
        try:
            await self._load_model()
            
            # Prepare query-document pairs
            pairs = [(query, result.chunk.content) for result in results]
            
            # Compute cross-encoder scores in batches
            all_scores = []
            for i in range(0, len(pairs), self.batch_size):
                batch_pairs = pairs[i:i + self.batch_size]
                batch_scores = self.model.predict(batch_pairs)
                all_scores.extend(batch_scores)
            
            # Create new results with cross-encoder scores
            scored_results = []
            for result, score in zip(results, all_scores):
                new_result = RetrievalResult(
                    chunk=result.chunk,
                    score=float(score),
                    retrieval_method="cross_encoder_reranked",
                    metadata={
                        **result.metadata,
                        "original_score": result.score,
                        "original_method": result.retrieval_method,
                        "cross_encoder_model": self.model_name
                    }
                )
                scored_results.append(new_result)
            
            # Sort by cross-encoder score and return top-k
            scored_results.sort(key=lambda x: x.score, reverse=True)
            reranked_results = scored_results[:top_k]
            
            logger.debug(f"Cross-encoder re-ranked {len(results)} results to top {len(reranked_results)}")
            return reranked_results
            
        except Exception as e:
            logger.error(f"Cross-encoder re-ranking failed: {e}")
            # Fallback to original results
            return results[:top_k]


class LLMReranker(Reranker):
    """Re-ranking using LLM-based relevance scoring."""
    
    def __init__(self, llm_adapter=None, scoring_prompt: Optional[str] = None, **kwargs):
        super().__init__(**kwargs)
        self.llm_adapter = llm_adapter
        self.scoring_prompt = scoring_prompt or self._default_scoring_prompt()
    
    def _default_scoring_prompt(self) -> str:
        """Default prompt for LLM-based relevance scoring."""
        return """Rate the relevance of the following document to the query on a scale of 0-10, where 0 is completely irrelevant and 10 is perfectly relevant.

Query: {query}

Document: {document}

Provide only a numeric score (0-10) as your response."""
    
    async def _get_llm_adapter(self):
        """Get LLM adapter for scoring."""
        if self.llm_adapter is None:
            # Use default echo adapter as fallback
            from core.adapters.echo_adapter import EchoAdapter
            self.llm_adapter = EchoAdapter()
            logger.warning("Using EchoAdapter for LLM re-ranking (not functional)")
        return self.llm_adapter
    
    async def _score_relevance(self, query: str, document: str) -> float:
        """Score document relevance using LLM."""
        try:
            llm = await self._get_llm_adapter()
            prompt = self.scoring_prompt.format(query=query, document=document)
            
            from core.contracts.llm import ChatMessage, ChatCompletionRequest
            request = ChatCompletionRequest(
                messages=[ChatMessage(role="user", content=prompt)],
                temperature=0.0,
                max_tokens=10
            )
            
            response = await llm.chat(request)
            score_text = response.choices[0].message.content.strip()
            
            # Extract numeric score
            import re
            score_match = re.search(r'\b(\d+(?:\.\d+)?)\b', score_text)
            if score_match:
                score = float(score_match.group(1))
                return min(max(score, 0.0), 10.0) / 10.0  # Normalize to [0, 1]
            else:
                logger.warning(f"Could not extract score from LLM response: {score_text}")
                return 0.5  # Default neutral score
                
        except Exception as e:
            logger.error(f"LLM scoring failed: {e}")
            return 0.5  # Default neutral score
    
    async def rerank(self, query: str, results: List[RetrievalResult], 
                    top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Re-rank results using LLM-based relevance scoring."""
        if not results:
            return results
        
        top_k = top_k or self.top_k
        
        try:
            # Score all documents
            scoring_tasks = [
                self._score_relevance(query, result.chunk.content) 
                for result in results
            ]
            scores = await asyncio.gather(*scoring_tasks)
            
            # Create new results with LLM scores
            scored_results = []
            for result, score in zip(results, scores):
                new_result = RetrievalResult(
                    chunk=result.chunk,
                    score=score,
                    retrieval_method="llm_reranked",
                    metadata={
                        **result.metadata,
                        "original_score": result.score,
                        "original_method": result.retrieval_method,
                        "llm_model": getattr(self.llm_adapter, 'model_name', 'unknown')
                    }
                )
                scored_results.append(new_result)
            
            # Sort by LLM score and return top-k
            scored_results.sort(key=lambda x: x.score, reverse=True)
            reranked_results = scored_results[:top_k]
            
            logger.debug(f"LLM re-ranked {len(results)} results to top {len(reranked_results)}")
            return reranked_results
            
        except Exception as e:
            logger.error(f"LLM re-ranking failed: {e}")
            # Fallback to original results
            return results[:top_k]


class EnsembleReranker(Reranker):
    """Ensemble re-ranking combining multiple re-ranking strategies."""
    
    def __init__(self, rerankers: List[Tuple[Reranker, float]], **kwargs):
        """
        Initialize ensemble reranker.
        
        Args:
            rerankers: List of (reranker, weight) tuples
        """
        super().__init__(**kwargs)
        self.rerankers = rerankers
        
        # Normalize weights
        total_weight = sum(weight for _, weight in rerankers)
        if total_weight > 0:
            self.rerankers = [(reranker, weight / total_weight) 
                             for reranker, weight in rerankers]
    
    async def rerank(self, query: str, results: List[RetrievalResult], 
                    top_k: Optional[int] = None) -> List[RetrievalResult]:
        """Re-rank using ensemble of multiple rerankers."""
        if not results or not self.rerankers:
            return results[:top_k or self.top_k]
        
        top_k = top_k or self.top_k
        
        try:
            # Get re-ranking results from all rerankers
            reranking_tasks = [
                reranker.rerank(query, results, len(results))  # Get all results
                for reranker, _ in self.rerankers
            ]
            all_reranked_results = await asyncio.gather(*reranking_tasks)
            
            # Combine scores using weighted average
            chunk_scores: Dict[str, Tuple[float, RetrievalResult]] = {}
            
            for reranked_results, (_, weight) in zip(all_reranked_results, self.rerankers):
                for result in reranked_results:
                    chunk_id = result.chunk.id
                    weighted_score = result.score * weight
                    
                    if chunk_id in chunk_scores:
                        # Add to existing score
                        existing_score, existing_result = chunk_scores[chunk_id]
                        combined_score = existing_score + weighted_score
                        
                        # Merge metadata
                        combined_metadata = {
                            **existing_result.metadata,
                            **result.metadata,
                            "ensemble_score": combined_score
                        }
                        
                        new_result = RetrievalResult(
                            chunk=result.chunk,
                            score=combined_score,
                            retrieval_method="ensemble_reranked",
                            metadata=combined_metadata
                        )
                        chunk_scores[chunk_id] = (combined_score, new_result)
                    else:
                        # New chunk
                        metadata = {
                            **result.metadata,
                            "ensemble_score": weighted_score
                        }
                        new_result = RetrievalResult(
                            chunk=result.chunk,
                            score=weighted_score,
                            retrieval_method="ensemble_reranked",
                            metadata=metadata
                        )
                        chunk_scores[chunk_id] = (weighted_score, new_result)
            
            # Sort by combined score and return top-k
            sorted_results = sorted(chunk_scores.values(), key=lambda x: x[0], reverse=True)
            final_results = [result for _, result in sorted_results[:top_k]]
            
            logger.debug(f"Ensemble re-ranked {len(results)} results to top {len(final_results)}")
            return final_results
            
        except Exception as e:
            logger.error(f"Ensemble re-ranking failed: {e}")
            # Fallback to original results
            return results[:top_k]
