import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { 
  ChatbotConfig, 
  ChatbotBuilderState, 
  BuilderStep,
  KnowledgeBase,
  KnowledgeDocument,
  Conversation,
  Message,
  TestSuite,
  ChatbotDeployment,
  ChatbotAnalytics,
  ChatbotFormData,
  KnowledgeUploadData
} from '../types/chatbot';
import { v4 as uuidv4 } from 'uuid';

interface ChatbotStore extends ChatbotBuilderState {
  // Chatbot CRUD
  createChatbot: (data: ChatbotFormData) => Promise<ChatbotConfig>;
  updateChatbot: (id: string, data: Partial<ChatbotConfig>) => Promise<void>;
  deleteChatbot: (id: string) => Promise<void>;
  loadChatbot: (id: string) => Promise<void>;
  duplicateChatbot: (id: string) => Promise<ChatbotConfig>;
  
  // Knowledge Base Management
  uploadDocuments: (chatbotId: string, data: KnowledgeUploadData) => Promise<void>;
  deleteDocument: (chatbotId: string, documentId: string) => Promise<void>;
  reindexKnowledgeBase: (chatbotId: string) => Promise<void>;
  
  // Conversation Management
  loadConversations: (chatbotId: string) => Promise<void>;
  sendMessage: (chatbotId: string, conversationId: string, message: string) => Promise<Message>;
  createConversation: (chatbotId: string) => Promise<Conversation>;
  endConversation: (conversationId: string) => Promise<void>;
  
  // Testing
  createTestSuite: (chatbotId: string, name: string) => Promise<TestSuite>;
  runTests: (testSuiteId: string) => Promise<void>;
  addTestCase: (testSuiteId: string, input: string, expectedOutput: string) => Promise<void>;
  
  // Deployment
  deployBot: (chatbotId: string, environment: 'development' | 'staging' | 'production') => Promise<ChatbotDeployment>;
  updateDeployment: (deploymentId: string, config: Partial<ChatbotDeployment['config']>) => Promise<void>;
  
  // Analytics
  loadAnalytics: (chatbotId: string, period: string) => Promise<void>;
  
  // Builder Navigation
  setCurrentStep: (step: BuilderStep) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // State Management
  setCurrentChatbot: (chatbot: ChatbotConfig | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  
  // Reset
  resetBuilder: () => void;
}

const builderSteps: BuilderStep[] = [
  'basic-info',
  'personality',
  'knowledge-base',
  'llm-settings',
  'conversation-settings',
  'integrations',
  'testing',
  'deployment',
  'analytics'
];

const defaultLLMSettings = {
  provider: 'openai' as const,
  model: 'gpt-3.5-turbo',
  temperature: 0.7,
  maxTokens: 1000,
  topP: 1,
  frequencyPenalty: 0,
  presencePenalty: 0,
  systemPrompt: 'You are a helpful AI assistant.',
  contextWindow: 4000,
};

const defaultPersonality = {
  tone: 'friendly' as const,
  style: 'conversational' as const,
  language: 'en',
  greeting: 'Hello! How can I help you today?',
  fallbackMessage: "I'm sorry, I don't understand. Could you please rephrase your question?",
  examples: [],
};

const defaultConversationSettings = {
  maxHistory: 10,
  contextRetention: 'session' as const,
  enableMemory: true,
  memoryDuration: 30,
  enableFeedback: true,
  enableAnalytics: true,
  rateLimiting: {
    enabled: true,
    messagesPerMinute: 10,
    messagesPerHour: 100,
    messagesPerDay: 1000,
  },
  moderationSettings: {
    enabled: true,
    filterProfanity: true,
    filterSpam: true,
    customFilters: [],
    escalationRules: [],
  },
};

// Mock data for development
const mockChatbots: ChatbotConfig[] = [
  {
    id: '1',
    name: 'Customer Support Bot',
    description: 'Handles customer inquiries and support requests',
    personality: defaultPersonality,
    knowledgeBase: {
      id: 'kb-1',
      name: 'Support Knowledge Base',
      documents: [],
      sources: [],
      indexingStatus: 'completed',
      lastUpdated: new Date().toISOString(),
      totalDocuments: 5,
      totalChunks: 150,
    },
    llmSettings: defaultLLMSettings,
    conversationSettings: defaultConversationSettings,
    integrations: [],
    status: 'active',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    userId: 'user-1',
  },
  {
    id: '2',
    name: 'Sales Assistant',
    description: 'Helps with product information and sales inquiries',
    personality: { ...defaultPersonality, tone: 'professional' },
    knowledgeBase: {
      id: 'kb-2',
      name: 'Product Catalog',
      documents: [],
      sources: [],
      indexingStatus: 'processing',
      lastUpdated: new Date().toISOString(),
      totalDocuments: 12,
      totalChunks: 300,
    },
    llmSettings: { ...defaultLLMSettings, temperature: 0.5 },
    conversationSettings: defaultConversationSettings,
    integrations: [],
    status: 'draft',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
    updatedAt: new Date().toISOString(),
    userId: 'user-1',
  },
];

export const useChatbotStore = create<ChatbotStore>()(
  persist(
    (set, get) => ({
      // Initial state
      currentChatbot: null,
      chatbots: mockChatbots,
      knowledgeBases: [],
      conversations: [],
      analytics: null,
      testSuites: [],
      deployments: [],
      isLoading: false,
      error: null,
      currentStep: 'basic-info',

      // Chatbot CRUD
      createChatbot: async (data: ChatbotFormData) => {
        set({ isLoading: true, error: null });
        
        try {
          const newChatbot: ChatbotConfig = {
            id: uuidv4(),
            name: data.name,
            description: data.description,
            personality: { ...defaultPersonality, ...data.personality },
            knowledgeBase: {
              id: uuidv4(),
              name: `${data.name} Knowledge Base`,
              documents: [],
              sources: [],
              indexingStatus: 'pending',
              lastUpdated: new Date().toISOString(),
              totalDocuments: 0,
              totalChunks: 0,
            },
            llmSettings: { ...defaultLLMSettings, ...data.llmSettings },
            conversationSettings: { ...defaultConversationSettings, ...data.conversationSettings },
            integrations: [],
            status: 'draft',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            userId: 'current-user',
          };

          set((state) => ({
            chatbots: [...state.chatbots, newChatbot],
            currentChatbot: newChatbot,
            isLoading: false,
          }));

          return newChatbot;
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to create chatbot' 
          });
          throw error;
        }
      },

      updateChatbot: async (id: string, data: Partial<ChatbotConfig>) => {
        set({ isLoading: true, error: null });
        
        try {
          const updatedData = {
            ...data,
            updatedAt: new Date().toISOString(),
          };

          set((state) => ({
            chatbots: state.chatbots.map((bot) =>
              bot.id === id ? { ...bot, ...updatedData } : bot
            ),
            currentChatbot: state.currentChatbot?.id === id 
              ? { ...state.currentChatbot, ...updatedData }
              : state.currentChatbot,
            isLoading: false,
          }));
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to update chatbot' 
          });
          throw error;
        }
      },

      deleteChatbot: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          set((state) => ({
            chatbots: state.chatbots.filter((bot) => bot.id !== id),
            currentChatbot: state.currentChatbot?.id === id ? null : state.currentChatbot,
            isLoading: false,
          }));
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to delete chatbot' 
          });
          throw error;
        }
      },

      loadChatbot: async (id: string) => {
        set({ isLoading: true, error: null });
        
        try {
          const chatbot = get().chatbots.find((bot) => bot.id === id);
          if (!chatbot) {
            throw new Error('Chatbot not found');
          }

          set({ currentChatbot: chatbot, isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to load chatbot' 
          });
          throw error;
        }
      },

      duplicateChatbot: async (id: string) => {
        const original = get().chatbots.find((bot) => bot.id === id);
        if (!original) {
          throw new Error('Chatbot not found');
        }

        const duplicate: ChatbotConfig = {
          ...original,
          id: uuidv4(),
          name: `${original.name} (Copy)`,
          status: 'draft',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          knowledgeBase: {
            ...original.knowledgeBase,
            id: uuidv4(),
            name: `${original.knowledgeBase.name} (Copy)`,
          },
        };

        set((state) => ({
          chatbots: [...state.chatbots, duplicate],
        }));

        return duplicate;
      },

      // Knowledge Base Management
      uploadDocuments: async (chatbotId: string, data: KnowledgeUploadData) => {
        set({ isLoading: true, error: null });
        
        try {
          // Mock document upload
          const newDocuments: KnowledgeDocument[] = data.files.map((file) => ({
            id: uuidv4(),
            name: file.name,
            type: file.name.split('.').pop() as any || 'txt',
            size: file.size,
            uploadedAt: new Date().toISOString(),
            status: 'processing',
            chunks: 0,
            metadata: {},
          }));

          // Simulate processing
          setTimeout(() => {
            set((state) => ({
              chatbots: state.chatbots.map((bot) =>
                bot.id === chatbotId
                  ? {
                      ...bot,
                      knowledgeBase: {
                        ...bot.knowledgeBase,
                        documents: [...bot.knowledgeBase.documents, ...newDocuments.map(doc => ({ ...doc, status: 'indexed' as const, chunks: Math.floor(Math.random() * 50) + 10 }))],
                        totalDocuments: bot.knowledgeBase.totalDocuments + newDocuments.length,
                        totalChunks: bot.knowledgeBase.totalChunks + newDocuments.reduce((sum, doc) => sum + Math.floor(Math.random() * 50) + 10, 0),
                        lastUpdated: new Date().toISOString(),
                        indexingStatus: 'completed' as const,
                      },
                    }
                  : bot
              ),
            }));
          }, 2000);

          set({ isLoading: false });
        } catch (error) {
          set({ 
            isLoading: false, 
            error: error instanceof Error ? error.message : 'Failed to upload documents' 
          });
          throw error;
        }
      },

      deleteDocument: async (chatbotId: string, documentId: string) => {
        set((state) => ({
          chatbots: state.chatbots.map((bot) =>
            bot.id === chatbotId
              ? {
                  ...bot,
                  knowledgeBase: {
                    ...bot.knowledgeBase,
                    documents: bot.knowledgeBase.documents.filter(doc => doc.id !== documentId),
                    totalDocuments: bot.knowledgeBase.totalDocuments - 1,
                  },
                }
              : bot
          ),
        }));
      },

      reindexKnowledgeBase: async (chatbotId: string) => {
        set((state) => ({
          chatbots: state.chatbots.map((bot) =>
            bot.id === chatbotId
              ? {
                  ...bot,
                  knowledgeBase: {
                    ...bot.knowledgeBase,
                    indexingStatus: 'processing',
                  },
                }
              : bot
          ),
        }));

        // Simulate reindexing
        setTimeout(() => {
          set((state) => ({
            chatbots: state.chatbots.map((bot) =>
              bot.id === chatbotId
                ? {
                    ...bot,
                    knowledgeBase: {
                      ...bot.knowledgeBase,
                      indexingStatus: 'completed',
                      lastUpdated: new Date().toISOString(),
                    },
                  }
                : bot
            ),
          }));
        }, 3000);
      },

      // Conversation Management
      loadConversations: async (chatbotId: string) => {
        // Mock conversations would be loaded here
        set({ conversations: [] });
      },

      sendMessage: async (chatbotId: string, conversationId: string, message: string) => {
        // Mock message sending
        const newMessage: Message = {
          id: uuidv4(),
          conversationId,
          type: 'bot',
          content: `This is a mock response to: "${message}"`,
          timestamp: new Date().toISOString(),
          metadata: {
            confidence: 0.85,
            sources: ['Knowledge Base'],
            processingTime: 1200,
            tokens: 50,
          },
        };

        return newMessage;
      },

      createConversation: async (chatbotId: string) => {
        const newConversation: Conversation = {
          id: uuidv4(),
          chatbotId,
          sessionId: uuidv4(),
          messages: [],
          metadata: {
            integration: 'web',
          },
          status: 'active',
          startedAt: new Date().toISOString(),
        };

        set((state) => ({
          conversations: [...state.conversations, newConversation],
        }));

        return newConversation;
      },

      endConversation: async (conversationId: string) => {
        set((state) => ({
          conversations: state.conversations.map((conv) =>
            conv.id === conversationId
              ? { ...conv, status: 'ended' as const, endedAt: new Date().toISOString() }
              : conv
          ),
        }));
      },

      // Testing
      createTestSuite: async (chatbotId: string, name: string) => {
        const newTestSuite: TestSuite = {
          id: uuidv4(),
          chatbotId,
          name,
          description: '',
          testCases: [],
          status: 'draft',
          createdAt: new Date().toISOString(),
        };

        set((state) => ({
          testSuites: [...state.testSuites, newTestSuite],
        }));

        return newTestSuite;
      },

      runTests: async (testSuiteId: string) => {
        // Mock test execution
        set((state) => ({
          testSuites: state.testSuites.map((suite) =>
            suite.id === testSuiteId
              ? { ...suite, status: 'running' }
              : suite
          ),
        }));

        setTimeout(() => {
          set((state) => ({
            testSuites: state.testSuites.map((suite) =>
              suite.id === testSuiteId
                ? {
                    ...suite,
                    status: 'completed',
                    lastRun: new Date().toISOString(),
                    results: {
                      totalTests: suite.testCases.length,
                      passedTests: Math.floor(suite.testCases.length * 0.8),
                      failedTests: Math.ceil(suite.testCases.length * 0.2),
                      averageConfidence: 0.82,
                      executionTime: 5000,
                      passRate: 0.8,
                    },
                  }
                : suite
            ),
          }));
        }, 3000);
      },

      addTestCase: async (testSuiteId: string, input: string, expectedOutput: string) => {
        set((state) => ({
          testSuites: state.testSuites.map((suite) =>
            suite.id === testSuiteId
              ? {
                  ...suite,
                  testCases: [
                    ...suite.testCases,
                    {
                      id: uuidv4(),
                      name: `Test Case ${suite.testCases.length + 1}`,
                      description: '',
                      input,
                      expectedOutput,
                    },
                  ],
                }
              : suite
          ),
        }));
      },

      // Deployment
      deployBot: async (chatbotId: string, environment) => {
        const deployment: ChatbotDeployment = {
          id: uuidv4(),
          chatbotId,
          environment,
          version: '1.0.0',
          status: 'deploying',
          deployedAt: new Date().toISOString(),
          config: {
            scaling: {
              minInstances: 1,
              maxInstances: 10,
              targetCPU: 70,
            },
            security: {
              enableCORS: true,
              allowedOrigins: ['*'],
              enableRateLimit: true,
              enableAuth: false,
            },
            monitoring: {
              enableLogs: true,
              enableMetrics: true,
              enableAlerts: true,
            },
          },
        };

        set((state) => ({
          deployments: [...state.deployments, deployment],
        }));

        // Simulate deployment
        setTimeout(() => {
          set((state) => ({
            deployments: state.deployments.map((dep) =>
              dep.id === deployment.id
                ? {
                    ...dep,
                    status: 'active',
                    url: `https://api.mgaif.com/chatbot/${chatbotId}`,
                    apiKey: `mgaif_${uuidv4().replace(/-/g, '')}`,
                  }
                : dep
            ),
          }));
        }, 5000);

        return deployment;
      },

      updateDeployment: async (deploymentId: string, config) => {
        set((state) => ({
          deployments: state.deployments.map((dep) =>
            dep.id === deploymentId
              ? { ...dep, config: { ...dep.config, ...config } }
              : dep
          ),
        }));
      },

      // Analytics
      loadAnalytics: async (chatbotId: string, period: string) => {
        // Mock analytics data
        const mockAnalytics: ChatbotAnalytics = {
          chatbotId,
          period: period as any,
          startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          endDate: new Date().toISOString(),
          metrics: {
            totalConversations: 1250,
            totalMessages: 8500,
            averageConversationLength: 6.8,
            averageResponseTime: 1.2,
            resolutionRate: 0.85,
            escalationRate: 0.12,
            userSatisfactionScore: 4.2,
            activeUsers: 890,
            returningUsers: 340,
          },
          conversations: [],
          topQuestions: [
            { question: 'How do I reset my password?', count: 145, averageConfidence: 0.92, satisfactionScore: 4.1 },
            { question: 'What are your business hours?', count: 98, averageConfidence: 0.95, satisfactionScore: 4.5 },
            { question: 'How can I contact support?', count: 87, averageConfidence: 0.88, satisfactionScore: 3.9 },
          ],
          userSatisfaction: {
            totalRatings: 456,
            averageRating: 4.2,
            ratingDistribution: { 1: 12, 2: 23, 3: 67, 4: 189, 5: 165 },
            positivePercentage: 0.78,
            negativePercentage: 0.08,
          },
        };

        set({ analytics: mockAnalytics });
      },

      // Builder Navigation
      setCurrentStep: (step: BuilderStep) => {
        set({ currentStep: step });
      },

      nextStep: () => {
        const { currentStep } = get();
        const currentIndex = builderSteps.indexOf(currentStep);
        if (currentIndex < builderSteps.length - 1) {
          set({ currentStep: builderSteps[currentIndex + 1] });
        }
      },

      previousStep: () => {
        const { currentStep } = get();
        const currentIndex = builderSteps.indexOf(currentStep);
        if (currentIndex > 0) {
          set({ currentStep: builderSteps[currentIndex - 1] });
        }
      },

      // State Management
      setCurrentChatbot: (chatbot: ChatbotConfig | null) => {
        set({ currentChatbot: chatbot });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      setError: (error: string | null) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      // Reset
      resetBuilder: () => {
        set({
          currentChatbot: null,
          currentStep: 'basic-info',
          error: null,
          isLoading: false,
        });
      },
    }),
    {
      name: 'chatbot-storage',
      partialize: (state) => ({
        chatbots: state.chatbots,
        currentChatbot: state.currentChatbot,
      }),
    }
  )
);
